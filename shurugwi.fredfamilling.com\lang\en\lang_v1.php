<?php

return [
    'enable_editing_product_from_purchase' => 'Enable editing product price from purchase screen',
    'sales_commission_agent' => 'Sales Commission Agent',
    'sales_commission_agents' => 'Sales Commission Agents',
    'disable' => 'Disable',
    'logged_in_user' => 'Logged in user',
    'select_from_users_list' => "Select from user's list",
    'select_from_commisssion_agents_list' => "Select from commission agent's list",
    'add_sales_commission_agent' => 'Add sales commission agent',
    'commission_agent_added_success' => 'Commission agent added successfully',
    'edit_sales_commission_agent' => 'Edit sales commission agent',
    'commission_agent_updated_success' => 'Commission agent updated successfully',
    'commission_agent_deleted_success' => 'Commission agent deleted successfully',
    'contact_no' => 'Contact No.',
    'commission_agent' => 'Commission Agent',
    'cmmsn_percent' => 'Sales Commission Percentage (%)',
    'sales_added' => 'Sales Added',
    'sales_with_commission' => 'Sales With Commission',
    // 'total_sale_with_commission' => 'Total Sale With Commission',
    'total_sale_commission' => 'Total Sale Commission',
    'sales_item_addition_method' => 'Sales Item Addition Method',
    'add_item_in_new_row' => 'Add item in new row',
    'increase_item_qty' => 'Increase item quantity if it already exists',
    'stock_transfers' => 'Stock Transfers',
    'all_stock_transfers' => 'All Stock Transfers',
    'add_stock_transfer' => 'Add Stock Transfer',
    'list_stock_transfers' => 'List Stock Transfers',
    'location_from' => 'Location (From)',
    'location_to' => 'Location (To)',
    'shipping_charges' => 'Shipping Charges',
    'stock_transfer_added_successfully' => 'Stock transfer added successfully',
    'stock_transfer_delete_success' => 'Stock transfer deleted successfully',
    'stock_transfer_cannot_be_deleted' => "This stock transfer can't be deleted as some of the stocks are already sold",
    'total_transfer_shipping_charges' => 'Total Transfer Shipping Charges',
    'enable_inline_tax' => 'Enable inline tax in purchase and sell',
    'save_n_add_opening_stock' => 'Save & Add Opening Stock',
    'add_opening_stock' => 'Add Opening Stock',
    'opening_stock_added_successfully' => 'Opening stock added successfully',
    'update_n_edit_opening_stock' => 'Update & Edit Opening Stock',
    'total_amount_exc_tax' => 'Total Amount (Exc. Tax)',
    'currency_symbol_placement' => 'Currency Symbol Placement',
    'before_amount' => 'Before amount',
    'after_amount' => 'After amount',
    'barcode_label_error' => 'Unsupported SKU id for the selected barcode type',
    'list_products' => 'List Products',
    'list_expenses' => 'List Expenses',
    'add_expiry' => 'Add item expiry',
    'add_manufacturing_auto_expiry' => 'Add manufacturing date & expiry period',
    'tooltip_enable_expiry' => 'Enable product expiry. <br/> <br/><b>Add item expiry</b>: To directly add item expiry only. <br/> <b>Add manufacturing date & expiry period</b>: To add manufacturing date & expiry period and calculate expiry date based on that.',
    'quantity' => 'Quantity',
    'on_product_expiry' => 'On Product Expiry',
    'keep_selling' => 'Keep Selling',
    'stop_selling' => 'Stop Selling n days before',
    'tooltip_on_product_expiry' => 'Specify action that needs to be done on product expiry. <br><br> <b>Keep Selling</b>: Products will be kept on selling after expiry also. <br> <b>Stop Selling</b>: Stop selling item n days before expiry.',
    'output_tax_minus_input_tax' => 'Output Tax - Input Tax - Expense Tax',
    'total_purchase_discount' => 'Total Purchase discount',
    'total_sell_discount' => 'Total Sell discount',
    'updated_succesfully' => 'Updated Successfully',
    'default_unit' => 'Default Unit',
    'enable_brand' => 'Enable Brands',
    'enable_category' => 'Enable Categories',
    'enable_sub_category' => 'Enable Sub-Categories',
    'enable_price_tax' => 'Enable Price & Tax info',
    'enable_purchase_status' => 'Enable Purchase Status',
    'tooltip_enable_purchase_status' => 'On disable all purchases will be marked as <i>Item Received</i>',
    'recent_product_quantity' => 'Go to product quantity',
    'full_screen' => 'Press F11 to go Full Screen',
    'Uncategorised' => 'Uncategorised',
    'no_products_to_display' => 'No Products to display',
    'item_out_of_stock' => 'Product out of stock',
    'go_back' => 'Go Back',
    'disable_pay_checkout' => 'Disable Multiple Pay',
    'disable_draft' => 'Disable Draft',
    'disable_express_checkout' => 'Disable Express Checkout',
    'hide_product_suggestion' => 'Don\'t show product suggestion',
    'hide_recent_trans' => 'Don\'t show recent transactions',
    'pos_settings' => 'POS settings',
    'disable_discount' => 'Disable Discount',
    'disable_order_tax' => 'Disable order tax',
    'customer_groups' => 'Customer Groups',
    'customer_group' => 'Customer Group',
    'all_your_customer_groups' => 'All Customer Groups',
    'add_customer_group' => 'Add Customer Group',
    'customer_group_name' => 'Customer Group Name',
    'calculation_percentage' => 'Calculation Percentage (%)',
    'tooltip_calculation_percentage' => '<b>Selling price = Selling price Set For the product + Calculation percentage </b> <br/><br/> You can specify percentage as positive to increase and negtive to decrease selling price.',
    'success' => 'Success',
    'edit_customer_group' => 'Edit Customer Group',
    'customer_groups_report' => 'Customer Groups Report',
    'none' => 'None',
    'enable_imei_or_sr_no' => 'Enable Product description, IMEI or Serial Number',
    'tooltip_sr_no' => 'Enable or disable adding product description, IMEI or Serial number while selling products in POS screen',
    'description' => 'Description',
    'sell_line_description_help' => 'Add product IMEI, Serial number or other informations here.',
    'unit_cost_before_discount' => 'Unit Cost (Before Discount)',
    'discount_percent' => 'Discount Percent %',
    'profit_margin' => 'Profit Margin %',
    'discount_percent' => 'Discount Percent',
    'application_tour' => 'Application Tour',
    'profile' => 'Profile',
    'sign_out' => 'Sign Out',
    'my_profile' => 'My Profile',
    'Admin' => 'Admin',
    'Cashier' => 'Cashier',
    'both_supplier_customer' => 'Both (Supplier & Customer)',
    'months' => 'Months',
    'days' => 'Days',
    'suppliers' => 'Suppliers',
    'customers' => 'Customers',
    'enter_product_name_to_print_labels' => 'Enter products name to print labels',
    'manage_product_variations' => 'Manage product variations',
    'all_variations' => 'All variations',
    'values' => 'Values',
    'add_variation' => 'Add Variation',
    'variation_name' => 'Variation Name',
    'add_variation_values' => 'Add variation values',
    'edit_variation' => 'Edit Variation',
    'received' => 'Received',
    'pending' => 'Pending',
    'ordered' => 'Ordered',
    'search_product_placeholder' => 'Enter Product name / SKU / Scan bar code',
    'fixed' => 'Fixed',
    'percentage' => 'Percentage',
    'select_location' => 'Select location',
    'add_description' => 'Add Description',
    'cash' => 'Cash',
    'card' => 'Card',
    'cheque' => 'Cheque',
    'bank_transfer' => 'Bank Transfer',
    'other' => 'Other',
    'payment' => 'Payment',
    'total_items' => 'Total Items',
    'total_paying' => 'Total Paying',
    'balance' => 'Balance',
    'payment_method' => 'Payment Method',
    'card_no' => 'Card Number',
    'card_holder_name' => 'Card Holder Name',
    'card_transaction_no' => 'Card Transaction Number',
    'card_type' => 'Card Type',
    'month' => 'Month',
    'year' => 'Year',
    'security_code' => 'Security Code',
    'cheque_no' => 'Cheque Number',
    'bank_account_number' => 'Bank Account No',
    'paid' => 'Paid',
    'due' => 'Due',
    'partial' => 'Partial',
    'no_of_products' => 'Number of products',
    'select_a_date_range' => 'Select a date range',
    'shortcut_help' => "Shortcut should be the names of the keys separated by '+'",
    'example' => 'Example',
    'available_key_names_are' => 'Available key names are',
    'add_new_product' => 'Add new product',
    'invoice_logo_help' => 'Max :max_size, jpeg,gif,png formats only.',
    'invoice_logo_help2' => 'Upload only if you want to replace previous logo',
    'printer_name_help' => 'Short Descriptive Name to recognize printer',
    'char_per_line_help' => 'Number of character that can be printed per line',
    'ip_address_help' => 'IP address for connecting to the printer',
    'port_help' => 'Most printer works on port 9100',
    'combination_of_taxes' => 'Combination of multiple taxes',
    'product_purchase_report' => 'Product Purchase Report',
    'unit_perchase_price' => 'Unit Purchase Price',
    'search_product' => 'Search Product',
    'product_sell_report' => 'Product Sell Report',
    'unit_sale_price' => 'Unit Sale Price',
    'contact_id' => 'Contact ID',
    'category_code_help' => 'Category code is same as <b>HSN code</b>',
    'sub_heading_line' => 'Sub Heading Line :_number_',
    'date_label' => 'Date Label',
    'client_id_label' => 'Client ID Label',
    'product_label' => 'Product Label',
    'qty_label' => 'Quantity Label',
    'unit_price_label' => 'Unit Price Label',
    'subtotal_label' => 'Subtotal Label',
    'product_details_to_be_shown' => 'Product details to be shown',
    'show_brand' => 'Show brand',
    'show_sku' => 'Show SKU',
    'show_cat_code' => 'Show category code or HSN code',
    'show_client_id' => 'Show client ID',
    'show_time_with_date' => 'Show time with date',
    'show_sale_description' => 'Show sale description',
    'product_imei_or_sn' => '(Product IMEI or Serial Number)',
    'purchase_already_exist' => "Product can't be deleted because purchases related to 
								this product exist",
    'opening_stock_sold' => "Product can't be deleted because some stocks are sold",
    'stock_adjusted' => "Product can't be deleted because some stocks are adjusted",
    'product_delete_success' => 'Product deleted successfully',
    'manage_products' => 'Manage your products',
    'all_products' => 'All Products',
    'login' => 'Login',
    'register' => 'Register',
    'username' => 'Username',
    'password' => 'Password',
    'remember_me' => 'Remember Me',
    'forgot_your_password' => 'Forgot Your Password?',
    'reset_password' => 'Reset Password',
    'email_address' => 'E-Mail Address',
    'send_password_reset_link' => 'Send Password Reset Link',
    'instructions' => 'Instructions',
    'instruction_line1' => 'Follow the instructions carefully before importing the file.',
    'instruction_line2' => 'The columns of the file should be in the following order.',
    'col_no' => 'Column Number',
    'col_name' => 'Column Name',
    'instruction' => 'Instruction',
    'required' => 'Required',
    'optional' => 'Optional',
    'name_ins' => 'Name of the product',
    'brand_ins' => 'Name of the brand',
    'brand_ins2' => 'If not found new brand with the given name will be created',
    'unit_ins' => 'Name of the unit',
    'category_ins' => 'Name of the Category',
    'category_ins2' => 'If not found new category with the given name will be created',
    'sub_category_ins' => 'Name of the Sub-Category',
    'sub_category_ins2' => 'If not found new sub-category with the given name under the <br> parent Category will be created',
    'sku_ins' => 'Product SKU. If blank an SKU will be automatically generated',
    'barcode_type_ins' => 'Barcode Type for the product.',
    'barcode_type_ins2' => 'Currently supported',
    'default' => 'Default',
    'manage_stock_ins' => 'Enable or disable stock managemant',
    'alert_qty_ins' => 'Required if Manage Stock is 1',
    'expires_in_ins' => 'Product expiry period (Only in numbers)',
    'expire_period_unit' => 'Expiry Period Unit',
    'available_options' => 'Available Options',
    'expire_period_unit_ins' => 'Unit for the expiry period',
    'applicable_tax_ins' => 'Name of the Tax Rate',
    'variation_name_ins' => 'Required if product type is variable',
    'variation_name_ins2' => 'Name of the variation (Ex: Size, Color etc )',
    'variation_values_ins' => 'Required if product type is variable',
    'variation_values_ins2' => "Values for the variation separated with '|' <br>
                            (Ex: Red|Blue|Green)",
    'purchase_price_inc_tax' => 'Purchase Price (Including Tax)',
    'purchase_price_inc_tax_ins1' => 'Required if Purchase Price Excluding Tax is not given',
    'purchase_price_inc_tax_ins2' => "Purchase Price (Including Tax) (Only in numbers)<br><br>For variable products '|' separated values with <br>the same order as Variation Values <br>(Ex: 84|85|88)",
    'purchase_price_exc_tax' => 'Purchase Price (Excluding Tax)',
    'purchase_price_exc_tax_ins1' => 'Required if Purchase Price Including Tax is not given',
    'purchase_price_exc_tax_ins2' => "Purchase Price (Excluding Tax) (Only in numbers)<br><br>For variable products '|' separated values with <br>the same order as Variation Values <br>(Ex: 84|85|88)",
    'profit_margin_ins' => 'Profit Margin (Only in numbers)',
    'profit_margin_ins1' => 'If blank default profit margin for the <br> business will be used',
    'selling_price' => 'Selling Price',
    'selling_price_ins' => 'Selling Price (Only in numbers)',
    'selling_price_ins1' => 'If blank selling price will be calculated <br>with the given Purchase Price <br>and Applicable Tax ',
    'opening_stock' => 'Opening Stock',
    'opening_stock_ins' => 'Opening Stock (Only in numbers)',
    'only_applicable_to_single_product' => 'Only applicable to single product',
    'purchase_delete_success' => 'Purchase deleted successfully',
    'location_ins' => 'If blank first business location will be used',
    'location_ins1' => 'Name of the business location',
    'expiry_date' => 'Expiry Date',
    'expiry_date_ins' => 'Stock Expiry Date <br><b>Format: mm-dd-yyyy; Ex: 11-25-2018</b>',
    'enable_lot_number' => 'Enable Lot number',
    'tooltip_enable_lot_number' => 'This will enable you to enter Lot number for each purchase line in purchase screen',
    'lot_number' => 'Lot Number',
    'enable_racks' => 'Enable Racks',
    'tooltip_enable_racks' => 'Enable this to add rack details of a product for different business locations while adding products',
    'rack_details' => 'Rack/Row/Position Details',
    'tooltip_rack_details' => 'Enter details of where the product is kept in the store, for different business locations.',
    'updated_success' => 'Updated Successfully',
    'added_success' => 'Added Successfully',
    'deleted_success' => 'Deleted Successfully',
    'enable_disable_modules' => 'Enable/Disable Modules',
    'opening_stock_help_text' => "<br><br>For variable products separate stock quantities with '|' <br>(Ex: 100|150|200)",
    'applicable_tax_help' => '<br><br/>If purchase Price (Excluding Tax) is not same as <br/>Purchase Price (Including Tax) <br/>then you must supply the tax rate name.',
    'sale_delete_success' => 'Sale deleted successfully',
    'click_to_edit' => 'Click to edit',
    'click_to_delete' => 'Click to delete',
    'quotation_heading' => 'Quotation Heading',
    'quotation' => 'Quotation',
    'quotation_no_prefix' => 'Quotation no. label',
    'tooltip_quotation_heading' => 'Quotation or Estimates heading is used while providing quotation to customers.',
    'quotation_added' => 'Quotation added successfully',
    'quotation_updated' => 'Quotation updated successfully',
    'product_sold_details_register' => 'Details of products sold',
    'grand_total' => 'Grand Total',
    'change_return' => 'Change Return',
    'date_format' => 'Date Format',
    'time_format' => 'Time Format',
    '12_hour' => '12 Hour',
    '24_hour' => '24 Hour',
    'list_quotations' => 'List quotations',
    'list_drafts' => 'List Drafts',
    'you_cannot_delete_this_contact' => 'Cannot delete the contact - Transactions already exist for the contact.',
    'enable_row' => 'Enable Row',
    'enable_position' => 'Enable Position',
    'rack' => 'Rack',
    'row' => 'Row',
    'position' => 'Position',
    'weight' => 'Weight',
    'rack_help_text' => "Rack details seperated by '|' for different business locations serially.<br/> (Ex: R1|R5|R12) ",
    'row_help_text' => "Row details seperated by '|' for different business locations serially.<br/> (Ex: ROW1|ROW2|ROW3) ",
    'position_help_text' => "Position details seperated by '|' for different business locations serially.<br/> (Ex: POS1|POS2|POS3) ",
    'import_opening_stock' => 'Import Opening Stock',
    'tooltip_import_opening_stock' => 'This feature is used to import opening stock of already added products. If the products are not added in the system then it is advisable to use import products for adding product details with opening stock.',
    'design' => 'Design',
    'express_checkout_cash' => 'Cash',
    'express_checkout_card' => 'Card',
    'checkout_multi_pay' => 'Multiple Pay',
    'tooltip_checkout_multi_pay' => 'Checkout using multiple payment methods',
    'tooltip_express_checkout_card' => 'Express checkout using card',
    'card_transaction_details' => 'Card transaction details',
    'client_tax_label' => 'Client tax number label',
    'cat_code_label' => 'Category or HSN code label',
    'list_sell_return' => 'List Sell Return',
    'sell_return' => 'Sell Return',
    'layout_credit_note' => 'Credit Note / Sell Return Details',
    'cn_heading' => 'Heading',
    'cn_no_label' => 'Reference Number',
    'cn_amount_label' => 'Total Amount',
    'custom_field' => 'Custom Field :number',
    'website' => 'Website',
    'total_credit_amt' => 'Total Credit Amount',
    'unit_sell_price' => 'Unit Sell Price',
    'prefixes' => 'Prefixes',
    'purchase_order' => 'Purchase Order',
    'stock_transfer' => 'Stock Transfer',
    'purchase_payment' => 'Purchase Payment',
    'sell_payment' => 'Sell Payment',
    'location_id' => 'Location ID',
    'add_edit_opening_stock' => 'Add or edit opening stock',
    'expiry_date_will_be_changed_in_pl' => 'Expiry date of the purchase line will be changed',
    'remove_from_stock' => 'Remove from stock',
    'stock_removed_successfully' => 'Stock removed successfully',
    'product_image' => 'Product image',
    'aspect_ratio_should_be_1_1' => 'Aspect ratio should be 1:1',
    'previous_image_will_be_replaced' => 'Previously uploaded image will be replaced',
    'all_category' => 'All Categories',
    'all_brands' => 'All Brands',
    'backup' => 'Administer Backup',
    'download_complete_backup' => 'Download complete backup',
    'backup_doesnt_exist' => "Backup Doesn't exist",
    'lot_report' => 'Lot Report',
    'purchase_payment_report' => 'Purchase Payment Report',
    'paid_on' => 'Paid on',
    'purchase' => 'Purchase',
    'cheque_no' => 'Cheque No.',
    'card_transaction_no' => 'Card Transaction No.',
    'bank_account_no' => 'Bank Account No.',
    'sell_payment_report' => 'Sell Payment Report',
    'restaurant' => 'Restaurant',
    'user_type' => 'User type',
    'business_inactive' => 'Sorry, your business is inactive!!',
    'product_custom_field1' => 'Custom Field1',
    'product_custom_field2' => 'Custom Field2',
    'product_custom_field3' => 'Custom Field3',
    'product_custom_field4' => 'Custom Field4',
    'image' => 'Image',
    'image_help_text' => 'Image name with extension.<br/> (Image name must be uploaded to the server :path )',
    'tooltip_kitchen' => 'This is the kitchen screen. Here order details can be viewed and orders can be marked as cooked.',
    'tooltip_serviceorder' => 'This is the service Staff screen. Service Staff can use this screen to view all orders for them and mark order as served.',
    'sales_person_label' => 'Sales Person Label',
    'show_sales_person' => 'Show Sales Person',
    'decimal_value_not_allowed' => 'Decimal value not allowed',
    'theme_color' => 'Theme Color',
    'quantity_error_msg_in_lot' => 'Only :qty :unit available in the selected Lot',
    'show_product_expiry' => 'Show product expiry',
    'show_lot_number' => 'Show lot number',
    'expiry' => 'Expiry',
    'lot' => 'Lot',
    'lot_n_expiry' => 'Lot & Expiry',
    'pos_edit_product_price_help' => 'Edit product Unit Price and Tax',
    'name' => 'Name',
    'payment_type' => 'Payment Type',
    'manage_payment_account' => 'Manage Your Payment Account',
    'all_payments' => 'All Payments',
    'payment_note' => 'Payment Note',
    'payment_account_deleted_success' => 'Payment account deleted successfully',
    'payment_account_updated_success' => 'Payment account updated successfully',
    'payment_gateway' => 'Online Payment Gateway',
    'payment_account_success' => 'Payment account added successfully',
    'payment_account' => 'Payment Account',
    'tooltip_sell_product_column' => 'Click <i>product name</i> to edit price, discount & tax. <br/>Click <i>Comment Icon</i> to enter serial number / IMEI or additional note.<br/><br/>Click <i>Modifier Icon</i>(if enabled) for modifiers',
    'credit_limit' => 'Credit Limit',
    'credit_limit_help' => 'Keep blank for no limit',
    'cutomer_credit_limit_exeeded' => 'Customer Credit Limit Exceeded <br>:credit_limit',
    'custom_payment_1' => 'Custom Payment 1',
    'custom_payment_2' => 'Custom Payment 2',
    'custom_payment_3' => 'Custom Payment 3',
    'transaction_no' => 'Transaction No.',
    'file' => 'File',
    'size' => 'Size',
    'date' => 'Date',
    'age' => 'Age',
    'create_new_backup' => 'Create New Backup',
    'all_sales' => 'All Sales',
    'opening_balance' => 'Opening Balance',
    'opening_balance_due' => 'Opening Balance Due',
    'import_contacts' => 'Import Contacts',
    'contact_type_ins' => '<strong>Available Options: customer, supplier & both</strong>',
    'contact_id_ins' => 'Leave blank to auto generate Contact ID',
    'required_if_supplier' => 'Required if contact type is supplier or both',
    'pay_term_period_ins' => 'Available Options: days and months',
    'your_username_will_be' => 'Your username will be',
    'currency_exchange_rate' => 'Currency Exchange Rate',
    'select_all' => 'Select all',
    'deselect_all' => 'Deselect all',
    'duplicate_product' => 'Duplicate Product',
    'delete_selected' => 'Delete Selected',
    'no_row_selected' => 'No row selected',
    'duplicate_sell' => 'Duplicate Sell',
    'duplicate_sell_created_successfully' => 'Duplicate Sell created successfully',
    'modules' => 'Modules',
    'theme' => 'Theme',
    'account' => 'Account',
    'expense_payment' => 'Expense Payment',
    'expense' => 'Expense',
    'disabled_in_demo' => 'Feature disabled in demo!!',
    'selling_price_group' => 'Selling Price Group',
    'all_selling_price_group' => 'All Selling Price Group',
    'add_selling_price_group' => 'Add Selling Price Group',
    'edit_selling_price_group' => 'Edit Selling Price Group',
    'access_selling_price_groups' => 'Access selling price groups',
    'save_n_add_selling_price_group_prices' => 'Save & Add Selling-Price-Group Prices',
    'add_selling_price_group_prices' => 'Add or edit Group Prices',
    'default_selling_price_inc_tax' => 'Default Selling Price (Inc. Tax)',
    'variation' => 'Variation',
    'price_group' => 'Price Group',
    'group_price' => 'Group Price',
    'default_selling_price' => 'Default Selling Price',
    'price_group_help_text' => 'Selling Price Group in which you want to sell',
    'group_prices' => 'Group Prices',
    'view_group_prices' => 'View group prices',
    'save_n_add_another' => 'Save And Add Another',
    'update_n_add_another' => 'Update And Add Another',
    'subtotal_editable' => 'Subtotal Editable',
    'subtotal_editable_help_text' => 'Check this to make Subtotal field editable for each product in POS screen',
    'notification_templates' => 'Notification Templates',
    'new_sale' => 'New Sale',
    'payment_reminder' => 'Payment Remider',
    'payment_received' => 'Payment Received',
    'new_booking' => 'New Booking',
    'new_order' => 'New Order',
    'payment_paid' => 'Payment Paid',
    'items_received' => 'Items Received',
    'items_pending' => 'Items Pending',
    'customer_notifications' => 'Customer Notifications',
    'supplier_notifications' => 'Supplier Notifications',
    'email_subject' => 'Email Subject',
    'email_body' => 'Email Body',
    'sms_body' => 'SMS Body',
    'available_tags' => 'Available Tags',
    'send_notification' => 'Send Notification',
    'new_sale_notification' => 'New Sale Notification',
    'send_email_only' => 'Send Email Only',
    'send_sms_only' => 'Send SMS Only',
    'send_both_email_n_sms' => 'Send Both Email & SMS',
    'to' => 'To',
    'mobile_number' => 'Mobile Number',
    'send' => 'Send',
    'notification_sent_successfully' => 'Notification sent successfully',
    'payment_received_notification' => 'Send Payment Received Notification',
    'send_payment_reminder' => 'Send Payment Remider',
    'new_order_notification' => 'New Order Notification',
    'item_received_notification' => 'Items Received Notification',
    'item_pending_notification' => 'Items Pending Notification',
    'payment_paid_notification' => 'Payment Paid Notification',
    'mail_host' => 'Host',
    'mail_port' => 'Port',
    'mail_username' => 'Username',
    'mail_password' => 'Password',
    'mail_encryption' => 'Encryption',
    'mail_from_address' => 'From Address',
    'mail_from_name' => 'From Name',
    'mail_encryption_place' => 'tls / ssl',
    'email_settings' => 'Email Settings',
    'sms_settings' => 'SMS Settings',
    'send_to_param_name' => 'Send to parameter name',
    'msg_param_name' => 'Message parameter name',
    'sms_settings_param_key' => 'Parameter :number key',
    'sms_settings_param_val' => 'Parameter :number value',
    'sending' => 'Sending',
    'request_method' => 'Request Method',
    'purchase_return' => 'Purchase Return',
    'return_quantity' => 'Return Quantity',
    'return_subtotal' => 'Return Subtotal',
    'quantity_left' => 'Quantity Remaining',
    'return_total' => 'Return Total',
    'total_return_tax' => 'Total Return Tax',
    'purchase_return_added_success' => 'Purchase return added successfully',
    'list_purchase_return' => 'List Purchase Return',
    'all_purchase_returns' => 'All Purchase Returns',
    'parent_purchase' => 'Parent Purchase',
    'purchase_return_details' => 'Purchase Return Details',
    'return_date' => 'Return Date',
    'parent_sale' => 'Parent Sale',
    'sell_quantity' => 'Sell Quantity',
    'total_return_discount' => 'Total Return Discount',
    'sell_return_details' => 'Sell Return Details',
    'sell_details' => 'Sale Details',
    'return_discount' => 'Return Discount',
    'total_unit_transfered' => 'Total Unit Transfered',
    'total_unit_adjusted' => 'Total Unit Adjusted',
    'return_exist' => 'Return exist for the Transaction, edit the return instead.',
    'synced_from_woocommerce' => 'Synced from Woocommerce',
    'available_stock_expired' => ' OR available stock has expired.',
    'sell_payments' => 'Permission to Add/Edit/Delete Payments in List Sells / List POS screen.',
    'purchase_payments' => 'Permission to Add/Edit/Delete Payments in List Purchases.',
    'sell.payments' => 'Add/Edit/Delete Payments',
    'purchase.payments' => 'Add/Edit/Delete Payments',
    'view_payment' => 'View Payment',
    'card_number' => 'Card No.',
    'transaction_number' => 'Transaction Number',
    'card_holder_name' => 'Card holder name',
    'card_number' => 'Card number',
    'card_transaction_number' => 'Card transaction No',
    'cheque_number' => 'Cheque number',
    'commsn_percent_help' => "Used only if Sales Commission Agent Type setting is: 'Logged In user' or 'Select from users list'",
    'max_amount_to_be_paid_is' => 'Maximum amount is :amount',
    'adjusted_for' => 'Adjusted For',
    'some_qty_returned' => 'Some quantities are returned from this purchase',
    'total_purchase_return' => 'Total Purchase Return',
    'total_purchase_return_paid' => 'Total Purchase Return Paid',
    'total_purchase_return_inc_tax' => 'Total Purchase Return Including Tax',
    'purchase_sell_report_formula' => 'Overall (Sale - Sale Return - Purchase - Purchase Return)',
    'purchase_return_due' => 'Purchase Return Due',
    'total_purchase_return_due' => 'Total Purchase Return Due',
    'receive_purchase_return_due' => 'Receive Purchase Return Due',
    'some_qty_returned_from_sell' => 'Some quantities are returned from this sale',
    'sell_due' => 'Sell Due',
    'sell_return_due' => 'Sell Return Due',
    'total_sell_return_due' => 'Total Sell Return Due',
    'pay_sell_return_due' => 'Pay Sell Return Due',
    'total_sell_return' => 'Total Sell Return',
    'total_sell_return_paid' => 'Total Sell Return Paid',
    'total_sell_return_inc_tax' => 'Total Sell Return Including Tax',
    'tooltip_columnize_taxes_heading' => 'Enter tax name for headings, heading should be present in tax name. For example headings can be: CGST, SGST, IGST & CESS. For tax names CGST@8% or CGST@10%; SGST@10% or SGST@8% etc',
    'sales_payment_dues' => 'Sales Payment Due',
    'purchase_payment_dues' => 'Purchase Payment Due',
    'tooltip_sales_payment_dues' => "Pending payment for Sales. <br/><small class='text-muted'>Based on invoice pay term. <br/> Showing payments to be received in 7 days or less.</small>",
    'edit_product_price_from_sale_screen' => 'Edit product price from sales screen',
    'edit_product_discount_from_sale_screen' => 'Edit product discount from Sale screen',
    'autosend_email' => 'Auto Send Email',
    'autosend_sms' => 'Auto Send SMS',
    'enable_selected_contacts' => 'Restrict Selected contacts',
    'tooltip_enable_selected_contacts' => 'Restrict access to selected contacts in sells/purchase customer/supplier search box',
    'selected_contacts' => 'Select Contacts',
    'view_role' => 'View role',
    'delete_role' => 'Delete role',
    'mail_driver' => 'Mail Driver',
    'disable_suspend_sale' => 'Disable Suspend Sale',
    'suspend' => 'Suspend',
    'suspend_note' => 'Suspend Note',
    'suspend_sale' => 'Suspend Sale',
    'view_suspended_sales' => 'View Suspended Sales',
    'suspended_sales' => 'Suspended Sales',
    'tooltip_suspend' => 'Suspend Sales (pause)',
    'business_telephone' => 'Business contact number',
    'accept_terms_and_conditions' => 'Accept Terms & Conditions',
    'terms_conditions' => 'Terms & Conditions',
    'select_base_unit' => 'Select base unit',
    'times_base_unit' => 'times base unit',
    'multi_unit_help' => 'Define this unit as the multiple of other units <br><strong>Ex: 1 dozen = 12 pieces</strong>',
    'add_as_multiple_of_base_unit' => 'Add as multiple of other unit',
    'product_description' => 'Product Description',
    'click_here' => 'Click here',
    'for_more_info' => 'for more info',
    'username_help' => 'Leave blank to auto generate username',
    'status_for_user' => 'Is active ?',
    'tooltip_enable_user_active' => 'Check/Uncheck to make a user active/inactive.',
    'user_inactive' => 'Sorry, account is inactive',
    'calculator' => 'Calculator',
    'fields_for_customer_details' => 'Fields for customer details',
    'show_previous_bal_due' => 'Show total balance due (All sales)',
    'previous_bal_due_help' => 'Check this field to show sum of balance dues for all sales of the customer if exists',
    'all_sales' => 'All sales',
    'current_sale' => 'Current sale',
    'view_invoice_url' => 'Invoice URL',
    'copy' => 'Copy',
    'extra_tags' => 'Extra tags',
    'date_time_format' => 'Date time format',
    'date_time_format_help' => "Enter date and time format in <a target='_blank' href='http://php.net/manual/en/function.date.php'>PHP datetime format</a>. If blank business date time format will be applied",
    'all' => 'All',
    'detailed' => 'Detailed',
    'grouped' => 'Grouped',
    'disable_recurring_invoice' => 'Disable Recurring Invoice',
    'is_recurring' => 'Is Recurring',
    'recurring_invoice_help' => 'If subscribed this invoice will be automatically generated at regular intervals.<br>You can disable this feature in <code>Settings > Business Settings > Modules</code>',
    'recurring_invoice' => 'Recurring Invoice',
    'years' => 'Years',
    'no_of_repetitions' => 'No. of Repetitions',
    'recur_repetition_help' => 'If blank invoice will be generated infinite times',
    'recurring_invoice_message' => 'New Invoice generated for Subscription no.:<i>:subscription_no</i>, Invoice no.: <i>:invoice_no</i>',
    'notifcation_count_msg' => 'You have :count notifications',
    'no_notifications_found' => 'No notifications found',
    'edit_multi_unit_help_text' => 'Editing this value will change the purchase & sales stocks accordingly',
    'sale_price_is_minimum_sale_price' => 'Sales price is minimum selling price',
    'minimum_sale_price_help' => 'If this is enabled, on the POS or Sales screen default selling price will be the minimum selling price for the product. You cannot set price below the default selling price.',
    'minimum_selling_price_error_msg' => 'Minimum Selling Price is :price',
    'lot_numbers_are_used_in_sale' => 'Cannot be deleted, Some lots from this purchase are already sold',
    'created_at' => 'Created At',
    'years' => 'Years',
    'download' => 'Download',
    'previous_file_will_be_replaced' => 'Previously uploaded file will be replaced',
    'access_accounts' => 'Access Accounts',
    'payment_accounts' => 'Payment Accounts',
    'total_shipping_charges' => 'Total Shipping Charges',
    'recurring_invoice_error_message' => 'Unable to create invoice for Subscription no.: <i>:subscription_no</i>. Required stock not available for product <i>:product_name</i>',
    'load_more' => 'Load More',
    'cash_flow' => 'Cash Flow',
    'subscribe' => 'Subscribe',
    'subscription_interval' => 'Subscription Interval',
    'enable_subscription' => 'Enable Subscription',
    'subscription_no' => 'Subscription No.',
    'start_subscription' => 'Start Subscription',
    'stop_subscription' => 'Stop Subscription',
    'subscriptions' => 'Subscriptions',
    'generated_invoices' => 'Generated Invoices',
    'last_generated' => 'Last generated',
    'upcoming_invoice' => 'Upcoming invoice',
    'total_sales_return' => 'Total Sales Return',
    'subscription_invoice' => 'Subscription Invoice',
    'subscribed_invoice' => 'Subscribed Invoice',
    'view_document' => 'View Document',
    'invoice_url_help' => 'Link to view the invoice without login.',
    'unit_cannot_be_deleted' => 'Products exist with this unit; Cannot be deleted',
    'expiry_date_in_business_date_format' => 'Stock expiry date in <b>Business date format</b>',
    'deactivate_selected' => 'Deactivate Selected',
    'products_deactivated_success' => 'Products deactivated successfully',
    'deactive_product_tooltip' => 'Deactivated products will not be available for purchase or sell',
    'reactivate' => 'Reactivate',
    'allow_overselling_help' => 'Check this field to allow a product to sell more than the available quantity. Oversold quantity will be adjusted automatically from future stock.',
    'add_discount' => 'Add Discount',
    'applicable_in_cpg' => 'Apply in selling price groups',
    'applicable_in_cg' => 'Apply in customer groups',
    'is_active' => 'Is active',
    'discount_priority_help' => 'Discount with higher priority will have higher weightage, however priority will not be considered for exact matches',
    'inactive' => 'Inactive',
    'applied_discount_text' => 'Discount: <i>:discount_name</i> applied (Ends at: <i>:ends_at</i>)',
    'discount.access' => 'Add/Edit/Delete Discount',
    'product_stock_details' => 'Product Stock Details',
    'search' => 'Search',
    'total_sold' => 'Total Sold',
    'total_stock_available' => 'Total Stock Available',
    'total_stock_transfered_to_the_location' => 'Total stock transfered to the location',
    'total_stock_transfered_from_the_location' => 'Total stock transfered from the location',
    'total_stock_calculated' => 'Total stock calculated',
    'adjust_stock_mismatch' => 'Adjust Stock Mismatch',
    'opening_balance_payments' => 'Opening Balance Payments',
    'enable_service_staff_in_product_line' => 'Enable service staff in product line',
    'inline_service_staff_tooltip' => 'If enabled different service staffs can be assigned for different products for an order/sale',
    'line_orders' => 'Line Orders',
    'total_tax' => 'Total Tax',
    'total_discount' => 'Total Discount',
    'net_price' => 'Net Price',
    'select_same_for_all_rows' => 'Select same value for all products',
    'edited' => 'Edited',
    'by' => 'By',
    'upload_documents' => 'Upload Documents',
    'documents' => 'Documents',
    'click_to_print' => 'Click to print',
    'deactivated_success' => 'Deactivated successfully',
    'products_could_not_be_deleted' => "Some products couldn't be deleted because it has transactions related to it.",
    'price' => 'Price',
    'auto_backup_instruction' => 'To enable auto backup you must setup a cron job with this command',
    'gross_profit' => 'Gross Profit',
    'single' => 'Single',
    'variable' => 'Variable',
    'total_purchase_price' => 'Total purchase price',
    'total_sell_price' => 'Total sell price',
    'packing_slip' => 'Packing Slip',
    'use_superadmin_email_settings' => 'Use system email configurations',
    'profit_by_products' => 'Profit by products',
    'profit_by_categories' => 'Profit by categories',
    'profit_by_brands' => 'Profit by brands',
    'profit_by_locations' => 'Profit by locations',
    'profit_by_invoice' => 'Profit by invoice',
    'profit_by_date' => 'Profit by date',
    'uncategorized' => 'Uncategorized',
    'add_purchase_return' => 'Add Purchase Return',
    'edit_purchase_return' => 'Edit Purchase Return',
    'purchase_return_updated_success' => 'Purchase return updated successfully',
    'profit_by_customer' => 'Profit by customer',
    'profit_by_day' => 'Profit by day',
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    'show_product_image' => 'Show product image',
    'view_user' => 'View User',
    'allow_selected_contacts' => 'Allow Selected Contacts',
    'allow_selected_contacts_tooltip' => 'Only allow access to selected contacts in sells/purchase customer/supplier search box',
    'allowed_contacts' => 'Allowed Contacts',
    'view_purchase_price' => 'View Purchase Price',
    'view_purchase_price_tooltip' => 'Permission to view purchase price in product details',
    'created' => 'Created',
    'updated' => 'Updated',
    'deleted' => 'Deleted',
    'day' => 'Day',
    'is_service_staff_required' => 'Is service staff required',
    'items_report' => 'Items Report',
    'purchase_price' => 'Purchase Price',
    'sell_date' => 'Sell Date',
    'returned' => 'returned',
    'dob' => 'Date of birth',
    'married' => 'Married',
    'unmarried' => 'Unmarried',
    'marital_status' => 'Marital Status',
    'blood_group' => 'Blood Group',
    'divorced' => 'Divorced',
    'contact_no' => 'Contact Number',
    'fb_link' => 'Facebook Link',
    'twitter_link' => 'Twitter Link',
    'social_media' => 'Social Media :number',
    'permanent_address' => 'Permanent Address',
    'current_address' => 'Current Address',
    'guardian_name' => 'Guardian Name',
    'custom_field' => 'Custom Field :number',
    'bank_details' => 'Bank Details',
    'account_holder_name' => "Account Holder's Name",
    'account_number' => 'Account Number',
    'bank_name' => 'Bank Name',
    'bank_code' => 'Bank Identifier Code',
    'bank_code_help' => 'A unique code to identify the bank in your country, for example: IFSC code',
    'branch' => 'Branch',
    'tax_payer_id' => 'Tax Payer ID',
    'tax_payer_id_help' => 'Tax number id of the employee, for example, PAN card in India',
    'more_info' => 'More Informations',
    'id_proof_name' => 'ID proof name',
    'id_proof_number' => 'ID proof number',
    'view_own_sell_only' => 'View own sell only',
    'reward_point_settings' => 'Reward Point Settings',
    'enable_rp' => 'Enable Reward Point',
    'rp_name' => 'Reward Point Display Name',
    'amount_for_unit_rp' => 'Amount spend for unit point',
    'min_order_total_for_rp' => 'Minimum order total to earn reward',
    'max_rp_per_order' => 'Maximum points per order',
    'earning_points_setting' => 'Earning Points Settings',
    'redeem_points_setting' => 'Redeem Points Settings',
    'redeem_amount_per_unit_rp' => 'Redeem amount per unit point',
    'min_order_total_for_redeem' => 'Minimum order total to redeem points',
    'min_redeem_point' => 'Minimum redeem point',
    'max_redeem_point' => 'Maximum redeem point per order',
    'rp_expiry_period' => 'Reward Point expiry period',
    'redeemed_amount' => 'Redeemed Amount',
    'max_points_used' => 'Maximum :points points can be used',
    'reward_points' => 'Reward Points',
    'max_reward_point_available' => 'Maximum :name available',
    'min_reward_points_used' => 'Minimum :name can be used',
    'redeemed' => 'Redeemed',
    'available' => 'Available',
    'earned' => 'Earned',
    'total_reward_amount' => 'Total customer reward',
    'ledger' => 'Ledger',
    'type' => 'Type',
    'show_payments' => 'Show Payments',
    'variation_images' => 'Variation Images',
    'file_deleted_successfully' => 'File deleted successfully',
    'combo' => 'Combo',
    'not_for_selling' => 'Not for selling',
    'tooltip_not_for_selling' => 'If checked, product will not be displayed in sales screen for selling purposes.',
    'show_reward_point' => 'Show reward point',
    'enable_booking' => 'Enable Bookings',
    'bulk_edit' => 'Bulk Edit',
    'bulk_edit_products' => 'Bulk Edit Products',
    'search_product_to_edit' => 'Search product to edit',
    'related_sub_units' => 'Related Sub Units',
    'sub_units_tooltip' => 'Based on selected Unit it will show sub units for it. Select the sub-unit applicable. Leave blank if all sub-units are applicable for the product.',
    'enable_sub_units' => 'Enable Sub Units',
    'added_on' => 'Added On',
    'quantity_mismatch_exception' => 'ERROR: NOT ALLOWED: Mismatch between sold and available quantity. Product: :product',
    'roles_and_permissions' => 'Roles and Permissions',
    'admin_role_location_permission_help' => 'Admin can access all locations',
    'edit_product_price_from_pos_screen' => 'Edit product price from POS screen',
    'edit_product_discount_from_pos_screen' => 'Edit product discount from POS screen',
    'download_template_file' => 'Download template file',
    'types_of_service' => 'Types of service',
    'add_type_of_service' => 'Add type of service',
    'packing_charge_type' => 'Packing Charge Type',
    'packing_charge' => 'Packing Charge',
    'enable_custom_fields' => 'Enable custom fields',
    'edit_type_of_service' => 'Edit type of service',
    'types_of_service_help' => 'Type of service means services like dine-in, parcel, home delivery, third party delivery etc.',
    'select_types_of_service' => 'Select types of service',
    'service_custom_field_1' => 'Custom Field 1',
    'service_custom_field_2' => 'Custom Field 2',
    'service_custom_field_3' => 'Custom Field 3',
    'service_custom_field_4' => 'Custom Field 4',
    'default_selling_price_group' => 'Default Selling Price Group',
    'location_price_group_help' => 'This price group will be used as the default price group in this location.',
    'types_of_service_help_long' => 'Type of service means services like dine-in, parcel, home delivery, third party delivery etc. It can be enabled/disabled from <code>Settings > Modules</code> and can be created from <code>Settings > Types of service</code>. You can also define different price group & packing charge for service types.',
    'default_accounts' => 'Default Account',
    'product_location_help' => 'Locations where product will be available.',
    'available_in_locations' => 'Available in locations',
    'enable' => 'Enable',
    'payment_options' => 'Payment Options',
    'pay_reference_no' => 'Pay reference no.',
    'search_settings' => 'Search Settings',
    'custom_labels' => 'Custom Labels',
    'labels_for_custom_payments' => 'Labels for custom payments',
    'add_to_location' => 'Add to location',
    'remove_from_location' => 'Remove from location',
    'add_location_to_the_selected_products' => 'Add location to the selected products',
    'remove_location_from_the_selected_products' => 'Remove location from the selected products',
    'types_of_service_label' => 'Types of service label',
    'show_types_of_service' => 'Show types of service',
    'show_tos_custom_fields' => 'Show types of service custom fields',
    'types_of_service_module_settings' => 'Types of service module settings',
    'product_business_location_tooltip' => 'Product will be available only in this business locations',
    'product_not_assigned_to_any_location' => 'Product not assigned to any locations',
    'packed' => 'Packed',
    'shipped' => 'Shipped',
    'delivered' => 'Delivered',
    'shipping_address' => 'Shipping Address',
    'shipping_status' => 'Shipping Status',
    'delivered_to' => 'Delivered To',
    'edit_shipping' => 'Edit Shipping',
    'shipments' => 'Shipments',
    'access_shipping' => 'Access Shipments',
    'uploaded_by' => 'Uploaded By',
    'account_types' => 'Account Types',
    'add_account_type' => 'Add account type',
    'parent_account_type' => 'Parent account type',
    'edit_account_type' => 'Edit account type',
    'account_type' => 'Account Type',
    'account_sub_type' => 'Account Sub Type',
    'enable_updating_product_price_tooltip' => 'If enabled product purchase price and selling price will be updated after a purchase is added or updated',
    'default_datatable_page_entries' => 'Default datatable page entries',
    'third_party_order_id' => '3rd party Order ID',
    'payment_option_help' => 'Enable or disable payment methods for the location',
    'default_account_help' => 'Choose default account to be pre selected for the payment method. You can change it while adding payment',
    'product_not_found_exception' => 'Product with SKU :sku not found  on row :row',
    'price_group_not_found_exception' => 'Price Group with name :pg not found on row :row',
    'price_group_non_numeric_exception' => 'Non numeric price found on row :row',
    'update_status' => 'Update Status',
    'types_of_service_details' => 'Types of service details',
    'total_stock_price' => 'Current Stock Value',
    'test_email_configuration' => 'Send test email',
    'email_tested_successfully' => 'Email configurations verified successfully',
    'test_number' => 'Test Number',
    'test_sms_configuration' => 'Send test SMS',
    'test_number_is_required' => 'Test number is required',
    'import_contact_type_ins' => 'Available Options: <strong><br> 1 = Customer, <br> 2 = Supplier <br> 3 = Both</strong>',
    'enable_pos_transaction_date' => 'Enable transaction date on POS screen',
    'amount_for_unit_rp_tooltip' => '<strong>Meaning how much the customer spent to get one reward points.</strong> <br><br> <strong>Example:</strong> If you set it as 10, then for every $10 spent by customer they will get one reward points. If the customer purchases for $1000 then they will get 100 reward points.',
    'max_rp_per_order_tooltip' => 'Maximum reward points customers can earn in one invoice. Leave it empty if you don’t want any such restrictions.',
    'min_order_total_for_rp_tooltip' => '<strong>The minimum amount the customer should spend to get reward points.</strong> <br><br> <strong>Example:</strong> If you set it as 100 then customer will get reward points only if there invoice total is greater or equal to 100. If invoice total is 99 then they won’t get any reward points. <br><br>You can set it as minimum 1.',
    'redeem_amount_per_unit_rp_tooltip' => '<strong> It indicates the redeem amount per point.</strong> <br><br> <strong>For example:</strong>If 1 point is $1 then enter the value as 1. If 2 points is $1 then enter the value as 0.50',
    'min_order_total_for_redeem_tooltip' => 'Minimum order total for which customers can redeem points. Leave it blank if you don’t need this restriction or you need to give something for free.',
    'min_redeem_point_tooltip' => 'Minimum redeem points that can be used per invoice. Leave it blank if you don’t need this restriction.',
    'max_redeem_point_tooltip' => 'Maximum points that can be used in one order. Leave it blank if you don’t need this restriction.',
    'rp_expiry_period_tooltip' => '<strong>Expiry period for points earned by customer.</strong>  <br><br>You can set it in months or year. Expired points will get deducted from customer account automatically after this period.',
    'configure_product_search' => 'Configure product search',
    'search_products_by' => 'Search products by',
    'manage_modules' => 'Manage Modules',
    'install' => 'Install',
    'uninstall' => 'Uninstall',
    'module_new_version' => 'There is a new version of :module available <a href=":link">Update</a>',
    'version' => 'Version',
    'duplicate_taxonomy_type_found' => 'Duplicate taxonomy type found',
    'taxonomy_type_not_found' => 'Taxonomy type not found',
    'credit_sales' => 'Credit Sales',
    'total_payment' => 'Total Payment',
    'deactivate_location' => 'Deactivate Location',
    'activate_location' => 'Activate Location',
    'business_location_activated_successfully' => 'Business location activated successfully',
    'business_location_deactivated_successfully' => 'Business location deactivated successfully',
    'show_credit_sale_button' => 'Show Credit Sale Button',
    'show_credit_sale_btn_help' => 'If enabled credit sale button will be shown in place of Card button on pos screen',
    'credit_sale' => 'Credit Sale',
    'tooltip_credit_sale' => 'Checkout as credit sale',
    'add_as_sub_txonomy' => 'Add as sub taxonomy',
    'select_parent_taxonomy' => 'Select parent taxonomy',
    'warranty' => 'Warranty',
    'warranties' => 'Warranties',
    'all_warranties' => 'All Warranties',
    'add_warranty' => 'Add Warranty',
    'duration' => 'Duration',
    'edit_warranty' => 'Edit Warranty',
    'enable_product_warranty' => 'Enable Warranty',
    'warranty_ends_on' => 'Warranty ends on',
    'restaurant_module_settings' => 'Restaurant module settings',
    'warranty_module_settings' => 'Warranty module settings',
    'show_warranty_name' => 'Show warranty name',
    'show_warranty_exp_date' => 'Show warranty expiry date',
    'show_warranty_description' => 'Show warranty description',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'others' => 'Others',
    'recent_transactions' => 'Recent Transactions',
    'add_row' => 'Add row',
    'add_widget_here' => 'Add a widget here',
    'move_row' => 'Move the row',
    'configure_dashboard' => 'Configure Dashboard :name',
    'change_return_label' => 'Change return label',
    'change_return_help' => 'Change return is the amount to be returned to the customer if excess payment is done.',
    'profile_photo' => 'Profile Photo',
    'upload_image' => 'Upload Image',
    'expense_tax' => 'Expense Tax',
    'expense_tax_tooltip' => 'Total tax paid towards business expenses for the selected time period',
    'tax_overall' => 'Overall (Input - Output - Expense)',
    'overdue' => 'Overdue',
    'partial-overdue' => 'Partial Overdue',
    'due_date_label' => 'Due date label',
    'show_due_date' => 'Show due date',
    'contact_custom_field1' => 'Custom Field 1',
    'contact_custom_field2' => 'Custom Field 2',
    'contact_custom_field3' => 'Custom Field 3',
    'contact_custom_field4' => 'Custom Field 4',
    'added_by' => 'Added By',
    'authorized_signatory' => 'Authorized Signatory',
    'no_location_access_found' => 'No location access found',
    'location_custom_field1' => 'Custom field 1',
    'location_custom_field2' => 'Custom field 2',
    'location_custom_field3' => 'Custom field 3',
    'location_custom_field4' => 'Custom field 4',
    'user_custom_field1' => 'Custom field 1',
    'user_custom_field2' => 'Custom field 2',
    'user_custom_field3' => 'Custom field 3',
    'user_custom_field4' => 'Custom field 4',
    'logo_not_work_in_sms' => 'Business logo will not work in SMS',
    'notification_email_tooltip' => 'Comma separated string of recipient emails',
    'heading' => 'Heading',
    'updated_at' => 'Updated At',
    'documents_and_notes' => 'Documents & Note',
    'add_note' => 'Add Note',
    'edit_note' => 'Edit Note',
    'is_private' => 'Is Private?',
    'private' => 'Private',
    'documents' => 'Documents',
    'note_will_be_visible_to_u_only' => 'Note & document will be visible to only you',
    'activities' => 'Activities',
    'no_limit' => 'No Limit',
    'contains_media' => 'This note contains media',
    'private_note' => 'This is private note',
    'amount_rounding_method' => 'Amount rounding method',
    'round_to_nearest_whole_number' => 'Round to nearest whole number',
    'round_to_nearest_decimal' => 'Round to nearest decimal (multiple of :multiple)',
    'amount_rounding_method_help' => 'Example: <br><b>Round to nearest 
    whole number:</b> 2.23 => 2, 2.50 => 3, 2.73 => 3 <br><b>Round to nearest 
    decimal (multiple of 0.05):</b> 2.11 => 2.10, 2.12 => 2.10, 2.13 => 2.15',
    'round_off' => 'Round Off',
    'round_off_label' => 'Round off label',
    'total_sell_round_off' => 'Total sell round off',
    'user_info' => 'User Information',
    'enable_weighing_scale' => 'Enable Weighing Scale',
    'weighing_scale_setting' => 'Weighing Scale barcode Setting',
    'weighing_scale_setting_help' => 'Configure barcode as per your weighing scale.',
    'weighing_barcode_prefix' => 'Prefix',
    'weighing_product_sku_length' => 'Product sku length',
    'weighing_qty_integer_part_length' => 'Quantity integer part length',
    'weighing_qty_fractional_part_length' => 'Quantity fractional part length',
    'weighing_scale' => 'Weighing Scale',
    'weighing_scale_barcode' => 'Weighing scale barcode',
    'weighing_scale_barcode_help' => 'Scan the barcode from weighing sale and submit',
    'prefix_did_not_match' => 'Prefix did not match',
    'sku_not_match' => 'No Product found with sku :sku',
    'total_quantity_label' => 'Total quantity label',
    'account_summary' => 'Account Summary',
    'ledger_table_heading' => 'Showing all invoices and payments between :start_date and :end_date',
    'beginning_balance' => 'Beginning balance',
    'total_invoice' => 'Total invoice',
    'balance_due' => 'Balance due',
    'send_ledger' => 'Send Ledger',
    'notifications' => 'Notifications',
    'ledger_attacment_help' => 'Ledger will be attached in PDF format with this email',
    'upload_module' => 'Upload Module',
    'upload' => 'Upload',
    'pls_upload_valid_zip_file' => 'Please upload a valid zip file.',
    'import_sales' => 'Import Sales',
    'preview_imported_sales' => 'Preview Imported Sales',
    'customer_phone_number' => 'Customer Phone number',
    'customer_email' => 'Customer Email',
    'item_tax' => 'Item Tax',
    'item_discount' => 'Item Discount',
    'item_description' => 'Item Description',
    'order_total' => 'Order Total',
    'upload_and_review' => 'Upload and review',
    'group_sale_line_by' => 'Group sell line by',
    'import_sale_product_not_found' => 'Product with name :product_name or sku :sku not found in row :row',
    'import_sale_tax_not_found' => 'Tax with name :tax_name not found in row :row',
    'email_or_phone_required' => 'Customer phone or email is required',
    'product_name_or_sku_is_required' => 'Product name or SKU is required',
    'quantity_is_required' => 'Quantity is required',
    'unit_price_is_required' => 'Unit price is required',
    'sales_imported_successfully' => 'Sales imported successfully',
    'upload_data_in_excel_format' => 'Upload sales data in excel format',
    'map_columns_with_respective_sales_fields' => 'Choose respective sales fields for each column',
    'choose_location_and_group_by' => 'Choose business location and column by which sell lines will be grouped',
    'date_format_instruction' => 'Sale date time format should be "Y-m-d H:i:s" (2020-07-15 17:45:32)',
    'product_name_single_only' => 'Product name (Single or combo products only)',
    'product_sku' => 'Product SKU',
    'product_unit' => 'Product Unit',
    'import_sale_unit_not_found' => 'Unit with name :unit_name not found in row :row',
    'types_of_servicet_not_found' => 'Types of service with name :types_of_service_name not found in row :row',
    'email_or_phone_cannot_be_empty_in_row' => 'Either customer phone or email is required in row :row',
    'product_cannot_be_empty_in_row' => 'Either product name or product SKU is required in row :row',
    'quantity_cannot_be_empty_in_row' => 'Product quantity is required in row :row',
    'unit_price_cannot_be_empty_in_row' => 'Unit Price is required in row :row',
    'imports' => 'Imports',
    'invoices' => 'Invoices',
    'import_batch' => 'Import batch',
    'import_time' => 'Import time',
    'cannot_select_a_field_twice' => 'You cannot select a field twice',
    'revert_import' => 'Revert Import',
    'import_reverted_successfully' => 'Import reverted successfully',
    'importable_fields' => 'Importable fields',
    'sort_order' => 'Sort order',
    'total_returned' => 'Total returned',
    'skip' => 'SKIP',
    'group_by_tooltip' => 'Column for grouping sell lines in same invoice',
    'allowed_file' => 'Allowed File',
    'detailed_with_purchase' => 'Detailed (With purchase)',
    'supplier_name' => 'Supplier Name',
    'purchase_ref_no' => 'Purchase ref no.',
    'save_and_print' => 'Save and print',
    'update_and_print' => 'Update and print',
    'all_your_discounts' => 'All your discounts',
    'priority' => 'Priority',
    'starts_at' => 'Starts At',
    'ends_at' => 'Ends At',
    'used_for_browser_based_printing' => 'Used for browser based printing',
    'hsn_or_category_code' => 'HSN or Category Code',
    'discounts' => 'Discounts',
    'allow_overselling' => 'Allow Overselling',
    'profile_updated_successfully' => 'Profile updated successfully',
    'password_updated_successfully' => 'Password updated successfully',
    'u_have_entered_wrong_password' => 'You have entered wrong password',
    'business_dont_have_crm_subscription' => "Business doesn't have Crm subscription",
    'source' => 'Source',
    'life_stage' => 'Life Stage',
    'assigned_to' => 'Assigned to',
    'view_own_purchase' => 'View own purchase only',
    'view_own_expense' => 'View own expense only',
    'allow_login' => 'Allow login',
    'login_not_allowed' => 'Login not allowed',
    'labels_for_contact_custom_fields' => 'Labels for contact custom fields',
    'labels_for_product_custom_fields' => 'Labels for product custom fields',
    'labels_for_purchase_custom_fields' => 'Label for purchase custom fields',
    'labels_for_sell_custom_fields' => 'Labels for sell custom fields',
    'labels_for_location_custom_fields' => 'Labels for location custom fields',
    'labels_for_types_of_service_custom_fields' => 'Labels for types of service custom fields',
    'labels_for_user_custom_fields' => 'Labels for user custom fields',
    'total_transfer_shipping_charge' => 'Total transfer shipping charge',
    'total_purchase_shipping_charge' => 'Total purchase shipping charge',
    'total_sell_shipping_charge' => 'Total sell shipping charge',
    'opening_stock_location' => 'Opening stock location',
    'product_locations_ins' => 'Comma separated string of business location names where product will be available',
    'product_locations' => 'Product locations',
    'view_products' => 'View Products',
    'pos_screen_featured_products' => 'POS screen Featured Products',
    'featured_products_help' => 'Selected products will be shown on top of the pos screen product suggestion for quick access',
    'purchase_report' => 'Purchase Report',
    'sale_report' => 'Sale Report',
    'payment_date' => 'Payment Date',
    'year_month' => 'Year/Month',
    'lead' => 'Lead',
    'featured_products' => 'Featured Products',
    'repeat_on' => 'Repeat on',
    'access_tables' => 'Access tables',
    'access_printers' => 'Access printers',
    'access_types_of_service' => 'Access types of service',
    'search_address' => 'Search address',
    'contact_locations' => 'Contact Locations',
    'map' => 'Map',
    'both_customer_and_supplier' => 'Both customer & supplier',
    'service' => 'Service',
    'either_cust_email_or_phone_required' => 'Either customer email id or phone number required',
    'either_product_name_or_sku_required' => 'Either product name (for single and combo only) or product sku required',
    'show_invoice_scheme' => 'Show invoice scheme',
    'select_invoice_scheme' => 'Select invoice scheme',
    'recurring_expense_help' => 'If checked this expense will be automatically generated at regular intervals.',
    'recur_interval' => 'Recurring interval',
    'recur_expense_repetition_help' => 'If blank expense will be generated infinite times',
    'recurring_expense_message' => 'New recurring expense generated. Ref no.: <i>:ref_no</i>',
    'recurring_expense' => 'Recurring expense',
    'generated_recurring_expense' => 'Generated recurring expense',
    'select_contacts' => 'Select contacts',
    'middle_name' => 'Middle name',
    'address_line_1' => 'Address line 1',
    'address_line_2' => 'Address line 2',
    'dob' => 'Date of birth',
    'dob_ins' => 'Format Y-m-d',
    'by_purchase_price' => 'By purchase price',
    'by_sale_price' => 'By sale price',
    'potential_profit' => 'Potential profit',
    'apply_all' => 'Apply all',
    'hide_all_prices' => 'Hide all prices',
    'select_invoice_layout' => 'Select invoice layout',
    'max_sales_discount_percent' => 'Max sales discount percent',
    'max_sales_discount_percent_help' => 'Maximum discount percentage that a user can give during sale. Leave it blank for no constraints',
    'max_discount_error_msg' => 'You can give max :discount% discount per sale',
    'show_invoice_layout' => 'Show invoice layout dropdown',
    'woocommerce_enabled' => 'Woocommerce enabled',
    'recurred_from' => 'Recurred from',
    'recur_details' => 'Recurring details',
    'recurring' => 'Recurring',
    'view_product_stock_value' => 'View product stock value',
    'added' => 'Added',
    'disable_credit_sale_button' => 'Disable credit sale button',
    'pay' => 'Pay',
    'use_advance_balance' => 'Use advance balance',
    'advance' => 'Advance',
    'advance_balance' => 'Advance Balance',
    'advance_payment' => 'Advance payment',
    'required_advance_balance_not_available' => 'Required advance balance not available',
    'in_transit' => 'In Transit',
    'edit_stock_transfer' => 'Edit Stock Transfer',
    'completed_status_help' => 'Stock transfer will not be editable if status is completed',
    'no_data' => 'No data',
    'sms_service' => 'SMS Service',
    'nexmo_key' => 'Nexmo Key',
    'nexmo_secret' => 'Nexmo Secret',
    'twilio_token' => 'Twilio Access Token',
    'twilio_sid' => 'Twilio Account SID',
    'upcoming' => 'Upcoming',
    'final' => 'Final',
    'expense_for_contact' => 'Expense for contact',
    'create' => 'Create',
    'waiting' => 'Waiting',
    'sms_settings_header_key' => 'Header :number key',
    'sms_settings_header_val' => 'Header :number value',
    'stock_adjustment_details' => 'Stock adjustment details',
    'stock_transfer_details' => 'Stock transfer details',
    'invoice_layout_for_pos' => 'Invoice layout for POS',
    'invoice_layout_for_sale' => 'Invoice layout for sale',
    'invoice_layout_for_sale_tooltip' => 'Invoice layout for direct sales',
    'new_quotation' => 'New Quotation',
    'new_quotation_notification' => 'New quotation notification',
    'view_quote_url' => 'View quote url',
    'close_cash_register' => 'Close cash register',
    'view_cash_register' => 'View cash register',
    'for_tax_group_only' => 'For tax group only',
    'for_tax_group_only_help' => 'If checked, this tax will not be displayed indivisually in dropdowns, only can be added in tax group',
    'exempt' => 'Exempt',
    'tax_exempt_help' => 'Zero percent tax rate will be considered as tax exempt',
    'show_total_in_words' => 'Show total in words',
    'show_in_word_help' => 'php-intl extention need to be enabled',
    'print_on_suspend' => 'Print invoice on suspend',
    'view_all_customer' => 'View all customer',
    'view_own_customer' => 'View own customer',
    'view_all_supplier' => 'View all supplier',
    'view_own_supplier' => 'View own supplier',
    'label_help' => 'Print Barcode/Label',
    'connection_type_windows' => 'Connection Type Windows',
    'connection_type_linux' => 'Connection Type Linux',
    'windows_type_help' => 'The device files will be along the lines of',
    'linux_type_help' => 'Your printer device file will be somewhere like',
    'due_date' => 'Due Date',
    'quotation_no' => 'Quotation number',
    'browser_based_printing' => 'Browser Based Printing',
    'configured_printer' => 'Use Configured Receipt Printer',
    'calendar' => 'Calendar',
    'parent_payment' => 'Parent Payment',
    'for_normal_printer' => 'For normal printer',
    'recomended_for_80mm' => 'Recommended for thermal line receipt printer, 80mm paper size',
    'recomended_for_58mm' => 'Recommended for thermal line receipt printer, 80mm and 58mm paper size',
    'classic' => 'Classic',
    'elegant' => 'Elegant',
    'slim' => 'Slim',
    'detailed' => 'Detailed',
    'columnize_taxes' => 'Columnize Taxes',
    'access_sell_return' => 'Access sell return',
    'system_notification' => 'System Notification',
    'time_range' => 'Time range',
    'enable_php_intl_extension' => 'Enable php-intl extension in PHP INI settings',
    'profit_note' => '<b>Note:</b> Profit by products/categories/brands only considers inline discount. Invoice discount is not considered.',
    'is_refund' => 'Is refund',
    'is_refund_help' => 'If checked expense will be refunded and will be added to net profit',
    'refund' => 'Refund',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'add_quotation' => 'Add Quotation',
    'product_stock_history' => 'Product stock history',
    'quantities_in' => 'Quantities In',
    'quantities_out' => 'Quantities Out',
    'in' => 'In',
    'out' => 'Out',
    'totals' => 'Totals',
    'quantity_change' => 'Quantity change',
    'new_quantity' => 'New Quantity',
    'no_stock_history_found' => 'No stock history found',
    'add_draft' => 'Add Draft',
    'labels_for_sale_shipping_custom_fields' => 'Labels for sale shipping custom fields',
    'is_required' => 'Is required',
    'convert_to_invoice' => 'Convert to invoice',
    'converted_to_invoice_successfully' => 'Converted to invoice :invoice_no successfully',
    'proforma' => 'Proforma',
    'proforma_invoice' => 'Proforma invoice',
    'convert_to_proforma' => 'Convert to Proforma Invoice',
    'converted_to_proforma_successfully' => 'Converted to Proforma Invoice',
    'billing_address' => 'Billing Address',
    'delete_sell' => 'Delete Sell',
    'product_brochure' => 'Product brochure',
    'whatsapp_text' => 'Whatsapp Text',
    'auto_send_wa_notif' => 'Auto send Whatsapp notification',
    'send_whatsapp' => 'Send Whatsapp notification',
    'send_sms' => 'Send SMS',
    'send_email' => 'Send Email',
    'shipping_documents' => 'Shipping Documents',
    'no_attachment_found' => 'No attachment found',
    'attachments' => 'Attachments',
    'print_invoice' => 'Print Invoice',
    'word_format' => 'Word Format',
    'international' => 'International',
    'indian' => 'Indian',
    'word_format_help' => 'In international format big numbers are represented in million, billion and trillion where as in indian format it is represented in lakhs and crores',
    'shipping_edited' => 'Shipping edited',
    'payment_edited' => 'Payment edited',
    'notification_sent' => 'Notification Sent',
    'imported' => 'Imported',
    'logout' => 'Logout',
    'backup_clean_command_instruction' => 'To clean up old backup you must setup a cron job with this command:',
    'converted' => 'Converted',
    'view_commission_agent_sell' => 'Commission agent can view their own sell',
    'access_commission_agent_shipping' => 'Commission agent can access their own shipments',
    'access_own_shipping' => 'Access own shipments',
    'email_notification_sent' => 'Email notification sent',
    'sms_notification_sent' => 'Sms notification sent',
    'packing_date' => 'Packing Date',
    'print_exp_date' => 'Print expiry date',
    'print_packing_date' => 'Print packing date',
    'print_lot_number' => 'Print lot number',
    'family_contact_number' => 'Family contact number',
    'custom_payment' => 'Custom Payment :number',
    'default_credit_limit' => 'Default credit limit',
    'price_calculation_type' => 'Price calculation type',
    'show_pricing_on_product_sugesstion' => 'Show pricing on product suggestion tooltip',
    'all_purchase_orders' => 'All purchase orders',
    'add_purchase_order' => 'Add Purchase Order',
    'edit_purchase_order' => 'Edit purchase order',
    'purchase_order_details' => 'Purchase order details',
    'enable_purchase_order' => 'Enable purchase order',
    'purchase_order_help_text' => 'A purchase order is a commercial document and first official offer issued by a buyer to a seller indicating types, quantities, and agreed prices for products or services. It is used to control the purchasing of products and services from external suppliers.Purchase orders can be an essential part of enterprise resource planning system orders.',
    'view_all_purchase_order' => 'View all purchase order',
    'view_own_purchase_order' => 'View own purchase order',
    'create_purchase_order' => 'Create purchase order',
    'edit_purchase_order' => 'Edit purchase order',
    'delete_purchase_order' => 'Delete purchase order',
    'purchase_order_delete_success' => 'Purchase order deleted successfully',
    'order_quantity' => 'Order quantity',
    'order_date' => 'Order date',
    'max_quantity_quantity_allowed' => 'Max :quantity allowed',
    'leave_empty_to_autogenerate' => 'Leave empty to autogenerate',
    'add_edit_invoice_number' => 'Add edit invoice number',
    'keep_blank_to_autogenerate' => 'Keep blank to auto generate',
    'download_pdf' => 'Download pdf',
    'attach_pdf_in_email' => 'Attach invoice pdf in email',
    'send_sms_whatsapp_notification' => 'Send sms/whatsapp notification',
    'enable_sales_order' => 'Enable Sales Order',
    'sales_order' => 'Sales Order',
    'sales_order_help_text' => 'The sales order, sometimes abbreviated as SO, is an order issued by a business or sole trader to a customer. A sales order may be for products and/or services.',
    'add_sales_order' => 'Add Sales Order',
    'edit_sales_order' => 'Edit Sales Order',
    'shipping_note' => 'Shipping note',
    'code_1_name' => 'Code 1 name',
    'code_2_name' => 'Code 2 name',
    'code_1' => 'Code 1',
    'code_2' => 'Code 2',
    'download_paking_pdf' => 'Download packing pdf',
    'subject_type' => 'Subject Type',
    'activity_log' => 'Activity Log',
    'sell_deleted' => 'Sell Deleted',
    'so_deleted' => 'Sales Order Deleted',
    'po_deleted' => 'Purchase Order Deleted',
    'purchase_deleted' => 'Purchase Deleted',
    'contact_deleted' => 'Contact Deleted',
    'payment_deleted' => 'Payment Deleted',
    'quantity_remaining' => 'Quantity Remaining',
    'view_sale' => 'View Sell',
    'update_sale' => 'Update Sell',
    'add_sell' => 'Add Sell',
    'account_details' => 'Account details',
    'label' => 'Label',
    'prefer_payment_method' => 'Prefer payment method',
    'prefer_payment_account' => 'Prefer payment account',
    'this_will_be_shown_in_pdf' => 'Preferred method/account where customer can pay the invoice. (Useful for credit invoices)',
    'view_own_so' => 'View own sales order',
    'view_all_so' => 'View all sales order',
    'create_so' => 'Create sales order',
    'edit_so' => 'Edit sales order',
    'delete_so' => 'Delete sales order',
    'types_of_service_custom_field_help' => 'Six custom fields will be available while adding sell',
    'delete_expense' => 'Delete Expense',
    'access_all_expense' => 'Access all expenses',
    'designation' => 'Designation',
    'department' => 'Department',
    'is_default_for_contact' => 'Is default for contact',
    'individual' => 'Individual',
    'is_export' => 'Is export?',
    'export' => 'Export',
    'export_custom_field1' => 'Export custom field 1',
    'export_custom_field2' => 'Export custom field 2',
    'export_custom_field3' => 'Export custom field 3',
    'export_custom_field4' => 'Export custom field 4',
    'export_custom_field5' => 'Export custom field 5',
    'export_custom_field6' => 'Export custom field 6',
    'enable_export' => 'Enable export',
    'edit_status' => 'Edit status',
    'status_updated' => 'Status updated',
    'item_discount_label' => 'Item discount label',
    'sales_orders' => 'Sales Orders',
    'cash_denominations' => 'Cash Denominations',
    'cash_denominations_help' => 'Comma separated values Example: 100,200,500,2000',
    'denomination' => 'Denomination',
    'count' => 'Count',
    'denomination_add_help_text' => 'Add denominations in Settings -> Business Settings -> POS -> Cash Denominations',
    'order_dates' => 'Order dates',
    'by_category' => 'By Category',
    'by_brand' => 'By Brand',
    'no_brand' => 'No brand',
    'woocommerce_sync' => 'WooCommerce Sync',
    'enable' => 'Enable',
    'disable' => 'Disable',
    'cmmsn_calculation_type' => 'Commission Calculation Type',
    'invoice_value' => 'Invoice value',
    'total_payment_with_commsn' => 'Total payment with commission',
    'payments_with_cmmsn' => 'Payments with commission',
    'view_all_drafts' => 'View all drafts',
    'view_own_drafts' => 'View own drafts',
    'edit_draft' => 'Edit draft',
    'delete_draft' => 'Delete draft',
    'view_all_quotations' => 'View all quotations',
    'view_own_quotations' => 'View own quotations',
    'edit_quotation' => 'Edit quotation',
    'delete_quotation' => 'Delete quotation',
    'primary_work_location' => 'Primary work location',
    'labels_for_purchase_shipping_custom_fields' => 'Labels for purchase shipping custom fields',
    'view_own_purchase_n_stock_adjustment' => 'View own Purchase & Stock Adjustment',
    'view_all_purchase_n_stock_adjustment' => 'View all Purchase & Stock Adjustment',
    'access_all_shipments' => 'Access all shipments',
    'view_all_sale' => 'View all sell',
    'view_paid_sells_only' => 'View paid sells only',
    'view_due_sells_only' => 'View due sells only',
    'view_partially_paid_sells_only' => 'View partially paid sells only',
    'view_overdue_sells_only' => 'View overdue sells only',
    'customer_with_no_sell_one_month' => 'View customers with no sell from one month only',
    'customer_with_no_sell_three_month' => 'View customers with no sell from three months only',
    'customer_with_no_sell_six_month' => 'View customers with no sell from six months only',
    'customer_with_no_sell_one_year' => 'View customers with no sell from one year only',
    'customer_permissions_tooltip' => 'To view all customers with no sell from a specific time <b>View all customer</b> permission is required otherwise it will filter with only cusromers created by the logged in user',
    'sell_permissions_tooltip' => 'To view sells on the basis of payment status <b> View all sell</b> permission is required otherwise it will filter with only sells created by the logged in user',
    'access_pending_shipments_only' => 'Access pending shipments only',
    'access_all_sell_return' => 'Access all sell return',
    'access_own_sell_return' => 'Access own sell return',
    'proforma_heading' => 'Proforma invoice heading',
    'tooltip_proforma_heading' => 'Proforma invoice heading is used while providing Proforma to customers.',
    'line_taxes' => 'Line taxes',
    'add_additional_expenses' => 'Add additional expenses',
    'additional_expense_name' => 'Additional expense name',
    'purchase_additional_expense' => 'Purchase additional expenses',
    'qr_code' => 'QR Code',
    'show_qr_code' => 'Show QR Code',
    'fields_to_be_shown' => 'Fields to be shown',
    'business_location_address' => 'Business location address',
    'business_tax_1' => 'Business tax 1',
    'business_tax_2' => 'Business tax 2',
    'invoice_datetime' => 'Invoice Datetime',
    'total_amount_with_tax' => 'Total amount with tax',
    'total_bank_transfer' => 'Total bank transfer',
    'total_advance_payment' => 'Total advance payment',
    'commission_agent_label' => 'Commission agent label',
    'show_commission_agent' => 'Show commission agent',
    'cannot_change_role' => 'Can not change role',
    'cr' => 'CR',
    'dr' => 'DR',
    'payment_link' => 'Payment Link',
    'enable_payment_link' => 'Enable payment link',
    'payment_link_help_text' => 'By enabling users can pay invoice using payment link',
    'invoice_payment' => 'Invoice Payment',
    'payment_for_invoice_no' => 'Payment for invoice number',
    'pending_shipments' => 'Pending Shipments',
    'sources' => 'Sources',
    'stripe_secret_key' => 'Stripe secret key',
    'stripe_public_key' => 'Stripe public key',
    'edit_opening_balance' => 'Edit Opening Balance',
    'edit_fund_transfer' => 'Edit Fund Transfer',
    'edit_deposit' => 'Edit Deposit',
    'deposit_to' => 'Deposit to',
    'transfer_from' => 'Transfer from',
    'edit_account_transaction' => 'Edit account transaction',
    'delete_account_transaction' => 'Delete account transaction',
    'add_purchase_payment' => 'Add purchase payment',
    'edit_purchase_payment' => 'Edit purchase payment',
    'delete_purchase_payment' => 'Delete purchase payment',
    'add_sell_payment' => 'Add sell payment',
    'edit_sell_payment' => 'Edit sell payment',
    'delete_sell_payment' => 'Delete sell payment',
    'sales_order_heading' => 'Sales Order Heading',
    'account_balance' => 'Account Balance',
    'total_balance' => 'Total Balance',
    'account_balance_tooltip' => 'Total balance of the particular account',
    'total_balance_tooltip' => 'Total Balance of all the accounts',
    'sell_additional_expense' => 'Sell additional expenses',
    'view_export_buttons' => 'View export to buttons (csv/excel/print/pdf) on tables',
    'overall' => 'Overall',
    'show_labels' => 'Show Labels',
    'zatca_qr' => 'ZATCA (Fatoora) QR code',
    'zatca_qr_help' => 'For Saudi Arabia country',
    'customer_irrespective_of_sell' => 'View customers irrespective of their sell',
    'img_url_help_text' => 'Or URL of the image',
    'add_as_sub_cat' => 'Add as sub-category',
    'import' => 'Import',
    'quantity_required' => 'Quantity required in row :row',
    'date_ins' => 'Format: yyyy-mm-dd; Ex: 2021-11-25',
    'has_no_sell_from' => 'Has no sell from',
    'one_month' => 'One month',
    'three_months' => 'Three months',
    'six_months' => 'Six months',
    'one_year' => 'One year',
    'is_commission_agent_required' => 'Is commission agent required?',
    'mobile_already_registered' => ':mobile Already registered for :contacts',
    'automatic' => 'Automatic',
    'payment_reminder_help' => 'If enabled, payment reminder notification will be automatically sent to customer on invoice overdue',
    'new_sale_notification_help' => 'If enabled, sell notification will be automatically sent to customer on creating new sales for them',
    'is_pay_term_required' => 'Is pay term required?',
    'variation_sku' => 'Variation SKUs',
    'variation_sku_ins' => 'SKUs of each variations separated by "|" if product type is variable',
    'lot_number_instructions' => 'Only if Lot number is enabled. You can enable Lot number from <br><code>Business Settings > Purchases > Enable Lot number</code>',
    'exp_date_instructions' => 'Only if Product Expiry is enabled. You can enable Product expiry from <br><code>Business Settings > Product > Enable Product Expiry</code>',
    'payment_recovered_today' => 'Payment recovered today',
    'payment_details' => 'Payment details',
    'payments_recovered_for' => 'Payments recovered for',
    'show_base_unit_details' => 'Show base unit details (If applicable)',
    'tax_summary_label' => 'Tax summary label',
    'discounted_unit_price_label' => 'Discounted unit price label',
    'format_1' => 'Format 1',
    'format_2' => 'Format 2',
    'ledger_format' => 'Ledger format',
    'statement' => 'Statement',
    'transaction' => 'Transaction',
    'current' => 'Current',
    '1_30_days_past_due' => '1-30 days past due',
    '30_60_days_past_due' => '30-60 days past due',
    '60_90_days_past_due' => '60-90 days past due',
    'over_90_days_past_due' => 'Over 90 days past due',
    'amount_due' => 'Amount due',
    'change_return_payment_method' => 'Change return payment method',
    'change_return_payment_account' => 'Change return payment account',
    'pay_to_supplier' => 'Debit (Pay to supplier)',
    'receive_from_supplier' => 'Credit (Receive from supplier)',
    'receive_from_customer' => 'Credit (Receive from customer)',
    'pay_to_customer' => 'Debit (Pay to customer)',
    'enable_cash_denomination_on' => 'Enable cash denomination on',
    'pos_screen' => 'POS screen',
    'all_screen' => 'All screens',
    'strict_check' => 'Strict check',
    'strict_check_help' => 'If enabled payment amount must be equal to sum of cash denominations',
    'cash_denomination_error' => 'Mismatch between amount & cash denomination. Please enter correct denomination',
    'enable_cash_denomination_for_payment_methods' => 'Enable cash denomination for payment methods',
    'invalid_date_format_at' => 'Invalid date format at row :row',
    'net' => 'Net',
    'net_home_tooltip' => 'NET = TOTAL SALES - INVOICE DUE - EXPENSE',
    'ledger_discount' => 'Ledger discount',
    'edit_discount' => 'Edit Discount',
    'discount_for' => 'Discount for',
    'delivery_at' => 'Delivery at',
    'dispatch_from' => 'Dispatch From',
    'checked_by' => 'Checked by',
    'required_by' => 'Required by',
    'for_business' => 'For :business',
    'delivery_date' => 'Delivery date',
    'po_no' => 'P.O. number',
    'prepared_by' => 'Prepared by',
    'permission_denied' => 'Permission denied',
    'sell_not_found' => 'Sell not found',
    'prev_unit_price' => 'Previous unit price',
    'prev_discount' => 'Previous discount',
    'download_excel' => 'Download Excel',
    'total_items_label' => 'Total items label',
    'currency_precision' => 'Currency precision',
    'currency_precision_help' => 'Number of digits after decimal point for currency value. Example:0.00 for value 2, 0.000 for value 3, 0.0000 for value 4',
    'quantity_precision' => 'Quantity precision',
    'quantity_precision_help' => 'Number of digits after decimal point for quantity value. Example:0.00 for value 2, 0.000 for value 3, 0.0000 for value 4',
    'customer_supplier_info' => 'Customer/Supplier information',
    'enable_secondary_unit' => 'Enable secondary unit',
    'secondary_unit' => 'Second unit',
    'secondary_unit_help' => 'Allows user to enter product quantity in secondary unit during purchase/sell',
    'quantity_in_second_unit' => 'Quantity in :unit',
    'login_as_username' => 'Login as :username',
    'back_to_username' => 'Back to :username',
    'purchase_requisition' => 'Purchase Requisition',
    'add_purchase_requisition' => 'Add Purchase Requisition',
    'show_products' => 'Show products',
    'required_by_date' => 'Required by date',
    'purchase_requisition_details' => 'Purchase requisition details',
    'enable_purchase_requisition' => 'Enable Purchase Requisition',
    'purchase_requisition_help_text' => 'A purchase requisition is a document that an employee creates to request a purchase of goods or services.',
    'view_all_purchase_requisition' => 'View all purchase requisition',
    'view_own_purchase_requisition' => 'View own purchase requisition',
    'create_purchase_requisition' => 'Create purchase requisition',
    'delete_purchase_requisition' => 'Delete purchase requisition',
    'all_added_products_will_be_removed' => 'All added products will be removed',
    'required_quantity' => 'Required quantity',
    'second_quantity' => 'Second quantity',
    'show_product_description' => 'Show product description',
    'preparation_time_in_minutes' => 'Service staff timer/Preparation time (In minutes)',
    'service_staff_availability' => 'Service staff avilability',
    'will_be_available_at' => 'Will be available at',
    'mark_as_available' => 'Mark as available',
    'pause_timer' => 'Pause timer',
    'resume_timer' => 'Resume timer',
    'paused' => 'Paused',
    'service_staff_availability_status' => 'Service staff availability status',
    'refresh' => 'Refresh',
    'gst_sales_report' => 'GST sales report (India)',
    'invoice_date' => 'Invoice date',
    'gstin_of_cutomer' => 'GSTIN of customer',
    'hsn_code' => 'HSN code',
    'taxable_value' => 'Taxable value',
    'gst_purchase_report' => 'GST purchase report (India)',
    'gstin_of_supplier' => 'GSTIN of supplier',
    'format_3' => 'Format 3',
    'additional_notes' => 'Additional notes',
    'delivery_note' => 'Delivery Note',
    'add_edit_payment' => 'Add/Edit Payment',
    'is_product_image_required' => 'Is product image required?',
    'select_variation_values' => 'Select variation values',
    'show_letter_head' => 'Show letter head',
    'letter_head' => 'Letter Head',
    'letter_head_help' => 'Upload a letterhead image containing all details of your business.
         Letterhead will be added at the top of the invoices.',
    'letter_head_help2' => 'Upload only if you want to replace previous letterhead',
    'copy_quotation' => 'Copy Quotation',
    'field_type' => 'Field Type',
    'text' => 'Text',
    'datepicker' => 'Datepicker',
    'selling_price_help_text' => 'Set multiple price for products. Name different price and then update price from "Update Price" or List Products -> Actions -> Add or edit Group prices',
    'update_product_price' => 'Update Price',
    'import_export_product_price' => 'Import Export Product Price',
    'export_product_prices' => 'Export product prices',
    'price_import_instruction_1' => 'Export product prices by clicking on above button',
    'price_import_instruction_2' => 'Make changes in product price including tax & selling price groups.',
    'price_import_instruction_3' => 'Do not change any product name, sku & headers',
    'price_import_instruction_4' => 'After making changes import the file',
    'product_prices_imported_successfully' => 'Product prices imported successfully',
    'price_group_price_type_tooltip' => 'If <b>Fixed</b> - the entered price will be used. If <b>Percentage</b> - price will be that much % of default selling price',
    'dropdown' => 'Dropdown',
    'enter_dropdown_values' => 'Enter dropdown options, one option per line'
];
