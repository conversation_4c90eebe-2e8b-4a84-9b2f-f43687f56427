<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Restaurant Language Lines
    |--------------------------------------------------------------------------
    */

    'restaurant' => 'Restaurant',
    'tables' => 'Tables',
    'table' => 'Table',
    'manage_your_tables' => 'Manage restaurant tables',
    'all_your_tables' => 'All restaurant tables',
    'description' => 'Description',
    'add_table' => 'Add table',
    'edit_table' => 'Edit table',
    'table_name' => 'Table name',
    'short_description' => 'Short Description',
    'select_table' => 'Select Table',
    'show_table' => 'Show table',
    'table_label' => 'Table label',
    'show_service_staff' => 'Show service staff',
    'service_staff_label' => 'Service staff label',
    'select_waiter' => 'Select Waiter',
    'modifiers' => 'Modifiers',
    'modifier_sets' => 'Modifier Sets',
    'manage_your_modifiers' => 'Manage all restaurant modifiers',
    'all_your_modifiers' => 'All Modifiers',
    'add_modifier' => 'Add Modifier',
    'modifier_set' => 'Modifier Set',
    'modifier' => 'Modifiers',
    'edit_modifier' => 'Edit Modifier',
    'manage_products' => 'Manage Products',
    'products' => 'Products',
    'modifiers_for_product' => 'Modifiers for product',
    'kitchen' => 'Kitchen',
    'mark_as_cooked' => 'Mark as cooked',
    'manage_your_kitchen' => 'Manage your kitchen',
    'all_orders' => 'All orders',
    'order_no' => 'Order No.',
    'order_statuses' => [
        'received' => 'Received',
        'cooked' => 'Cooked',
        'served' => 'Served',
        '' => 'Received',
        'partial_cooked' => 'Partially Cooked',
        'partial_served' => 'Partially Served',
    ],
    'order_successfully_marked_cooked' => 'Order successfully marked as cooked',
    'order_successfully_marked_served' => 'Order successfully marked as served',
    'orders' => 'Orders',
    'manage_your_orders' => 'Manage your orders',
    'all_your_orders' => 'All your orders',
    'mark_as_served' => 'Mark as served',
    'placed_at' => 'Placed at',
    'order_status' => 'Order status',
    'order_details' => 'Order details',
    'refresh' => 'Refresh',
    'no_orders_found' => 'No orders found',
    'select_service_staff' => 'Select service staff',
    'service_staff' => 'Service staff',
    'kitchen_for_restaurant' => 'Kitchen (For restaurants)',
    'products_for_modifier' => 'Products for modifier',
    'is_service_staff' => 'Is service staff',
    'tooltip_tables' => 'Used mostly in restaurants, bars, salons etc',
    'tooltip_modifiers' => 'Extra items added with the main product<br>Example:Toppings, Cheese',
    'tooltip_service_staff' => 'Person who provides the designated service to customers<br> Example: Waiting Staff in restaurants, Hairdressers in salon',
    'modules_settings' => 'Modules settings',
    'bookings' => 'Bookings',
    'add_edit_view_all_booking' => 'Add/Edit/View all bookings',
    'add_edit_view_own_booking' => 'Add/Edit/View own bookings',
    'add_booking' => 'Add new booking',
    'start_time' => 'Start time',
    'end_time' => 'End time',
    'booking_not_available' => 'Already booked for <b>:customer_name</b> between <b>:booking_time_range</b>',
    'select_correspondent' => 'Select correspondent',
    'send_notification_to_customer' => 'Send Email/SMS notification to customer',
    'booking_details' => 'Booking Details',
    'correspondent' => 'Correspondent',
    'booking_starts' => 'Booking starts',
    'booking_ends' => 'Booking ends',
    'cancelled' => 'Cancelled',
    'completed' => 'Completed',
    'change_booking_status' => 'Change booking status',
    'booked' => 'Booked',
    'delete_booking' => 'Delete booking',
    'click_on_any_booking_to_view_or_change_status' => 'Click on any booking to view details or change status',
    'double_click_on_any_day_to_add_new_booking' => 'Double click on any day to add new booking',
    'todays_bookings' => "Today's Bookings",
    'hello_name' => 'Hello :name!',
    'booking_confirmed' => 'Your booking has been confirmed.',
    'table_info' => 'Table: :table',
    'booking_time_info' => 'From :from TO :to',
    'new_booking_subject' => 'New booking - :business_name',
    'customer_note' => 'Customer note',
    'table_report' => 'Table Report',
    'service_staff_report' => 'Service Staff Report',

];
