<?php

return [
    'enable_editing_product_from_purchase' => 'Bearbeiten des Produktpreises vom Kaufbildschirm aktivieren',
    'sales_commission_agent' => 'Vertreter der Verkaufskommission',
    'sales_commission_agents' => 'Agenten der Verkaufskommission',
    'disable' => 'Deaktivieren',
    'logged_in_user' => 'Angemeldeter Benutzer',
    'select_from_users_list' => 'Aus der Benutzerliste auswählen',
    'select_from_commisssion_agents_list' => 'Wählen Sie aus der Liste der Kommissionsagenten',
    'add_sales_commission_agent' => 'Verkaufskommissionär hinzufügen',
    'commission_agent_added_success' => 'Kommissionsagent erfolgreich hinzugefügt',
    'edit_sales_commission_agent' => 'Verkaufskommissionär bearbeiten',
    'commission_agent_updated_success' => 'Kommissionsagent wurde erfolgreich aktualisiert',
    'commission_agent_deleted_success' => 'Kommissionsagent erfolgreich gelöscht',
    'contact_no' => 'Kontakt Nr.',
    'commission_agent' => 'Kommissionär',
    'cmmsn_percent' => 'Prozentsatz der Verkaufsprovision (%)',
    'sales_added' => 'Umsatz hinzugefügt',
    'sales_with_commission' => 'Verkäufe mit der Kommission',
    //"total_sale_with_commission" => "Gesamtverkauf mit Kommission",
    'total_sale_commission' => 'Gesamtverkaufs-Kommission',
    'sales_item_addition_method' => 'Verkaufspositionszusatzmethode',
    'add_item_in_new_row' => 'Artikel in neuer Zeile hinzufügen',
    'increase_item_qty' => 'Artikelmenge erhöhen, wenn diese bereits existiert',
    'stock_transfers' => 'Umlagerungen',
    'all_stock_transfers' => 'Alle Umlagerungen',
    'add_stock_transfer' => 'Umlagerung hinzufügen',
    'list_stock_transfers' => 'Umlagerungen auflisten',
    'location_from' => 'Standort (von)',
    'location_to' => 'Ort (zu)',
    'shipping_charges' => 'Versandkosten',
    'stock_transfer_added_successfully' => 'Stock Transfer erfolgreich hinzugefügt',
    'stock_transfer_delete_success' => 'Umbuchung erfolgreich gelöscht',
    'stock_transfer_cannot_be_deleted' => 'Diese Umlagerung kann nicht gelöscht werden, da einige Bestände bereits verkauft sind',
    'total_transfer_shipping_charges' => 'Gesamttransfergebühren',
    'enable_inline_tax' => 'Aktivieren Sie Inline-Steuer beim Kauf und Verkauf',
    'save_n_add_opening_stock' => 'Speichern und Hinzufügen von Anfangsbeständen',
    'add_opening_stock' => 'Eröffnungskurs hinzufügen',
    'opening_stock_added_successfully' => 'Anfangsbestand erfolgreich hinzugefügt',
    'update_n_edit_opening_stock' => 'Aktualisieren und Bearbeiten des Eröffnungsbestands',
    'total_amount_exc_tax' => 'Gesamtbetrag (ohne Steuern)',
    'currency_symbol_placement' => 'Währungssymbol-Platzierung',
    'before_amount' => 'Vor dem Betrag',
    'after_amount' => 'Nach Menge',
    'barcode_label_error' => 'Nicht unterstützte Artikelnummer für den ausgewählten Barcode-Typ',
    'list_products' => 'Produkte auflisten',
    'list_expenses' => 'Ausgaben auflisten',
    'add_expiry' => 'Artikel Ablauf hinzufügen',
    'add_manufacturing_auto_expiry' => 'Herstellungsdatum und Verfallszeit hinzufügen',
    'tooltip_enable_expiry' => 'Produktablauf aktivieren. <br/> <br/><b>Artikel Ablauf hinzufügen</b>: Um nur den Artikelablauf direkt hinzuzufügen. <br/> <b>Herstellungsdatum und Verfallszeit hinzufügen</b>: Hinzufügen des Herstellungsdatums und des Verfallsdatums und Berechnung des darauf basierenden Verfallsdatums.',
    'quantity' => 'Menge',
    'on_product_expiry' => 'Bei Produktablauf',
    'keep_selling' => 'Weiterverkaufen',
    'stop_selling' => 'Stoppen Sie den Verkauf vor n Tagen',
    'tooltip_on_product_expiry' => 'Geben Sie die Aktion an, die bei Ablauf des Produkts ausgeführt werden muss. <br><br> <b>Weiterverkaufen</b>: Product will be keep on selling after expiry also. <br> <b>Stoppt den Verkauf</b>: Stoppen Sie den Verkauf des Artikels n Tage vor Ablauf.',
    'output_tax_minus_input_tax' => 'Ausgangssteuer - Vorsteuer',
    'total_purchase_discount' => 'Gesamtkaufrabatt',
    'total_sell_discount' => 'Gesamtverkaufsrabatt',
    'updated_succesfully' => 'Erfolgreich geupdated',
    'default_unit' => 'Standardeinheit',
    'enable_brand' => 'Marken aktivieren',
    'enable_category' => 'Kategorien aktivieren',
    'enable_sub_category' => 'Unterkategorien aktivieren',
    'enable_price_tax' => 'Preis- und Steuerinformationen aktivieren',
    'enable_purchase_status' => 'Kaufstatus aktivieren',
    'tooltip_enable_purchase_status' => 'Bei Deaktivierung werden alle Einkäufe als <i> empfangener Artikel markiert </i>',
    'recent_product_quantity' => 'Gehe zur Produktmenge',
    'full_screen' => 'Vollbild',
    'Uncategorised' => 'Nicht kategorisiert',
    'no_products_to_display' => 'Keine Produkte zum Anzeigen',
    'item_out_of_stock' => 'Produkt ausverkauft',
    'go_back' => 'Geh zurück',
    'disable_pay_checkout' => 'Deaktivieren Sie bezahlen & Kasse',
    'disable_draft' => 'Entwurf deaktivieren',
    'disable_express_checkout' => 'Express Checkout deaktivieren',
    'hide_product_suggestion' => 'Produktvorschlag nicht anzeigen',
    'hide_recent_trans' => 'Neueste Transaktionen nicht anzeigen',
    'pos_settings' => 'POS-Einstellungen',
    'disable_discount' => 'Rabatt deaktivieren',
    'disable_order_tax' => 'Bestellsteuer deaktivieren',
    'customer_groups' => 'Kundengruppen',
    'customer_group' => 'Kundengruppe',
    'all_your_customer_groups' => 'Alle Kundengruppen',
    'add_customer_group' => 'Kundengruppe hinzufügen',
    'customer_group_name' => 'Kundengruppenname',
    'calculation_percentage' => 'Berechnungsprozentsatz (%)',
    'tooltip_calculation_percentage' => '<b> Verkaufspreis = Verkaufspreis Set Für das Produkt + Berechnungsprozentsatz </b> <br/> <br/> Sie können den Prozentsatz als positiv für die Erhöhung und den negativen für die Senkung des Verkaufspreises angeben.',
    'success' => 'Erfolg',
    'edit_customer_group' => 'Kundengruppe bearbeiten',
    'customer_groups_report' => 'Kundengruppenbericht',
    'none' => 'Keiner',
    'enable_imei_or_sr_no' => 'Aktivieren Sie IMEI oder Seriennummer',
    'tooltip_sr_no' => 'Aktivieren oder deaktivieren Sie das Hinzufügen von Produkt-IMEI oder Seriennummer beim Hinzufügen von Produkten im POS-Bildschirm',
    'description' => 'Beschreibung',
    'sell_line_description_help' => 'Produkt hinzufügen IMEI, Seriennummer oder andere Informationen hier.',
    'unit_cost_before_discount' => 'Stückkosten (vor Rabatt)',
    'discount_percent' => 'Rabatt Prozent',
    'application_tour' => 'Anwendungstour',
    'profile' => 'Profil',
    'sign_out' => 'Ausloggen',
    'my_profile' => 'Mein Profil',
    'Admin' => 'Administrator',
    'Cashier' => 'Kassierer',
    'both_supplier_customer' => 'Beide (Lieferant und Kunde)',
    'months' => 'Monate',
    'days' => 'Tage',
    'suppliers' => 'Lieferanten',
    'customers' => 'Kunden',
    'enter_product_name_to_print_labels' => 'Geben Sie den Produktnamen ein, um Etiketten zu drucken',
    'manage_product_variations' => 'Produktvarianten verwalten',
    'all_variations' => 'Alle Variationen',
    'values' => 'Werte',
    'add_variation' => 'Variation hinzufügen',
    'variation_name' => 'Variantenname',
    'add_variation_values' => 'Änderungswerte hinzufügen',
    'edit_variation' => 'Variation bearbeiten',
    'received' => 'Empfangen',
    'pending' => 'Steht aus',
    'ordered' => 'Bestellt',
    'search_product_placeholder' => 'Produktname / Artikelnummer / Scan-Barcode eingeben',
    'fixed' => 'Fest',
    'percentage' => 'Prozentsatz',
    'select_location' => 'Ort auswählen',
    'add_description' => 'Beschreibung hinzufügen',
    'cash' => 'Kasse',
    'card' => 'Karte',
    'cheque' => 'Prüfen',
    'bank_transfer' => 'Banküberweisung',
    'other' => 'Andere',
    'payment' => 'Zahlung',
    'total_items' => 'Gesamtanzahl',
    'total_paying' => 'Gesamtbezahlung',
    'balance' => 'Balance',
    'payment_method' => 'Bezahlverfahren',
    'card_no' => 'Kartennummer',
    'card_holder_name' => 'Name des Karteninhabers',
    'card_transaction_no' => 'Kartentransaktionsnummer',
    'card_type' => 'Speicherkarten-Typ',
    'month' => 'Monat',
    'year' => 'Jahr',
    'security_code' => 'Sicherheitscode',
    'cheque_no' => 'Scheck-Nummer',
    'bank_account_number' => 'Bankkontonummer',
    'paid' => 'Bezahlt',
    'due' => 'Fällig',
    'partial' => 'Teilweise',
    'no_of_products' => 'Anzahl der Produkte',
    'select_a_date_range' => 'Wählen Sie einen Datumsbereich',
    'shortcut_help' => "Abkürzung sollten die Namen der Schlüssel sein, getrennt durch '+'",
    'example' => 'Beispiel',
    'available_key_names_are' => 'Verfügbare Schlüsselnamen sind',
    'add_new_product' => 'Neues Produkt hinzufügen',
    'invoice_logo_help' => 'Max :max_size, jpeg, gif, nur png-Formate.',
    'invoice_logo_help2' => 'Nur hochladen, wenn Sie das vorherige Logo ersetzen möchten',
    'printer_name_help' => 'Kurzer beschreibender Name zur Druckererkennung',
    'char_per_line_help' => 'Anzahl der Zeichen, die pro Zeile gedruckt werden können',
    'ip_address_help' => 'IP-Adresse für die Verbindung zum Drucker',
    'port_help' => 'Der meiste Drucker funktioniert auf Port 9100',
    'combination_of_taxes' => 'Kombination mehrerer Steuern',
    'product_purchase_report' => 'Produkt Kauf Bericht',
    'unit_perchase_price' => 'Einheit Kaufpreis',
    'search_product' => 'Produkt suchen',
    'product_sell_report' => 'Produktverkaufsbericht',
    'unit_sale_price' => 'Verkaufseinheitspreis',
    'contact_id' => 'Kontakt-ID',
    'category_code_help' => 'Kategoriecode ist gleich wie <b> HSN-Code </b>',
    'sub_heading_line' => 'Unterüberschrift :_number_',
    'date_label' => 'Datumsbezeichnung',
    'client_id_label' => 'Kunden-ID-Label',
    'product_label' => 'Produktetikett',
    'qty_label' => 'Mengenlabel',
    'unit_price_label' => 'Stückpreisschild',
    'subtotal_label' => 'Zwischensumme Label',
    'product_details_to_be_shown' => 'Produktdetails werden angezeigt',
    'show_brand' => 'Marke zeigen',
    'show_sku' => 'Zeige SKU',
    'show_cat_code' => 'Kategoriecode anzeigen',
    'show_client_id' => 'Kundennummer anzeigen',
    'show_time_with_date' => 'Zeige Zeit mit Datum',
    'show_sale_description' => 'Verkaufsbeschreibung anzeigen',
    'product_imei_or_sn' => '(Produkt IMEI oder Seriennummer)',
    'purchase_already_exist' => 'Produkt kann nicht gelöscht werden, da Käufe im Zusammenhang mit diesem Produkt existieren',
    'opening_stock_sold' => 'Produkt kann nicht gelöscht werden, weil einige Bestände verkauft werden',
    'stock_adjusted' => 'Produkt kann nicht gelöscht werden, weil einige Bestände angepasst werden',
    'product_delete_success' => 'Produkt erfolgreich gelöscht',
    'manage_products' => 'Verwalten Sie Ihre Produkte',
    'all_products' => 'Alle Produkte',
    'login' => 'Anmeldung',
    'register' => 'Registrieren',
    'username' => 'Nutzername',
    'password' => 'Passwort',
    'remember_me' => 'Erinnere dich an mich',
    'forgot_your_password' => 'Haben Sie Ihr Passwort vergessen?',
    'reset_password' => 'Passwort zurücksetzen',
    'email_address' => 'E-Mail-Addresse',
    'send_password_reset_link' => 'Link zum Zurücksetzen des Kennworts senden',
    'instructions' => 'Anleitung',
    'instruction_line1' => 'Befolgen Sie die Anweisungen sorgfältig, bevor Sie die Datei importieren.',
    'instruction_line2' => 'Die Spalten der CSV-Datei sollten in der folgenden Reihenfolge sein.',
    'col_no' => 'Spaltennummer',
    'col_name' => 'Spaltenname',
    'instruction' => 'Anweisung',
    'required' => 'Erforderlich',
    'optional' => 'Wahlweise',
    'name_ins' => 'Produktname',
    'brand_ins' => 'Name der Marke',
    'brand_ins2' => 'Wenn nicht gefunden, wird eine neue Marke mit dem gegebenen Namen erstellt',
    'unit_ins' => 'Name der Einheit',
    'category_ins' => 'Name der Kategorie',
    'category_ins2' => 'Wenn nicht gefunden, wird eine neue Kategorie mit dem angegebenen Namen erstellt',
    'sub_category_ins' => 'Name der Unterkategorie',
    'sub_category_ins2' => 'Wenn nicht gefunden, wird eine neue Unterkategorie mit dem gegebenen Namen unter der übergeordneten Kategorie erstellt',
    'sku_ins' => 'Product SKU. Wenn diese Option leer ist, wird automatisch eine SKU generiert',
    'barcode_type_ins' => 'Barcode-Typ für das Produkt.',
    'barcode_type_ins2' => 'Derzeit unterstützt',
    'default' => 'Standard',
    'manage_stock_ins' => 'Aktivieren oder Deaktivieren der Lagerverwaltung',
    'alert_qty_ins' => 'Erforderlich, wenn Lagerbestand verwalten 1 ist',
    'expires_in_ins' => 'Produktablaufdauer (nur in Zahlen)',
    'expire_period_unit' => 'Verfallszeiteinheit',
    'available_options' => 'Verfügbare Optionen',
    'expire_period_unit_ins' => 'Einheit für die Ablauffrist',
    'applicable_tax_ins' => 'Name des Steuersatzes',
    'variation_name_ins' => 'Erforderlich, wenn der Produkttyp variabel ist',
    'variation_name_ins2' => 'Name der Variation (Bsp .: Größe, Farbe usw.)',
    'variation_values_ins' => 'Erforderlich, wenn der Produkttyp variabel ist',
    'variation_values_ins2' => "Werte für die Variation getrennt durch '|' '| (zB: Rot | Blau | Grün)",
    'purchase_price_inc_tax' => 'Kaufpreis (einschließlich Steuern)',
    'purchase_price_inc_tax_ins1' => 'Erforderlich, wenn der Kaufpreis ohne Steuer nicht angegeben wird',
    'purchase_price_inc_tax_ins2' => "Kaufpreis (inkl. MwSt.) (Nur in Zahlen) <br> <br> Für variable Produkte '|' getrennte Werte mit der gleichen Reihenfolge wie die Variationswerte <br> (Bsp .: 84 | 85 | 88) ",
    'purchase_price_exc_tax' => 'Kaufpreis (ohne Steuern)',
    'purchase_price_exc_tax_ins1' => 'Erforderlich, wenn der Kaufpreis inklusive Steuern nicht angegeben ist',
    'purchase_price_exc_tax_ins2' => "Kaufpreis (ohne Steuern) (nur in Zahlen) <br> <br> Für variable Produkte '|' getrennte Werte mit der gleichen Reihenfolge wie die Variationswerte <br> (Bsp .: 84 | 85 | 88) ",
    'profit_margin_ins' => 'Gewinnspanne (nur in Zahlen)',
    'profit_margin_ins1' => 'Wenn eine leere Standardgewinnmarge für das Geschäft verwendet wird',
    'selling_price' => 'Verkaufspreis',
    'selling_price_ins' => 'Verkaufspreis (nur in Zahlen)',
    'selling_price_ins1' => 'Wenn der leere Verkaufspreis mit dem angegebenen Kaufpreis und der anwendbaren Steuer berechnet wird',
    'opening_stock' => 'Anfangsbestand',
    'opening_stock_ins' => 'Eröffnungsbestand (nur in Zahlen)',
    'only_applicable_to_single_product' => 'Gilt nur für ein einzelnes Produkt',
    'purchase_delete_success' => 'Kauf wurde erfolgreich gelöscht',
    'location_ins' => 'Wenn ein leerer erster Unternehmensstandort verwendet wird',
    'location_ins1' => 'Name des Unternehmensstandortes',
    'expiry_date' => 'Verfallsdatum',
    'expiry_date_ins' => 'Stock Ablaufdatum <br><b>Format: mm-dd-yyyy; Ex: 11-25-2018</b>',
    'enable_lot_number' => 'Aktivieren Sie die Losnummer',
    'tooltip_enable_lot_number' => 'Auf diese Weise können Sie für jede Einkaufszeile im Kaufbildschirm die Losnummer eingeben',
    'lot_number' => 'Chargennummer',
    'enable_racks' => 'Aktivieren Sie Racks',
    'tooltip_enable_racks' => 'Aktivieren Sie diese Option, um beim Hinzufügen von Produkten Rackdetails eines Produkts für verschiedene Unternehmensstandorte hinzuzufügen',
    'rack_details' => 'Gestelldetails',
    'tooltip_rack_details' => 'Geben Sie Details für den Standort des Produkts im Geschäft für verschiedene Geschäftsstandorte ein.',

    'updated_success' => 'Erfolgreich geupdated',
    'added_success' => 'Erfolgreich hinzugefügt',
    'deleted_success' => 'Erfolgreich gelöscht',
    'enable_disable_modules' => 'Aktivieren / Deaktivieren von Modulen',
    'opening_stock_help_text' => "<br><br>Bei variablen Produkten separate Bestandsmengen mit '|' <br>(Ex: 100|150|200)",
    'applicable_tax_help' => '<br><br/>Wenn der Kaufpreis (ohne Steuern) nicht gleich ist wie <br/>Kaufpreis (inkl. Steuern) <br/>dann müssen Sie den Steuersatznamen angeben.',
    'sale_delete_success' => 'Verkauf erfolgreich gelöscht',
    'click_to_edit' => 'Zum Bearbeiten anklicken',
    'click_to_delete' => 'Klicken Sie, um zu löschen',
    'quotation_heading' => 'Zitat Überschrift',
    'quotation' => 'Zitat',
    'quotation_no_prefix' => 'Zitat Nr. Etikette',
    'tooltip_quotation_heading' => 'Die Überschrift Zitat oder Schätzungen wird verwendet, während den Kunden ein Angebot unterbreitet wird.',
    'quotation_added' => 'Angebot wurde erfolgreich hinzugefügt',
    'quotation_updated' => 'Angebot wurde erfolgreich aktualisiert',
    'product_sold_details_register' => 'Details der verkauften Produkte',
    'grand_total' => 'Gesamtsumme',
    'change_return' => 'Ändern Sie die Rückgabe',
    'date_format' => 'Datumsformat',
    'time_format' => 'Zeitformat',
    '12_hour' => '12 Stunden',
    '24_hour' => '24 Stunden',
    'list_quotations' => 'Zitate auflisten',
    'list_drafts' => 'Listen Entwürfe',
    'you_cannot_delete_this_contact' => 'Der Kontakt kann nicht gelöscht werden - Für den Kontakt existieren bereits Transaktionen.',
    'enable_row' => 'Zeile aktivieren',
    'enable_position' => 'Position aktivieren',
    'rack' => 'Gestell',
    'row' => 'Reihe',
    'position' => 'Position',
    'weight' => 'Gewicht',
    'rack_help_text' => "Rack Details getrennt durch '|' für verschiedene Geschäftsstandorte in Serie. <br/> (Bsp .: R1 | R5 | R12) ",
    'row_help_text' => "Zeilendetails getrennt durch '|' für verschiedene Geschäftsstandorte in Serie. <br/> (Bsp .: ROW1 | ROW2 | ROW3) ",
    'position_help_text' => "Positionsdetails getrennt durch '|' für verschiedene Geschäftsstandorte in Serie. <br/> (Bsp .: POS1 | POS2 | POS3) ",
    'import_opening_stock' => 'Importöffnungsbestand',
    'tooltip_import_opening_stock' => 'Diese Funktion wird verwendet, um Anfangsbestände bereits hinzugefügter Produkte zu importieren. Wenn die Produkte nicht im System hinzugefügt werden, ist es ratsam, Importprodukte zu verwenden, um Produktdetails mit dem Anfangsbestand hinzuzufügen.',
    'design' => 'Design',
    'express_checkout_cash' => 'Kasse',
    'express_checkout_card' => 'Karte',
    'checkout_multi_pay' => 'Mehrfachbezahlung',
    'tooltip_checkout_multi_pay' => 'Checkout mit mehreren Zahlungsmethoden',
    'tooltip_express_checkout_card' => 'Express Checkout mit Karte',
    'card_transaction_details' => 'Kartentransaktionsdetails',
    'client_tax_label' => 'Kundensteuer-Kennzeichen',
    'cat_code_label' => 'Kategorie- oder HSN-Code-Label',
    'list_sell_return' => 'List Sell Return',
    'sell_return' => 'Verkauf zurück',
    'layout_credit_note' => 'Gutschrift / Rücksendeinformationen verkaufen',
    'cn_heading' => 'Überschrift',
    'cn_no_label' => 'Referenznummer',
    'cn_amount_label' => 'Gesamtmenge',
    'custom_field' => 'Benutzerdefiniertes Feld :number',
    'website' => 'Webseite',
    'total_credit_amt' => 'Gesamtkreditbetrag',
    'unit_sell_price' => 'Verkaufspreis der Einheit',
    'prefixes' => 'Präfixe',
    'purchase_order' => 'Bestellung',
    'stock_transfer' => 'Umlagerung',
    'purchase_payment' => 'Kaufzahlung',
    'sell_payment' => 'Zahlung verkaufen',
    'location_id' => 'Standort-ID',
    'add_edit_opening_stock' => 'Anfangsbestand hinzufügen oder bearbeiten',
    'expiry_date_will_be_changed_in_pl' => 'Ablaufdatum der Kaufzeile wird geändert',
    'remove_from_stock' => 'Aus Vorrat entfernen',
    'stock_removed_successfully' => 'Bestand erfolgreich entfernt',
    'product_image' => 'Produktbild',
    'aspect_ratio_should_be_1_1' => 'Das Seitenverhältnis sollte 1: 1 sein',
    'previous_image_will_be_replaced' => 'Das zuvor hochgeladene Bild wird ersetzt',
    'all_category' => 'Alle Kategorien',
    'all_brands' => 'Alle Marken',
    'backup' => 'Backup',
    'download_complete_backup' => 'Laden Sie das vollständige Backup herunter',
    'backup_doesnt_exist' => 'Backup existiert nicht',
    'lot_report' => 'Los Bericht',
    'purchase_payment_report' => 'Zahlungsbericht kaufen',
    'paid_on' => 'Bezahlt am',
    'purchase' => 'Kauf',
    'cheque_no' => 'Überprüfen Nein',
    'card_transaction_no' => 'Karten Transaktionsnummer',
    'bank_account_no' => 'Bankkontonummer',
    'sell_payment_report' => 'Verkaufsbericht verkaufen',
    'restaurant' => 'Restaurant',
    'user_type' => 'Benutzertyp',
    'product_custom_field1' => 'Benutzerdefiniertes Feld1',
    'product_custom_field2' => 'Benutzerdefiniertes Feld2',
    'product_custom_field3' => 'Benutzerdefiniertes Feld3',
    'product_custom_field4' => 'Benutzerdefiniertes Feld4',
    'image' => 'Bild',
    'image_help_text' => 'Bildname mit Erweiterung.<br/> (Der Bildname muss auf den Server hochgeladen werden: Pfad )',
    'tooltip_kitchen' => 'Dies ist der Küchenbildschirm. Hier können Bestelldetails angezeigt und Bestellungen als gekocht markiert werden.',
    'tooltip_serviceorder' => 'Dies ist der Service-Mitarbeiter-Bildschirm. Servicemitarbeiter können diesen Bildschirm verwenden, um alle Bestellungen für sie anzuzeigen und die Bestellung als geliefert zu markieren.',
    'sales_person_label' => 'Verkäufer-Label',
    'show_sales_person' => 'Verkaufsperson anzeigen',
    'decimal_value_not_allowed' => 'Dezimalwert nicht erlaubt',
    'theme_color' => 'Thema Farbe',
    'quantity_error_msg_in_lot' => 'Nur :qty :Einheit im ausgewählten Los verfügbar',
    'show_product_expiry' => 'Produktablauf anzeigen',
    'show_lot_number' => 'Show lot number',
    'expiry' => 'Ablauf',
    'lot' => 'Menge',
    'lot_n_expiry' => 'Lot & Verfall',
    'pos_edit_product_price_help' => 'Produkt bearbeiten Einzelpreis und Steuern',
    'name' => 'Name',
    'payment_type' => 'Zahlungsart',
    'manage_payment_account' => 'Verwalten Sie Ihr Zahlungskonto',
    'all_payments' => 'Alle Zahlungen',
    'payment_note' => 'Zahlungshinweis',
    'payment_account_deleted_success' => 'Zahlungskonto wurde erfolgreich gelöscht',
    'payment_account_updated_success' => 'Zahlungskonto wurde erfolgreich aktualisiert',
    'payment_gateway' => 'Online-Zahlungsgateway',
    'payment_account_success' => 'Zahlungskonto wurde erfolgreich hinzugefügt',
    'payment_account' => 'Zahlungskonto',
    'tooltip_sell_product_column' => 'Klicken <i>Produktname</i> um Preis, Rabatt und Steuern zu bearbeiten. <br/>Klicken <i>Kommentar-Symbol</i> Geben Sie die Seriennummer / IMEI oder eine zusätzliche Notiz ein.<br/><br/>Klicken <i>Modifikatorsymbol</i>(falls aktiviert) für Modifikatoren',
    'credit_limit' => 'Kreditlimit',
    'credit_limit_help' => 'Bleib leer, für kein Limit',
    'cutomer_credit_limit_exeeded' => 'Kundenkreditlimit überschritten <br>:credit_limit',
    'custom_payment_1' => 'Kundenspezifische Zahlung 1',
    'custom_payment_2' => 'Kundenspezifische Zahlung 2',
    'custom_payment_3' => 'Kundenspezifische Zahlung 3',
    'transaction_no' => 'Transaktionsnummer',
    'file' => 'Datei',
    'size' => 'Größe',
    'date' => 'Datum',
    'age' => 'Alter',
    'create_new_backup' => 'Neue Sicherung erstellen',
    'all_sales' => 'Alle Verkäufe',
    'opening_balance' => 'Anfangsbestand',
    'opening_balance_due' => 'Eröffnungssaldo fällig',
    'import_contacts' => 'Kontakte importieren',
    'contact_type_ins' => '<strong> Verfügbare Optionen: Kunde, Lieferant & beide </strong>',
    'contact_id_ins' => 'Leer lassen, um die Kontakt-ID automatisch zu generieren',
    'required_if_supplier' => 'Erforderlich, wenn der Kontakttyp Lieferant oder beides ist',
    'pay_term_period_ins' => 'Verfügbare Optionen: Tage und Monate',
    'your_username_will_be' => 'Ihr Benutzername wird sein',
    'currency_exchange_rate' => 'Wechselkurs',
    'select_all' => 'Wählen Sie Alle',
    'deselect_all' => 'Alle abwählen',
    'duplicate_product' => 'Duplikatprodukt',
    'delete_selected' => 'Ausgewählte löschen',
    'no_row_selected' => 'Keine Zeile ausgewählt',
    'duplicate_sell' => 'Doppelter Verkauf',
    'duplicate_sell_created_successfully' => 'Doppelter Verkauf erfolgreich erstellt',
    'modules' => 'Module',
    'theme' => 'Thema',
    'account' => 'Konto',
    'expense_payment' => 'Spesenzahlung',
    'expense' => 'Aufwand',
    'disabled_in_demo' => 'Funktion in Demo deaktiviert !!',
    'selling_price_group' => 'Verkaufspreisgruppe',
    'all_selling_price_group' => 'Alle Verkaufspreisgruppe',
    'add_selling_price_group' => 'Verkaufspreisgruppe hinzufügen',
    'edit_selling_price_group' => 'Verkaufspreisgruppe bearbeiten',
    'access_selling_price_groups' => 'Zugriff auf Verkaufspreisgruppen',
    'save_n_add_selling_price_group_prices' => 'Sparen und Hinzufügen von Verkaufspreisgruppenpreisen',
    'add_selling_price_group_prices' => 'Gruppenpreise hinzufügen oder bearbeiten',
    'default_selling_price_inc_tax' => 'Default Selling Price (Inc. Tax)',
    'variation' => 'Variation',
    'price_group' => 'Preisgruppe',
    'group_price' => 'Gruppenpreis',
    'default_selling_price' => 'Standardverkaufspreis',
    'price_group_help_text' => 'Verkaufspreisgruppe, in der Sie verkaufen möchten',
    'group_prices' => 'Gruppenpreise',
    'view_group_prices' => 'Gruppenpreise anzeigen',
    'save_n_add_another' => 'Speichern und weitere hinzufügen',
    'update_n_add_another' => 'Aktualisieren und ein weiteres hinzufügen',
    'subtotal_editable' => 'Zwischensumme editierbar',
    'subtotal_editable_help_text' => 'Aktivieren Sie diese Option, um das Feld für die Zwischensumme für jedes Produkt im Bildschirm POS editierbar zu machen.',
    'notification_templates' => 'Benachrichtigungsvorlagen',
    'new_sale' => 'Neuer Verkauf',
    'payment_reminder' => 'Payment Remider',
    'payment_received' => 'Zahlung erhalten',
    'new_booking' => 'Neue Buchung',
    'new_order' => 'Neue Ordnung',
    'payment_paid' => 'Zahlung bezahlt',
    'items_received' => 'Erhaltene Artikel',
    'items_pending' => 'Ausstehende Elemente',
    'customer_notifications' => 'Kundenbenachrichtigungen',
    'supplier_notifications' => 'Lieferantenbenachrichtigungen',
    'email_subject' => 'E-Mail Betreff',
    'email_body' => 'Nachrichtentext',
    'sms_body' => 'SMS Body',
    'available_tags' => 'Verfügbare Tags',
    'send_notification' => 'Benachrichtigung senden',
    'new_sale_notification' => 'Neue Verkaufsbenachrichtigung',
    'send_email_only' => 'Nur E-Mail senden',
    'send_sms_only' => 'Nur SMS senden',
    'send_both_email_n_sms' => 'Senden Sie sowohl E-Mail als auch SMS',
    'to' => 'Zu',
    'mobile_number' => 'Handynummer',
    'send' => 'Senden',
    'notification_sent_successfully' => 'Benachrichtigung erfolgreich gesendet',
    'payment_received_notification' => 'Benachrichtigung über Zahlungseingang senden',
    'send_payment_reminder' => 'Senden Sie einen Zahlungsreiter',
    'new_order_notification' => 'Benachrichtigung über neue Bestellung',
    'item_received_notification' => 'Benachrichtigung über empfangene Objekte',
    'item_pending_notification' => 'Benachrichtigung über ausstehende Artikel',
    'payment_paid_notification' => 'Zahlungsbenachrichtigung',
    'mail_host' => 'Wirt',
    'mail_port' => 'Hafen',
    'mail_username' => 'Nutzername',
    'mail_password' => 'Passwort',
    'mail_encryption' => 'Verschlüsselung',
    'mail_from_address' => 'Von der Adresse',
    'mail_from_name' => 'Von Namen',
    'mail_encryption_place' => 'tls / ssl',
    'email_settings' => 'Email Einstellungen',
    'sms_settings' => 'SMS-Einstellungen',
    'send_to_param_name' => 'Senden an Parametername',
    'msg_param_name' => 'Name des Nachrichtenparameters',
    'sms_settings_param_key' => 'Parameter: Nummerntaste',
    'sms_settings_param_val' => 'Parameter: Zahlenwert',
    'sending' => 'Senden',
    'request_method' => 'Anforderungsmethode',
    'purchase_return' => 'Rückgabe',
    'return_quantity' => 'Rückgabemenge',
    'return_subtotal' => 'Teilwert zurückgeben',
    'quantity_left' => 'Restmenge',
    'return_total' => 'Gesamtbetrag',
    'total_return_tax' => 'Gesamtsteuer',
    'purchase_return_added_success' => 'Kaufrendite erfolgreich hinzugefügt',
    'list_purchase_return' => 'Kaufrückkauf auflisten',
    'all_purchase_returns' => 'Alle Kaufretouren',
    'parent_purchase' => 'Kauf von Eltern',
    'purchase_return_details' => 'Details zur Kaufrückgabe',
    'return_date' => 'Rückflugdatum',
    'parent_sale' => 'Elternverkauf',
    'sell_quantity' => 'Verkaufsmenge',
    'total_return_discount' => 'Total Return Discount',
    'sell_return_details' => 'Rückgabedetails verkaufen',
    'sell_details' => 'Verkauf Details',
    'return_discount' => 'Retourenrabatt',
    'total_unit_transfered' => 'Summe übertragene Einheit',
    'total_unit_adjusted' => 'Angepasste Gesamtmenge',
    'return_exist' => 'Msgstr Rückgabe für die Transaktion vorhanden, bearbeiten Sie stattdessen die Rückgabe.',
    'synced_from_woocommerce' => 'Von Woocommerce synchronisiert',
    'available_stock_expired' => 'ODER der verfügbare Bestand ist abgelaufen.',
    'sell_payments' => 'Berechtigung zum Hinzufügen / Bearbeiten / Löschen von Zahlungen in der Liste Verkauf / Listenkasse.',
    'purchase_payments' => 'Berechtigung zum Hinzufügen / Bearbeiten / Löschen von Zahlungen in Listenkäufen.',
    'sell.payments' => 'Zahlungen hinzufügen / bearbeiten / löschen',
    'purchase.payments' => 'Zahlungen hinzufügen / bearbeiten / löschen',
    'view_payment' => 'Zahlung anzeigen',
    'card_number' => 'Kartennummer',
    'transaction_number' => 'Transaktionsnummer',
    'card_holder_name' => 'Name des Karteninhabers',
    'card_transaction_number' => 'Kartentransaktion Nein',
    'cheque_number' => 'Scheck-Nummer',
    'commsn_percent_help' => 'Wird nur verwendet, wenn für Sales Commission Agent Type die Einstellung Angemeldeter Benutzer oder Aus Benutzerliste auswählen ausgewählt ist.',
    'max_amount_to_be_paid_is' => 'Maximal zu zahlender Betrag ist: Betrag',
    'adjusted_for' => 'Angepasst für',
    'some_qty_returned' => 'Einige Mengen werden von diesem Kauf zurückgegeben',
    'total_purchase_return' => 'Gesamtkaufrendite',
    'total_purchase_return_paid' => 'Gesamter Kaufpreis',
    'total_purchase_return_inc_tax' => 'Gesamterkaufsrendite einschließlich Steuern',
    'purchase_sell_report_formula' => 'Gesamt (Verkauf - Verkauf - Kauf - Kauf)',
    'purchase_return_due' => 'Kaufrückgabe fällig',
    'total_purchase_return_due' => 'Gesamter fälliger Kauf',
    'receive_purchase_return_due' => 'Erhaltene Kaufrückgabe fällig',
    'some_qty_returned_from_sell' => 'Einige Mengen werden von diesem Verkauf zurückgegeben',
    'sell_due' => 'Verkaufen aufgrund',
    'sell_return_due' => 'Rendite zurück verkaufen',
    'total_sell_return_due' => 'Gesamtverkaufsrendite fällig',
    'pay_sell_return_due' => 'Pay Sell Return Due',
    'total_sell_return' => 'Gesamtverkaufsrendite',
    'total_sell_return_paid' => 'Gesamtverkaufsrendite bezahlt',
    'total_sell_return_inc_tax' => 'Gesamtverkaufsrendite einschließlich Steuern',
    'tooltip_columnize_taxes_heading' => 'Geben Sie den Steuernamen für Überschriften ein. Überschriften sollten im Steuernamen vorhanden sein. Überschriften können zum Beispiel CGST, SGST, IGST & CESS sein % usw',
    'sales_payment_dues' => 'Verkäufe fällig',
    'purchase_payment_dues' => 'Kaufzahlung fällig',
    'tooltip_sales_payment_dues' => "Ausstehende Zahlung für Verkäufe. <br/> <small class='text-muted'> Basierend auf der Zahlungsfrist der Rechnung. <br/> Zeigt an, dass Zahlungen in weniger als 7 Tagen eingehen. </small>",
    'edit_product_price_from_sale_screen' => 'Produktpreis im Bildschirm Verkauf bearbeiten',
    'edit_product_discount_from_sale_screen' => 'Produktrabatt vom Bildschirm Verkauf bearbeiten',
    'autosend_email' => 'Automatisch E-Mail senden',
    'autosend_sms' => 'SMS automatisch senden',
    'enable_selected_contacts' => 'Ausgewählte Kontakte einschränken',
    'tooltip_enable_selected_contacts' => 'Beschränken Sie den Zugriff auf ausgewählte Kontakte in das Suchfeld Verkäufer / Einkaufskunde / Anbieter ',
    'selected_contacts' => 'Kontakte auswählen',
    'view_role' => 'Rolle anzeigen',
    'delete_role' => 'Rolle löschen',
    'mail_driver' => 'Mail Driver',
    'disable_suspend_sale' => 'Verkauf aussetzen deaktivieren',
    'suspend' => 'Aussetzen',
    'suspend_note' => 'Notiz aussetzen',
    'suspend_sale' => 'Verkauf unterbrechen',
    'view_suspended_sales' => 'Aussetzung von Verkäufen anzeigen',
    'suspended_sales' => 'Aussetzung des Verkaufs',
    'tooltip_suspend' => 'Verkauf unterbrechen (Pause)',
    'business_telephone' => 'Geschäftskontaktnummer',
    'accept_terms_and_conditions' => 'AGB akzeptieren',
    'terms_conditions' => 'Terms & amp; Bedingungen',
    'product_description' => 'Produktbeschreibung',
    'click_here' => 'Klick hier',
    'for_more_info' => 'Für mehr Information',
    'username_help' => 'Leer lassen, um den Benutzernamen automatisch zu generieren',
    'status_for_user' => 'Ist aktiv ?',
    'tooltip_enable_user_active' => 'Aktivieren / Deaktivieren, um einen Benutzer zu aktivieren / deaktivieren.',
    'user_inactive' => 'Sorry, Konto ist inaktiv',
    'calculator' => 'Taschenrechner',
    'fields_for_customer_details' => 'Felder für Kundendaten',
    'show_previous_bal_due' => 'Gesamtsumme fällig stellen (Alle Verkäufe)',
    'previous_bal_due_help' => 'Aktivieren Sie dieses Feld, um die Summe der Saldobeträge für alle Verkäufe des Kunden anzuzeigen, falls vorhanden',
    'all_sales' => 'Alle Verkäufe',
    'current_sale' => 'Aktueller Verkauf',
    'view_invoice_url' => 'Rechnungs-URL anzeigen',
    'copy' => 'Kopieren',
    'extra_tags' => 'Zusätzliche Tags',
    'date_time_format' => 'Datum Zeitformat',
    'date_time_format_help' => "Geben Sie Datum und Uhrzeit in ein <a target='_blank' href='http://php.net/manual/en/function.date.php'>PHP-Datetime-Format</a>. Bei leerem Geschäftsdatum wird das Uhrzeitformat angewendet",
    'all' => 'Alles',
    'detailed' => 'Detailliert',
    'grouped' => 'Gruppiert',
    'disable_recurring_invoice' => 'Wiederkehrende Rechnung deaktivieren',
    'is_recurring' => 'Wiederholt sich',
    'recurring_invoice_help' => 'Wenn abonniert wird, wird diese Rechnung in regelmäßigen Abständen automatisch generiert. <br> Sie können diese Funktion in <code> Einstellungen > Geschäftseinstellungen > Modules </code> deaktivieren',
    'recurring_invoice' => 'Wiederkehrende Rechnung',
    'years' => 'Jahre',
    'no_of_repetitions' => 'Anzahl der Wiederholungen',
    'recur_repetition_help' => 'Wenn die leere Rechnung unendlich oft erstellt wird',
    'recurring_invoice_message' => 'Neue Rechnung für Abonnement-Nr. Generiert: <i>:subscription_no</i>, Rechnungsnummer: <i>:invoice_no</i>',
    'notifcation_count_msg' => 'Sie haben :count Benachrichtigungen',
    'no_notifications_found' => 'Keine Benachrichtigungen gefunden',
    'edit_multi_unit_help_text' => 'Wenn Sie diesen Wert bearbeiten, werden die Einkaufs- und Verkaufsbestände entsprechend geändert.',
    'sale_price_is_minimum_sale_price' => 'Verkaufspreis ist minimaler Verkaufspreis',
    'minimum_sale_price_help' => 'Wenn diese Option aktiviert ist, ist der Standardverkaufspreis auf dem Bildschirm POS oder Verkauf der Mindestverkaufspreis für das Produkt. Sie können keinen Preis unter dem Standardverkaufspreis festlegen.',
    'minimum_selling_price_error_msg' => 'Mindestverkaufspreis ist :price',
    'lot_numbers_are_used_in_sale' => 'Kann nicht gelöscht werden. Einige Lots dieses Kaufs sind bereits verkauft',
    'created_at' => 'Hergestellt in',
    'download' => 'Herunterladen',
    'previous_file_will_be_replaced' => 'Die zuvor hochgeladene Datei wird ersetzt',
    'access_accounts' => 'Zugriff auf Konten',
    'payment_accounts' => 'Zahlungskonten',
    'total_shipping_charges' => 'Gesamt Versandkosten',
    'recurring_invoice_error_message' => 'Rechnung kann nicht erstellt werden für Subskriptionsnummer: <i>:subscription_no</i>. Erforderlicher Bestand für Produkt <i>:product_name</i> nicht vorhanden',
    'load_more' => 'Mehr laden',
    'cash_flow' => 'Bargeldumlauf',
    'subscribe' => 'Abonnieren',
    'subscription_interval' => 'Abonnementintervall',
    'enable_subscription' => 'Abonnement aktivieren',
    'subscription_no' => 'Abonnement-Nr.',
    'start_subscription' => 'Abonnement starten',
    'stop_subscription' => 'Abonnement beenden',
    'subscriptions' => 'Abonnements',
    'generated_invoices' => 'Generierte Rechnungen',
    'last_generated' => 'Zuletzt generiert',
    'upcoming_invoice' => 'Kommende Rechnung',
    'total_sales_return' => 'Gesamtumsatzrendite',
    'subscription_invoice' => 'Abonnementrechnung',
    'subscribed_invoice' => 'Abonnierte Rechnung',
    'view_document' => 'Dokument anzeigen',
    'invoice_url_help' => 'Link zum Anzeigen der Rechnung ohne Login.',
    'unit_cannot_be_deleted' => 'Produkte existieren mit dieser Einheit; Kann nicht gelöscht werden',
    'expiry_date_in_business_date_format' => 'Verfallsdatum des Bestands in <b>Format des Geschäftsdatums</b>',
    'product_stock_details' => 'Details zum Produktbestand',
    'search' => 'Suche',
    'total_sold' => 'Insgesamt verkauft',
    'total_stock_available' => 'Gesamtbestand verfügbar',
    'total_stock_transfered_to_the_location' => 'Gesamtbestand, der an den Standort übertragen wurde',
    'total_stock_transfered_from_the_location' => 'Summe der vom Standort übertragenen Bestände',
    'total_stock_calculated' => 'Lagerbestand insgesamt',
    'adjust_stock_mismatch' => 'Passen Sie die Übereinstimmung der Aktien an',
    'opening_balance_payments' => 'Eröffnungsbilanzzahlungen',
    'enable_service_staff_in_product_line' => 'Servicepersonal in Produktlinie aktivieren',
    'inline_service_staff_tooltip' => 'Wenn aktiviert, können für eine Bestellung / einen Verkauf verschiedene Servicemitarbeiter für verschiedene Produkte zugewiesen werden',
    'line_orders' => 'Zeilenaufträge',
    'total_tax' => 'Gesamtsteuer',
    'total_discount' => 'Totaler Rabatt',
    'net_price' => 'Nettopreis',
    'select_same_for_all_rows' => 'Wählen Sie für alle Produkte den gleichen Wert',
    'edited' => 'Bearbeitet',
    'by' => 'Durch',
    'upload_documents' => 'Dokumente hochladen',
    'documents' => 'Unterlagen',
    'click_to_print' => 'Zum Drucken klicken',
    'deactivated_success' => 'Erfolgreich deaktiviert',
    'products_could_not_be_deleted' => 'Einige Produkte konnten nicht gelöscht werden, da Transaktionen damit verbunden sind.',
    'price' => 'Preis',
    'auto_backup_instruction' => 'Msgstr Um die automatische Sicherung zu aktivieren, müssen Sie mit diesem Befehl einen Cron - Job einrichten',
    'gross_profit' => 'Bruttoertrag',
    'single' => 'Single',
    'variable' => 'Variable',
    'total_purchase_price' => 'Gesamtkaufpreis',
    'total_sell_price' => 'Gesamtverkaufspreis',
    'packing_slip' => 'Packzettel',
    'use_superadmin_email_settings' => 'System-E-Mail-Konfigurationen verwenden',
    'profit_by_products' => 'Profit durch Produkte',
    'profit_by_categories' => 'Profit nach Kategorien',
    'profit_by_brands' => 'Profitieren Sie von Marken',
    'profit_by_locations' => 'Profitieren Sie von Standorten',
    'profit_by_invoice' => 'Gewinn auf Rechnung',
    'profit_by_date' => 'Gewinn nach Datum',
    'uncategorized' => 'Nicht kategorisiert',
    'add_purchase_return' => 'Kaufrückgabe hinzufügen',
    'edit_purchase_return' => 'Kaufretoure bearbeiten',
    'purchase_return_updated_success' => 'Kaufretoure erfolgreich aktualisiert',
    'profit_by_customer' => 'Profitieren Sie vom Kunden',
    'profit_by_day' => 'Profit by day',
    'sunday' => 'Sonntag',
    'monday' => 'Montag',
    'tuesday' => 'Dienstag',
    'wednesday' => 'Mittwoch',
    'thursday' => 'Donnerstag',
    'friday' => 'Freitag',
    'saturday' => 'Samstag',
    'show_product_image' => 'Produktbild anzeigen',
    'view_user' => 'Benutzer anzeigen',
    'allow_selected_contacts' => 'Ausgewählte Kontakte zulassen',
    'allow_selected_contacts_tooltip' => 'Nur Zugriff auf ausgewählte Kontakte im Feld für die Suche nach Verkäufen / Käufen von Kunden / Lieferanten erlauben',
    'allowed_contacts' => 'Zulässige Kontakte',
    'view_purchase_price' => 'Kaufpreis anzeigen',
    'view_purchase_price_tooltip' => 'Berechtigung zum Anzeigen des Kaufpreises in Produktdetails',
    'created' => 'Erstellt',
    'updated' => 'Aktualisierte',
    'deleted' => 'Gelöscht',
    'day' => 'Tag',
    'is_service_staff_required' => 'Wird Servicepersonal benötigt',
    'items_report' => 'Artikelbericht',
    'purchase_price' => 'Kaufpreis',
    'sell_date' => 'Verkaufsdatum',
    'returned' => 'ist zurückgekommen',
    'dob' => 'Geburtsdatum',
    'married' => 'Verheiratet',
    'unmarried' => 'Unverheiratet',
    'marital_status' => 'Familienstand',
    'blood_group' => 'Blutgruppe',
    'divorced' => 'Geschieden',
    'contact_no' => 'Kontakt Nummer',
    'fb_link' => 'Facebook Link',
    'twitter_link' => 'Twitter Link',
    'social_media' => 'Social Media :number',
    'permanent_address' => 'Fester Wohnsitz',
    'current_address' => 'Momentane Adresse',
    'guardian_name' => 'Guardian Name',
    'custom_field' => 'Benutzerdefiniertes Feld :number',
    'bank_details' => 'Bankdaten',
    'account_holder_name' => 'Der Name des Kontoinhabers',
    'account_number' => 'Kontonummer',
    'bank_name' => 'Bank Name',
    'bank_code' => 'Bankleitzahl',
    'bank_code_help' => 'Ein eindeutiger Code zur Identifizierung der Bank in Ihrem Land, zum Beispiel: IFSC-Code',
    'branch' => 'Ast',
    'tax_payer_id' => 'Steuerzahleridentifikationsnummer',
    'tax_payer_id_help' => 'Steuernummer des Mitarbeiters, z. B. PAN-Karte in Indien',
    'more_info' => 'Mehr Informationen',
    'id_proof_name' => 'ID Proof Name',
    'id_proof_number' => 'ID Proof Nummer',
    'view_own_sell_only' => 'Nur eigenen Verkauf anzeigen',
    'reward_point_settings' => 'Belohnungspunkteinstellungen',
    'enable_rp' => 'Belohnungspunkt aktivieren',
    'rp_name' => 'Belohnungspunkt-Anzeigename',
    'amount_for_unit_rp' => 'Betrag für Einheitspunkte',
    'min_order_total_for_rp' => 'Mindestbestellmenge, um Belohnung zu verdienen',
    'max_rp_per_order' => 'Maximale Punktzahl pro Bestellung',
    'earning_points_setting' => 'Einstellungen für das Sammeln von Punkten',
    'redeem_points_setting' => 'Punkte einlösen',
    'redeem_amount_per_unit_rp' => 'Betrag pro Einheitspunkt einlösen',
    'min_order_total_for_redeem' => 'Mindestbestellmenge zum Einlösen von Punkten',
    'min_redeem_point' => 'Mindesteinlösungspunkt',
    'max_redeem_point' => 'Maximaler Einlösungspunkt pro Bestellung',
    'rp_expiry_period' => 'Ablauffrist für Prämienpunkte',
    'redeemed_amount' => 'Eingelöster Betrag',
    'max_points_used' => 'Maximum :points Punkte können verwendet werden',
    'reward_points' => 'Belohnungspunkte',
    'max_reward_point_available' => 'Maximum :name verfügbar',
    'min_reward_points_used' => 'Minimum :name kann verwendet werden',
    'redeemed' => 'Eingelöst',
    'available' => 'Verfügbar',
    'earned' => 'Verdient',
    'total_reward_amount' => 'Total Kundenbelohnung',
    'ledger' => 'Hauptbuch',
    'type' => 'Art',
    'show_payments' => 'Zahlungen anzeigen',
    'variation_images' => 'Variationsbilder',
    'file_deleted_successfully' => 'Datei erfolgreich gelöscht',
    'combo' => 'Combo',
    'not_for_selling' => 'Nicht zum Verkaufen',
    'tooltip_not_for_selling' => 'Wenn diese Option aktiviert ist, wird das Produkt zu Verkaufszwecken nicht im Verkaufsbildschirm angezeigt.',
    'show_reward_point' => 'Belohnungspunkt anzeigen',
    'enable_booking' => 'Buchungen aktivieren',
    'bulk_edit' => 'Bulk-Bearbeitung',
    'bulk_edit_products' => 'Massenbearbeitung von Produkten',
    'search_product_to_edit' => 'Produkt zum Bearbeiten suchen',
    'related_sub_units' => 'Verwandte Untereinheiten',
    'sub_units_tooltip' => 'Basierend auf der ausgewählten Einheit werden Untereinheiten für diese Einheit angezeigt. Wählen Sie die zutreffende Untereinheit aus. Lassen Sie das Feld leer, wenn alle Untereinheiten für das Produkt zutreffen.',
    'enable_sub_units' => 'Untereinheiten aktivieren',
    'added_on' => 'Hinzugefügt zu',
    'quantity_mismatch_exception' => 'FEHLER: NICHT ERLAUBT: Nichtübereinstimmung zwischen verkaufter und verfügbarer Menge. Produkt: :product',
    'roles_and_permissions' => 'Rollen und Berechtigungen',
    'admin_role_location_permission_help' => 'Admin kann auf alle Standorte zugreifen',
    'edit_product_price_from_pos_screen' => 'Produktpreis vom POS-Bildschirm aus bearbeiten',
    'edit_product_discount_from_pos_screen' => 'Produktrabatt vom POS-Bildschirm aus bearbeiten',
    'download_template_file' => 'Vorlagendatei herunterladen',
    'types_of_service' => 'Arten von Dienstleistungen',
    'add_type_of_service' => 'Art des Dienstes hinzufügen',
    'packing_charge_type' => 'Packgebührentyp',
    'packing_charge' => 'Verpackungsgebühr',
    'enable_custom_fields' => 'Benutzerdefinierte Felder aktivieren',
    'edit_type_of_service' => 'Art des Dienstes bearbeiten',
    'types_of_service_help' => 'Art der Dienstleistung bedeutet Dienstleistungen wie Dine-In, Paket, Hauszustellung, Lieferung durch Dritte usw.',
    'select_types_of_service' => 'Arten von Diensten auswählen',
    'service_custom_field_1' => 'Benutzerdefiniertes Feld 1',
    'service_custom_field_2' => 'Benutzerdefiniertes Feld 2',
    'service_custom_field_3' => 'Benutzerdefiniertes Feld 3',
    'service_custom_field_4' => 'Benutzerdefiniertes Feld 4',
    'default_selling_price_group' => 'Standardverkaufspreisgruppe',
    'location_price_group_help' => 'Diese Preisgruppe wird an diesem Standort als Standardpreisgruppe verwendet.',
    'default_accounts' => 'Standardkonto',
    'product_location_help' => 'Standorte, an denen das Produkt verfügbar sein wird.',
    'available_in_locations' => 'Verfügbar an Standorten',
    'enable' => 'Aktivieren',
    'payment_options' => 'Zahlungsmöglichkeiten',
    'pay_reference_no' => 'Referenznummer bezahlen.',
    'search_settings' => 'Sucheinstellungen',
    'custom_labels' => 'Benutzerdefinierte Etiketten',
    'labels_for_custom_payments' => 'Etiketten für benutzerdefinierte Zahlungen',
    'packed' => 'Verpackt',
    'shipped' => 'Versendet',
    'delivered' => 'Geliefert',
    'shipping_address' => 'Lieferanschrift',
    'shipping_status' => 'Versandstatus',
    'delivered_to' => 'Geliefert an',
    'edit_shipping' => 'Versand bearbeiten',
    'shipments' => 'Sendungen',
    'access_shipping' => 'Zugang Versand',
    'uploaded_by' => 'Hochgeladen von',
    'account_types' => 'Kontoarten',
    'add_account_type' => 'Kontotyp hinzufügen',
    'parent_account_type' => 'Übergeordneter Kontotyp',
    'edit_account_type' => 'Kontotyp bearbeiten',
    'account_type' => 'Konto Typ',
    'account_sub_type' => 'Konto-Untertyp',
    'enable_updating_product_price_tooltip' => 'Wenn der Kaufpreis und der Verkaufspreis des aktivierten Produkts aktualisiert werden, nachdem ein Kauf hinzugefügt oder aktualisiert wurde',
    'default_datatable_page_entries' => 'Standardmäßig datierbare Seiteneinträge',
    'product_not_found_exception' => 'Produkt mit Artikelnummer :sku nicht in Zeile :sku gefunden',
    'price_group_not_found_exception' => 'Preisgruppe mit Name :pg in Zeile :row nicht gefunden',
    'price_group_non_numeric_exception' => 'Nicht numerischer Preis in Zeile :row gefunden',
    'total_stock_price' => 'Aktueller Aktienwert',
    'test_email_configuration' => 'Test-E-Mail senden',
    'email_tested_successfully' => 'E-Mail-Konfigurationen erfolgreich überprüft',
    'test_number' => 'Testnummer',
    'test_sms_configuration' => 'Test-SMS senden',
    'test_number_is_required' => 'Testnummer ist erforderlich',
    'import_contact_type_ins' => 'Verfügbare Optionen: <strong> <br> 1 = Kunde, <br> 2 = Lieferant und 3 = Beide </strong>',
    'enable_pos_transaction_date' => 'Transaktionsdatum auf POS-Bildschirm aktivieren',
    'amount_for_unit_rp_tooltip' => '<strong> Bedeutet, wie viel der Kunde ausgegeben hat, um einen Prämienpunkt zu erhalten. </strong> <br> <br> <strong> Beispiel: </strong> Wenn Sie den Wert auf 10 festlegen, werden diese für jeweils 10 US-Dollar ausgegeben Wenn der Kunde für $ 1000 einkauft, erhält er 100 Bonuspunkte. ',
    'max_rp_per_order_tooltip' => 'Maximale Prämienpunkte, die Kunden mit einer Rechnung verdienen können. Lassen Sie dieses Feld leer, wenn Sie keine derartigen Einschränkungen wünschen.',
    'min_order_total_for_rp_tooltip' => '<strong> Der Mindestbetrag, den der Kunde ausgeben sollte, um Prämienpunkte zu erhalten. </strong> <br> <br> <strong> Beispiel: </strong> Wenn Sie den Wert auf 100 festlegen, erhält der Kunde nur dann Prämienpunkte, wenn diese vorhanden sind Rechnungssumme ist größer oder gleich 100. Wenn die Rechnungssumme 99 ist, erhalten sie keine Belohnungspunkte. <br> <br> Sie können sie als Minimum 1 festlegen. ',
    'redeem_amount_per_unit_rp_tooltip' => '<strong> Gibt den Einlösungsbetrag pro Punkt an. </strong> <br> <br> <strong> Zum Beispiel: </strong> Wenn 1 Punkt $ 1 ist, geben Sie den Wert als 1 ein. Wenn 2 Punkte $ 1 sind, dann geben Sie den Wert als 0.50 ein',
    'min_order_total_for_redeem_tooltip' => 'Mindestbestellmenge, für die der Kunde Punkte einlösen kann. Lassen Sie dieses Feld leer, wenn Sie diese Einschränkung nicht benötigen, oder geben Sie etwas kostenlos.',
    'min_redeem_point_tooltip' => 'Minimale Einlösungspunkte, die pro Rechnung verwendet werden können. Lassen Sie dieses Feld leer, wenn Sie diese Einschränkung nicht benötigen.',
    'max_redeem_point_tooltip' => 'Maximale Punktzahl, die in einer Bestellung verwendet werden kann. Lassen Sie dieses Feld leer, wenn Sie diese Einschränkung nicht benötigen.',
    'rp_expiry_period_tooltip' => '<strong> Ablauffrist für vom Kunden verdiente Punkte. </strong> <br> <br> Sie können diese in Monaten oder im Jahr festlegen. Abgelaufene Punkte werden nach Ablauf dieser Frist automatisch vom Kundenkonto abgezogen.',
    'configure_product_search' => 'Produktsuche konfigurieren',
    'search_products_by' => 'Produkte suchen nach',
    'credit_sales' => 'Kreditverkäufe',
    'total_payment' => 'Gesamtzahlung',
    'deactivate_location' => 'Standort deaktivieren',
    'activate_location' => 'Standort aktivieren',
    'business_location_activated_successfully' => 'Wirtschaftsstandort erfolgreich aktiviert',
    'business_location_deactivated_successfully' => 'Wirtschaftsstandort erfolgreich deaktiviert',
    'show_credit_sale_button' => 'Show Credit Sale Button',
    'show_credit_sale_btn_help' => 'Msgstr Wenn die Schaltfläche Guthabenverkauf anstelle der Schaltfläche Karte auf dem POS - Bildschirm angezeigt wird',
    'credit_sale' => 'Kreditverkauf',
    'tooltip_credit_sale' => 'Kasse als Guthabenverkauf',
    'add_as_sub_txonomy' => 'Als Subtaxonomie hinzufügen',
    'select_parent_taxonomy' => 'Wählen Sie die übergeordnete Taxonomie aus',
    'warranty' => 'Garantie',
    'warranties' => 'Garantien',
    'all_warranties' => 'Alle Garantien',
    'add_warranty' => 'Garantie hinzufügen',
    'duration' => 'Dauer',
    'edit_warranty' => 'Garantie bearbeiten',
    'enable_product_warranty' => 'Garantie aktivieren',
    'warranty_ends_on' => 'Garantie endet am',
    'restaurant_module_settings' => 'Restaurant Modul Einstellungen',
    'warranty_module_settings' => 'Einstellungen des Garantiemoduls',
    'show_warranty_name' => 'Garantienamen anzeigen',
    'show_warranty_exp_date' => 'Verfallsdatum der Garantie anzeigen',
    'show_warranty_description' => 'Garantiebeschreibung anzeigen',
    'gender' => 'Geschlecht',
    'male' => 'Männlich',
    'female' => 'Weiblich',
    'others' => 'Andere',
    'recent_transactions' => 'Kürzliche Transaktionen',
    'add_row' => 'Zeile hinzufügen',
    'add_widget_here' => 'Hier ein Widget hinzufügen',
    'move_row' => 'Verschiebe die Reihe',
    'configure_dashboard' => 'Dashboard konfigurieren :name',
    'change_return_label' => 'Rücksendeetikett ändern',
    'change_return_help' => 'Rückgabe ändern ist der Betrag, der an den Kunden zurückgegeben wird, wenn eine Überzahlung erfolgt ist.',
    'profile_photo' => 'Profilfoto',
    'upload_image' => 'Bild hochladen',
    'expense_tax' => 'Expense Tax',
    'expense_tax_tooltip' => 'Gesamtsteuer auf Geschäftsausgaben für den ausgewählten Zeitraum',
    'tax_overall' => 'Gesamt (Input - Output - Aufwand)',
    'overdue' => 'Überfällig',
    'partial-overdue' => 'Teilweise überfällig',
    'due_date_label' => 'Fälligkeitsdatum Etikett',
    'show_due_date' => 'Fälligkeitsdatum anzeigen',
    'contact_custom_field1' => 'Benutzerdefiniertes Feld 1',
    'contact_custom_field2' => 'Benutzerdefiniertes Feld 2',
    'contact_custom_field3' => 'Benutzerdefiniertes Feld 3',
    'contact_custom_field4' => 'Benutzerdefiniertes Feld 4',
    'added_by' => 'Hinzugefügt von',
    'authorized_signatory' => 'Zeichnungsberechtigte',
    'no_location_access_found' => 'Kein Standortzugriff gefunden',
    'location_custom_field1' => 'Benutzerdefiniertes Feld 1',
    'location_custom_field2' => 'Benutzerdefiniertes Feld 2',
    'location_custom_field3' => 'Benutzerdefiniertes Feld 3',
    'location_custom_field4' => 'Benutzerdefiniertes Feld 4',
    'user_custom_field1' => 'Benutzerdefiniertes Feld 1',
    'user_custom_field2' => 'Benutzerdefiniertes Feld 2',
    'user_custom_field3' => 'Benutzerdefiniertes Feld 3',
    'user_custom_field4' => 'Benutzerdefiniertes Feld 4',
    'logo_not_work_in_sms' => 'Das Geschäftslogo funktioniert in SMS nicht',
    'notification_email_tooltip' => 'Kommagetrennte Folge von Empfänger-E-Mails',
    'heading' => 'Überschrift',
    'updated_at' => 'Aktualisiert am',
    'documents_and_notes' => 'Documents & Note',
    'add_note' => 'Notiz hinzufügen',
    'edit_note' => 'Notiz bearbeiten',
    'is_private' => 'Ist privat?',
    'private' => 'Privat',
    'documents' => 'Unterlagen',
    'note_will_be_visible_to_u_only' => 'Notiz & Dokument sind nur für Sie sichtbar',
    'activities' => 'Aktivitäten',
    'no_limit' => 'Keine Begrenzung',
    'contains_media' => 'Diese Notiz enthält Medien',
    'private_note' => 'Dies ist eine private Notiz',
    'amount_rounding_method' => 'Betragsrundungsmethode',
    'round_to_nearest_whole_number' => 'Runde auf die nächste ganze Zahl',
    'round_to_nearest_decimal' => 'Auf nächste Dezimalstelle runden (Vielfaches von :multiple)',
    'amount_rounding_method_help' => 'Beispiel: <br> <b> Auf nächste ganze Zahl runden: </b> 2.23 => 2, 2.50 => 3, 2.73 => 3 <br> <b> Auf nächste Dezimalstelle runden (Vielfaches von 0.05): </b> 2,11 => 2,10, 2,12 => 2,10, 2,13 => 2,15 ',
    'round_off' => 'Abrunden',
    'round_off_label' => 'Abrundungsetikett',
    'total_sell_round_off' => 'Verkauf insgesamt abrunden',
    'user_info' => 'Nutzerinformation',
    'enable_weighing_scale' => 'Waage aktivieren',
    'weighing_scale_setting' => 'Wiegen der Barcode-Einstellung',
    'weighing_scale_setting_help' => 'Konfigurieren Sie den Barcode gemäß Ihrer Waage.',
    'weighing_barcode_prefix' => 'Präfix',
    'weighing_product_sku_length' => 'Produktlänge',
    'weighing_qty_integer_part_length' => 'Menge ganzzahlige Teillänge',
    'weighing_qty_fractional_part_length' => 'Menge Bruchteil Länge',
    'weighing_scale' => 'Waage',
    'weighing_scale_barcode' => 'Waagen-Barcode',
    'weighing_scale_barcode_help' => 'Scannen Sie den Barcode vom Wiegeverkauf und senden Sie ihn ab',
    'prefix_did_not_match' => 'Präfix stimmte nicht überein',
    'sku_not_match' => 'Kein Produkt mit sku gefunden :sku',
    'total_quantity_label' => 'Gesamtmengenetikett',
    'account_summary' => 'Kontoübersicht',
    'ledger_table_heading' => 'Alle Rechnungen und Zahlungen zwischen :start_date und :end_date anzeigen',
    'beginning_balance' => 'Anfangsbestand',
    'total_invoice' => 'Gesamtrechnung',
    'balance_due' => 'Restbetrag fällig',
    'send_ledger' => 'Ledger senden',
    'notifications' => 'Benachrichtigungen',
    'ledger_attacment_help' => 'Das Hauptbuch wird mit dieser E-Mail im PDF-Format angehängt',
    'upload_module' => 'Upload-Modul',
    'upload' => 'Hochladen',
    'pls_upload_valid_zip_file' => 'Bitte laden Sie eine gültige Zip-Datei hoch.',
    'import_sales' => 'Verkäufe importieren',
    'preview_imported_sales' => 'Vorschau importierter Verkäufe',
    'customer_phone_number' => 'Telefonnummer des Kunden',
    'customer_email' => 'Kunden-eMail',
    'item_tax' => 'Artikelsteuer',
    'item_discount' => 'Artikelrabatt',
    'item_description' => 'Artikelbeschreibung',
    'order_total' => 'Auftragssumme',
    'upload_and_review' => 'Hochladen und überprüfen',
    'group_sale_line_by' => 'Group Sell Line by',
    'import_sale_product_not_found' => 'Produkt mit Name :product_name oder SKU :sku nicht in Zeile :row gefunden',
    'import_sale_tax_not_found' => 'Steuer mit Name :tax_name nicht in Zeile :row gefunden',
    'email_or_phone_required' => 'Telefon oder E-Mail des Kunden ist erforderlich',
    'product_name_or_sku_is_required' => 'Produktname oder SKU erforderlich',
    'quantity_is_required' => 'Menge ist erforderlich',
    'unit_price_is_required' => 'Stückpreis ist erforderlich',
    'sales_imported_successfully' => 'Verkäufe erfolgreich importiert',
    'upload_data_in_excel_format' => 'Verkaufsdaten im Excel-Format hochladen',
    'map_columns_with_respective_sales_fields' => 'Wählen Sie für jede Spalte die entsprechenden Verkaufsfelder aus',
    'choose_location_and_group_by' => 'Wählen Sie den Unternehmensstandort und die Spalte, nach der die Verkaufslinien gruppiert werden sollen',
    'date_format_instruction' => 'Verkaufsdatum Zeitformat sollte Y-m-d H:i:s (2020-07-15 17:45:32) sein',
    'product_name_single_only' => 'Produktname (nur Einzel- oder Kombinationsprodukte)',
    'product_sku' => 'Produkt-SKU',
    'product_unit' => 'Produkteinheit',
    'import_sale_unit_not_found' => 'Einheit mit Name :unit_name nicht in Zeile :row gefunden',
    'types_of_servicet_not_found' => 'Diensttypen mit dem Namen :types_of_service_name nicht in Zeile :row gefunden',
    'email_or_phone_cannot_be_empty_in_row' => 'In Zeile :row ist entweder ein Telefon oder eine E-Mail des Kunden erforderlich',
    'product_cannot_be_empty_in_row' => 'In Zeile :row ist entweder der Produktname oder die Produkt-SKU erforderlich',
    'quantity_cannot_be_empty_in_row' => 'Produktmenge wird in Zeile :row benötigt',
    'unit_price_cannot_be_empty_in_row' => 'Stückpreis wird in Zeile :row benötigt',
    'imports' => 'Importe',
    'invoices' => 'Rechnungen',
    'import_batch' => 'Stapel importieren',
    'import_time' => 'Importzeit',
    'cannot_select_a_field_twice' => 'Sie können ein Feld nicht zweimal auswählen',
    'revert_import' => 'Import zurücksetzen',
    'import_reverted_successfully' => 'Import erfolgreich zurückgesetzt',
    'importable_fields' => 'Importierbare Felder',
    'sort_order' => 'Sortierreihenfolge',
    'total_returned' => 'Total zurückgegeben',
    'skip' => 'ÜBERSPRINGEN',
    'group_by_tooltip' => 'Spalte zum Gruppieren von Verkaufszeilen in derselben Rechnung',
    'allowed_file' => 'Zulässige Datei',
    'detailed_with_purchase' => 'Detailliert (beim Kauf)',
    'supplier_name' => 'Name des Anbieters',
    'purchase_ref_no' => 'Kaufreferenz Nr.',
    'save_and_print' => 'Speichern und drucken',
    'update_and_print' => 'Aktualisieren und drucken',
    'all_your_discounts' => 'Alle Ihre Rabatte',
    'priority' => 'Priorität',
    'starts_at' => 'Startet um',
    'ends_at' => 'Endet am',
    'used_for_browser_based_printing' => 'Wird für das browserbasierte Drucken verwendet',
    'hsn_or_category_code' => 'HSN oder Kategoriecode',
    'discounts' => 'Rabatte',
    'allow_overselling' => 'Überverkauf zulassen',
    'view_own_purchase' => 'Nur Eigenkauf anzeigen',
    'view_own_expense' => 'Nur eigene Ausgaben anzeigen',
    'allow_login' => 'Login erlauben',
    'login_not_allowed' => 'Login nicht erlaubt',
    'labels_for_contact_custom_fields' => 'Beschriftungen für benutzerdefinierte Kontaktfelder',
    'labels_for_product_custom_fields' => 'Etiketten für benutzerdefinierte Produktfelder',
    'labels_for_purchase_custom_fields' => 'Label für Kaufkundenfelder',
    'labels_for_sell_custom_fields' => 'Labels für den Verkauf von benutzerdefinierten Feldern',
    'labels_for_location_custom_fields' => 'Beschriftungen für benutzerdefinierte Standortfelder',
    'labels_for_types_of_service_custom_fields' => 'Beschriftungen für Arten von benutzerdefinierten Servicefeldern',
    'labels_for_user_custom_fields' => 'Beschriftungen für benutzerdefinierte Benutzerfelder',
    'total_transfer_shipping_charge' => 'Gesamttransfer Versandkosten',
    'total_purchase_shipping_charge' => 'Gesamtkaufkosten',
    'total_sell_shipping_charge' => 'Total Sell Versandkosten',
    'opening_stock_location' => 'Eröffnungslagerort',
    'product_locations_ins' => 'Durch Kommas getrennte Zeichenfolge von Geschäftsstandortnamen, unter denen das Produkt verfügbar sein wird',
    'product_locations' => 'Produktstandorte',
    'view_products' => 'Produkte anzeigen',
    'pos_screen_featured_products' => 'POS-Bildschirm Empfohlene Produkte',
    'featured_products_help' => 'Ausgewählte Produkte werden oben auf dem Produktvorschlag für den Positionsbildschirm angezeigt, um einen schnellen Zugriff zu ermöglichen',
    'purchase_report' => 'Kaufbericht',
    'sale_report' => 'Verkaufsbericht',
    'payment_date' => 'Zahlungsdatum',
    'year_month' => 'Jahr Monat',
    'lead' => 'Führen',
    'featured_products' => 'Ausgewählte Produkte',
    'repeat_on' => 'Wiederholen am',
    'access_tables' => 'Zugriffstabellen',
    'access_printers' => 'Zugriff auf Drucker',
    'access_types_of_service' => 'Zugriff auf Dienstarten',
    'search_address' => 'Adresse suchen',
    'contact_locations' => 'Kontaktorte',
    'map' => 'Karte',
    'both_customer_and_supplier' => 'Sowohl Kunde als auch Lieferant',
    'service' => 'Bedienung',
    'either_cust_email_or_phone_required' => 'Entweder Kunden-E-Mail-ID oder Telefonnummer erforderlich',
    'either_product_name_or_sku_required' => 'Entweder Produktname (nur für Single und Combo) oder Produkt-Sku erforderlich',
    'show_invoice_scheme' => 'Rechnungsschema anzeigen',
    'select_invoice_scheme' => 'Rechnungsschema auswählen',
    'recurring_expense_help' => 'Wenn diese Option aktiviert ist, werden diese Ausgaben in regelmäßigen Abständen automatisch generiert.',
    'recur_interval' => 'Wiederkehrendes Intervall',
    'recur_expense_repetition_help' => 'Wenn leere Ausgaben unendlich oft generiert werden',
    'recurring_expense_message' => 'Neue wiederkehrende Kosten generiert. Ref.-Nr.: <i>:ref_no </i>',
    'recurring_expense' => 'Wiederkehrende Kosten',
    'generated_recurring_expense' => 'Generierte wiederkehrende Kosten',
    'select_contacts' => 'Kontakte auswählen',
    'middle_name' => 'Zweiter Vorname',
    'address_line_1' => 'Anschrift Zeile 1',
    'address_line_2' => 'Adresszeile 2',
    'dob' => 'Geburtsdatum',
    'dob_ins' => 'Format Y-m-d',
    'by_purchase_price' => 'Nach Kaufpreis',
    'by_sale_price' => 'Nach Verkaufspreis',
    'potential_profit' => 'Potenzieller Gewinn',
    'apply_all' => 'Alle anwenden',
    'hide_all_prices' => 'Alle Preise verstecken',
    'select_invoice_layout' => 'Rechnungslayout auswählen',
    'max_sales_discount_percent' => 'Max. Umsatzrabatt in Prozent',
    'max_sales_discount_percent_help' => 'Maximaler Rabattprozentsatz, den ein Benutzer während des Verkaufs geben kann. Lassen Sie ihn ohne Einschränkungen leer',
    'max_discount_error_msg' => 'Sie können max :discount% Rabatt pro Verkauf geben',
    'show_invoice_layout' => 'Dropdown-Liste Rechnungslayout anzeigen ',
    'recurred_from' => 'Wiederholt von',
    'recur_details' => 'Wiederkehrende Details',
    'recurring' => 'Wiederkehrend',
    'view_product_stock_value' => 'Produktbestandswert anzeigen',
    'added' => 'Hinzugefügt',
    'disable_credit_sale_button' => 'Schaltfläche Kreditverkauf deaktivieren',
    'pay' => 'Bezahlen',
    'use_advance_balance' => 'Vorauszahlung verwenden',
    'advance' => 'Vorauszahlung',
    'advance_balance' => 'Vorauszahlung',
    'advance_payment' => 'Vorauszahlung',
    'required_advance_balance_not_available' => 'Erforderliche Vorauszahlung nicht verfügbar',
    'in_transit' => 'In Transit',
    'edit_stock_transfer' => 'Bearbeiten Umlagerung',
    'completed_status_help' => 'Umlagerung nicht bearbeitet werden , wenn Status abgeschlossen ist',
    'no_data' => 'keine Daten',
    'sms_service' => 'SMS Service',
    'nexmo_key' => 'Nexmo Key',
    'nexmo_secret' => 'Nexmo Secret',
    'twilio_token' => 'Twilio Zugriffstoken',
    'twilio_sid' => 'Twilio Konto SID',
    'upcoming' => 'Kommende',
    'final' => 'Endgültige',
    'expense_for_contact' => 'Kontaktkosten',
    'create' => 'Erstellen',
    'waiting' => 'Warten',
    'sms_settings_header_key' => 'Header: Zifferntaste',
    'sms_settings_header_val' => 'Header: Zahlenwert',
    'stock_adjustment_details' => 'Bestandsanpassungsdetails',
    'stock_transfer_details' => 'Umlagerungsdetails',
    'invoice_layout_for_pos' => 'Rechnungslayout für POS',
    'invoice_layout_for_sale' => 'Rechnungslayout zum Verkauf',
    'invoice_layout_for_sale_tooltip' => 'Rechnungslayout für den Direktvertrieb',
    'new_quotation' => 'Neues Angebot',
    'new_quotation_notification' => 'Neue Angebotsbenachrichtigung',
    'view_quote_url' => 'Zitat-URL anzeigen',
    'close_cash_register' => 'Registrierkasse schließen',
    'view_cash_register' => 'Registrierkasse anzeigen',
    'for_tax_group_only' => 'Nur für Steuergruppen',
    'for_tax_group_only_help' => 'Wenn diese Option aktiviert ist, wird diese Steuer in Dropdown-Listen nicht einzeln angezeigt, sondern kann nur in der Steuergruppe hinzugefügt werden.',
    'exempt' => 'Befreit',
    'tax_exempt_help' => 'Der Steuersatz von null Prozent gilt als steuerfrei',
    'show_total_in_words' => 'Summe in Worten anzeigen',
    'show_in_word_help' => 'PHP-Intl-Erweiterung muss aktiviert sein',
    'print_on_suspend' => 'Rechnung bei Suspendierung drucken',
    'view_all_customer' => 'Alle Kunden anzeigen',
    'view_own_customer' => 'Eigenen Kunden anzeigen',
    'view_all_supplier' => 'Alle Lieferanten anzeigen',
    'view_own_supplier' => 'Eigenen Lieferanten anzeigen',
    'label_help' => 'Barcode / Etikett drucken',
    'connection_type_windows' => 'Verbindungstyp Windows',
    'connection_type_linux' => 'Verbindungstyp Linux',
    'windows_type_help' => 'Die Gerätedateien werden in Anlehnung an',
    'linux_type_help' => 'Ihre Druckergerätedatei befindet sich irgendwo wie',
    'due_date' => 'Geburtstermin',
    'quotation_no' => 'Angebotsnummer',
    'browser_based_printing' => 'Browser-basiertes Drucken',
    'configured_printer' => 'Konfigurierten Belegdrucker verwenden',
    'for_normal_printer' => 'Für normalen Drucker',
    'recomended_for_80mm' => 'Empfohlen für Thermo-Belegempfangsdrucker, 80 mm Papiergröße',
    'recomended_for_58mm' => 'Empfohlen für Thermo-Belegempfangsdrucker, Papierformat 80 mm und 58 mm',
    'classic' => 'Klassisch',
    'elegant' => 'Elegant',
    'slim' => 'Schlank',
    'detailed' => 'Detailliert',
    'columnize_taxes' => 'Steuern kolumnisieren',
    'types_of_service_help_long' => 'Servicetyp bedeutet Services wie Dine-In, Paket, Hauszustellung, Zustellung durch Dritte usw. Sie kann über <code> Einstellungen> Module </code> aktiviert / deaktiviert und über <code> Einstellungen> Servicetypen erstellt werden </code>. Sie können auch unterschiedliche Preisgruppen und Verpackungsgebühren für Servicetypen definieren.',
    'access_sell_return' => 'Der Zugang sell return',
    'system_notification' => 'Systembenachrichtigung',
    'time_range' => 'Zeitspanne',
    'enable_php_intl_extension' => 'Aktivieren Sie die PHP-Intl-Erweiterung in den PHP INI-Einstellungen',
    'profit_note' => '<b> Hinweis: </b> Der Gewinn nach Produkten / Kategorien / Marken berücksichtigt nur den Inline-Rabatt. Der Rechnungsrabatt wird nicht berücksichtigt.',
    'upcoming_bookings' => 'Bevorstehende Buchungen',
    'is_refund' => 'Ist Rückerstattung',
    'is_refund_help' => 'Wenn geprüfte Ausgaben erstattet werden und zum Nettogewinn addiert werden',
    'refund' => 'Rückerstattung',
    'activate' => 'Aktivieren Sie',
    'deactivate' => 'Deaktivieren',
    'add_quotation' => 'Zitat hinzufügen',
    'product_stock_history' => 'Produktbestandshistorie',
    'quantities_in' => 'Mengen in',
    'quantities_out' => 'Mengen raus',
    'in' => 'Im',
    'out' => 'Aus',
    'totals' => 'Summen',
    'quantity_change' => 'Mengenänderung',
    'new_quantity' => 'Neue Menge',
    'no_stock_history_found' => 'Keine Bestandshistorie gefunden',
    'add_draft' => 'Entwurf hinzufügen',
    'labels_for_sale_shipping_custom_fields' => 'Etiketten zum Verkauf Versand benutzerdefinierte Felder',
    'is_required' => 'Ist nötig',
    'convert_to_invoice' => 'In Rechnung umrechnen',
    'converted_to_invoice_successfully' => 'In Rechnung konvertiert :invoice_no erfolgreich',
    'proforma' => 'Proforma',
    'proforma_invoice' => 'Proforma-Rechnung',
    'convert_to_proforma' => 'In Proforma-Rechnung konvertieren',
    'converted_to_proforma_successfully' => 'In Proforma-Rechnung umgewandelt',
    'billing_address' => 'Rechnungsadresse',
    'delete_sell' => 'Verkauf löschen',
    'product_brochure' => 'Produktbroschüre',
    'whatsapp_text' => 'WhatsApp Text',
    'auto_send_wa_notif' => 'Automatische WhatsApp-Benachrichtigung senden',
    'send_whatsapp' => 'WhatsApp-Benachrichtigung senden',
    'send_sms' => 'SMS senden',
    'send_email' => 'E-Mail senden',
    'shipping_documents' => 'Versanddokumente',
    'no_attachment_found' => 'Kein Anhang gefunden',
    'attachments' => 'Anhänge',
    'print_invoice' => 'Rechnung drucken',
    'word_format' => 'Word Format',
    'international' => 'International',
    'indian' => 'Indisch',
    'word_format_help' => 'Im internationalen Format sind große Zahlen in Millionen, Milliarden und Billionen vertreten, während sie im indischen Format in Lakhs und Crores vertreten sind',
    'shipping_edited' => 'Versand bearbeitet',
    'payment_edited' => 'Zahlung bearbeitet',
    'notification_sent' => 'Benachrichtigung gesendet',
    'imported' => 'Importiert',
    'logout' => 'Ausloggen',
    'backup_clean_command_instruction' => 'Um alte Backups zu bereinigen, müssen Sie mit diesem Befehl einen Cron-Job einrichten:',
    'converted' => 'Umgewandelt',
    'view_commission_agent_sell' => 'Provisionsagent kann ihren eigenen Verkauf sehen',
    'access_commission_agent_shipping' => 'Kommissionsvertreter kann auf ihre eigenen Sendungen zugreifen',
    'access_own_shipping' => 'Zugriff auf eigene Sendungen',
    'email_notification_sent' => 'E-Mail-Benachrichtigung gesendet',
    'sms_notification_sent' => 'SMS-Benachrichtigung gesendet',
    'packing_date' => 'Verpackungsdatum',
    'print_exp_date' => 'Verfallsdatum drucken',
    'print_packing_date' => 'Verpackungsdatum drucken',
    'print_lot_number' => 'Chargennummer drucken',
    'family_contact_number' => 'Familienkontaktnummer',
    'custom_payment' => 'Benutzerdefinierte Zahlung :number',
    'default_credit_limit' => 'Standardkreditlimit',
    'price_calculation_type' => 'Preisberechnungsart',
    'show_pricing_on_product_sugesstion' => 'Preis auf Produktvorschlag-Tooltip anzeigen',
    'all_purchase_orders' => 'Alle Bestellungen',
    'add_purchase_order' => 'Bestellung hinzufügen',
    'edit_purchase_order' => 'Bestellung bearbeiten',
    'purchase_order_details' => 'Bestelldetails',
    'enable_purchase_order' => 'Bestellung aktivieren',
    'purchase_order_help_text' => 'Eine Bestellung ist ein Handelsdokument und ein erstes offizielles Angebot eines Käufers an einen Verkäufer, in dem Typen, Mengen und vereinbarte Preise für Produkte oder Dienstleistungen angegeben sind. Sie dient zur Kontrolle des Kaufs von Produkten und Dienstleistungen von externen Lieferanten. Bestellungen können ein wesentlicher Bestandteil der Bestellungen von Enterprise Resource Planning-Systemen sein. ',
    'view_all_purchase_order' => 'Alle Bestellungen anzeigen',
    'view_own_purchase_order' => 'Eigene Bestellung anzeigen',
    'create_purchase_order' => 'Bestellung erstellen',
    'delete_purchase_order' => 'Bestellung löschen',
    'purchase_order_delete_success' => 'Bestellung erfolgreich gelöscht',
    'order_quantity' => 'Bestellmenge',
    'order_date' => 'Auftragsdatum',
    'max_quantity_quantity_allowed' => 'Max :quantity erlaubt',
    'leave_empty_to_autogenerate' => 'Zum automatischen Generieren leer lassen',
    'add_edit_invoice_number' => 'Bearbeitungsrechnungsnummer hinzufügen',
    'keep_blank_to_autogenerate' => 'Zum automatischen Generieren leer lassen',
    'download_pdf' => 'PDF Herunterladen',
    'attach_pdf_in_email' => 'Rechnungs-PDF per E-Mail anhängen',
    'send_sms_whatsapp_notification' => 'SMS / WhatsApp-Benachrichtigung senden',
    'enable_sales_order' => 'Kundenauftrag aktivieren',
    'sales_order' => 'Kundenauftrag',
    'sales_order_help_text' => 'Der Kundenauftrag, manchmal als SO abgekürzt, ist ein Auftrag, den ein Unternehmen oder Einzelunternehmer an einen Kunden erteilt. Ein Kundenauftrag kann sich auf Produkte und / oder Dienstleistungen beziehen.',
    'add_sales_order' => 'Kundenauftrag hinzufügen',
    'edit_sales_order' => 'Kundenauftrag bearbeiten',
    'shipping_note' => 'Lieferschein',
    'code_1_name' => 'Code 1 Name',
    'code_2_name' => 'Code 2 Name',
    'code_1' => 'Code 1',
    'code_2' => 'Code 2',
    'download_paking_pdf' => 'Download packing pdf',
    'subject_type' => 'Betreff Typ',
    'activity_log' => 'Aktivitätsprotokoll',
    'sell_deleted' => 'Verkauf gelöscht',
    'so_deleted' => 'Kundenauftrag gelöscht',
    'po_deleted' => 'Bestellung gelöscht',
    'purchase_deleted' => 'Kauf gelöscht',
    'contact_deleted' => 'Kontakt gelöscht',
    'payment_deleted' => 'Zahlung gelöscht',
    'quantity_remaining' => 'Verbleibende Menge',
    'view_sale' => 'View Sell',
    'update_sale' => 'Update Sell',
    'add_sell' => 'Add Sell',
    'account_details' => 'Kontodetails',
    'label' => 'Etikette',
    'prefer_payment_method' => 'Zahlungsmethode bevorzugen',
    'prefer_payment_account' => 'Zahlungskonto bevorzugen',
    'this_will_be_shown_in_pdf' => 'Bevorzugte Methode / Konto, auf der der Kunde die Rechnung bezahlen kann. (Nützlich für Kreditrechnungen)',
    'view_own_so' => 'Eigenen Kundenauftrag anzeigen',
    'view_all_so' => 'Alle Kundenaufträge anzeigen',
    'create_so' => 'Kundenauftrag anlegen',
    'edit_so' => 'Kundenauftrag bearbeiten',
    'delete_so' => 'Kundenauftrag löschen',
    'types_of_service_custom_field_help' => 'Beim Hinzufügen von Verkauf werden sechs benutzerdefinierte Felder verfügbar sein',
    'delete_expense' => 'Kosten löschen',
    'access_all_expense' => 'Zugriff auf alle Ausgaben',
    'designation' => 'Bezeichnung',
    'department' => 'Abteilung',
    'is_default_for_contact' => 'Ist Standard für Kontakt',
    'individual' => 'Individuell',
    'is_export' => 'Ist Export?',
    'export' => 'Export',
    'export_custom_field1' => 'Benutzerdefiniertes Feld 1 exportieren',
    'export_custom_field2' => 'Benutzerdefiniertes Feld 2 exportieren',
    'export_custom_field3' => 'Benutzerdefiniertes Feld 3 exportieren',
    'export_custom_field4' => 'Benutzerdefiniertes Feld 4 exportieren',
    'export_custom_field5' => 'Benutzerdefiniertes Feld 5 exportieren',
    'export_custom_field6' => 'Benutzerdefiniertes Feld 6 exportieren',
    'enable_export' => 'Export aktivieren',
    'edit_status' => 'Status bearbeiten',
    'status_updated' => 'Status aktualisiert',
    'item_discount_label' => 'Artikelrabattetikett',
    'sales_orders' => 'Kundenaufträge',
    'cash_denominations' => 'Bargeldbezeichnungen',
    'cash_denominations_help' => 'Kommagetrennte Werte Beispiel: 100.************',
    'denomination' => 'Konfession',
    'count' => 'Anzahl',
    'denomination_add_help_text' => 'Nennwerte in Einstellungen -> Geschäftseinstellungen -> POS -> Barwert hinzufügen',
    'order_dates' => 'Bestelldaten',
    'by_category' => 'Nach Kategorie',
    'by_brand' => 'Nach Marke',
    'no_brand' => 'Keine Marke',
    'woocommerce_sync' => 'WooCommerce-Synchronisierung',
    'enable' => 'Aktivieren',
    'disable' => 'Deaktivieren',
    'cmmsn_calculation_type' => 'Provisionsberechnungsart',
    'invoice_value' => 'Rechnungswert',
    'total_payment_with_commsn' => 'Gesamtzahlung mit Provision',
    'payments_with_cmmsn' => 'Zahlungen mit Provision',
    'view_all_drafts' => 'Alle Entwürfe anzeigen',
    'view_own_drafts' => 'Eigene Entwürfe ansehen',
    'edit_draft' => 'Entwurf bearbeiten',
    'delete_draft' => 'Entwurf löschen',
    'view_all_quotations' => 'Alle Angebote anzeigen',
    'view_own_quotations' => 'Eigene Angebote ansehen',
    'edit_quotation' => 'Angebot bearbeiten',
    'delete_quotation' => 'Angebot löschen',
    'primary_work_location' => 'Hauptarbeitsplatz',
    'labels_for_purchase_shipping_custom_fields' => 'Etiketten für benutzerdefinierte Felder für den Kaufversand',
    'view_own_purchase_n_stock_adjustment' => 'Eigene Käufe und Bestandsanpassung anzeigen',
    'view_all_purchase_n_stock_adjustment' => 'Alle Käufe und Bestandsanpassungen anzeigen',
    'access_all_shipments' => 'Auf alle Sendungen zugreifen',
    'view_all_sale' => 'Alle verkaufen anzeigen',
    'total_quantity' => 'Gesamtmenge',
    'pending_shipments' => 'Ausstehende Sendungen',
    'view_paid_sells_only' => 'Nur bezahlte Verkäufe anzeigen',
    'view_due_sells_only' => 'Nur fällige Verkäufe anzeigen',
    'view_partially_paid_sells_only' => 'Nur teilweise bezahlte Verkäufe anzeigen',
    'view_overdue_sells_only' => 'Nur überfällige Verkäufe anzeigen',
    'customer_with_no_sell_one_month' => 'Kunden ohne Verkauf nur ab einem Monat anzeigen',
    'customer_with_no_sell_three_month' => 'Kunden ohne Verkauf erst ab drei Monaten anzeigen',
    'customer_with_no_sell_six_month' => 'Kunden ohne Verkauf erst ab sechs Monaten anzeigen',
    'customer_with_no_sell_one_year' => 'Kunden ohne Verkauf nur ab einem Jahr anzeigen',
    'customer_permissions_tooltip' => 'Um alle Kunden ohne Verkauf zu einem bestimmten Zeitpunkt anzuzeigen, ist die Berechtigung <b>Alle Kunden anzeigen</b> erforderlich, andernfalls wird nur nach Kunden gefiltert, die vom angemeldeten Benutzer erstellt wurden',
    'sell_permissions_tooltip' => 'Um Verkäufe auf der Grundlage des Zahlungsstatus anzuzeigen, ist die Berechtigung <b>Alle Verkäufe anzeigen</b> erforderlich, andernfalls wird nur nach Verkäufen gefiltert, die vom angemeldeten Benutzer erstellt wurden',
    'access_pending_shipments_only' => 'Nur ausstehende Sendungen aufrufen',
    'access_all_sell_return' => 'Zugriff auf alle Verkaufswiederholungen',
    'access_own_sell_return' => 'Zugriff auf eigenen Verkaufsrücklauf',
    'proforma_heading' => 'Proforma-Rechnungstitel',
    'tooltip_proforma_heading' => 'Die Überschrift der Proforma-Rechnung wird verwendet, wenn Kunden Proforma bereitgestellt wird.',
    'line_taxes' => 'Liniensteuern',
    'add_additional_expenses' => 'Zusätzliche Ausgaben hinzufügen',
    'additional_expense_name' => 'Zusatzausgabenname',
    'purchase_additional_expense' => 'Nebenkosten kaufen',
    'qr_code' => 'QR-Code',
    'show_qr_code' => 'QR-Code anzeigen',
    'fields_to_be_shown' => 'Anzuzeigende Felder',
    'business_location_address' => 'Adresse des Unternehmensstandorts',
    'business_tax_1' => 'Gewerbesteuer 1',
    'business_tax_2' => 'Gewerbesteuer 2',
    'invoice_datetime' => 'Rechnungsdatum/Uhrzeit',
    'total_amount_with_tax' => 'Gesamtbetrag mit Steuern',
    'total_bank_transfer' => 'Gesamtüberweisung',
    'total_advance_payment' => 'Gesamtvorauszahlung',
    'commission_agent_label' => 'Beauftragter-Label',
    'show_commission_agent' => 'Kommissionär anzeigen',
    'cannot_change_role' => 'Kann die Rolle nicht ändern',
    'cr' => 'CR',
    'dr' => 'DR',
    'payment_link' => 'Zahlungslink',
    'enable_payment_link' => 'Zahlungslink aktivieren',
    'payment_link_help_text' => 'Durch die Aktivierung können Benutzer Rechnungen über den Zahlungslink bezahlen',
    'invoice_payment' => 'Rechnungszahlung',
    'payment_for_invoice_no' => 'Zahlung für Rechnungsnummer',
    'pending_shipments' => 'Ausstehende Sendungen',
    'sources' => 'Quellen',
    'stripe_secret_key' => 'Geheimschlüssel Streifen',
    'stripe_public_key' => 'Stripe öffentlichen Schlüssel',
    'edit_opening_balance' => 'Eröffnungssaldo bearbeiten',
    'edit_fund_transfer' => 'Geldtransfer bearbeiten',
    'edit_deposit' => 'Einzahlung bearbeiten',
    'deposit_to' => 'Einzahlung an',
    'transfer_from' => 'Übertragen von',
    'edit_account_transaction' => 'Kontotransaktion bearbeiten',
    'delete_account_transaction' => 'Kontotransaktion löschen',
    'add_purchase_payment' => 'Kaufzahlung hinzufügen',
    'edit_purchase_payment' => 'Kaufzahlung bearbeiten',
    'delete_purchase_payment' => 'Kaufzahlung löschen',
    'add_sell_payment' => 'Verkaufszahlung hinzufügen',
    'edit_sell_payment' => 'Verkaufszahlung bearbeiten',
    'delete_sell_payment' => 'Verkaufszahlung löschen',
    'sales_order_heading' => 'Kundenauftragsüberschrift',
    'account_balance' => 'Kontostand',
    'total_balance' => 'Gesamtsaldo',
    'account_balance_tooltip' => 'Gesamtsaldo des jeweiligen Kontos',
    'total_balance_tooltip' => 'Gesamtsaldo aller Konten',
    'sell_additional_expense' => 'Nebenkosten verkaufen',
    'view_export_buttons' => 'Export in Schaltflächen (csv/excel/print/pdf) in Tabellen anzeigen',
    'overall' => 'Gesamt',
    'show_labels' => 'Etiketten anzeigen',
    'zatca_qr' => 'ZATCA (Fatoora) QR-Code',
    'zatca_qr_help' => 'Für Saudi-Arabien Land',
    'customer_irrespective_of_sell' => 'Kunden unabhängig von ihrem Verkauf anzeigen',
    'img_url_help_text' => 'Oder URL des Bildes',
];
