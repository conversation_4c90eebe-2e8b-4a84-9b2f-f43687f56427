<?php

use Illuminate\Database\Migrations\Migration;
use <PERSON><PERSON>\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $insert_data = ['name' => 'product.opening_stock',
            'guard_name' => 'web',
        ];

        Permission::create($insert_data);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
