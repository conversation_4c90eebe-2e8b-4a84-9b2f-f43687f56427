# Stock Control Options Guide

This guide explains the three different stock control modes available in your POS system and how to configure them.

## Overview

The system now offers three distinct approaches to inventory management:

1. **Strict Stock Control** (Default)
2. **Allow Sales Until Zero** (New Feature)
3. **Allow Unlimited Overselling**

## Stock Control Modes

### 1. Strict Stock Control (Default)
- **Behavior**: Blocks any sale that would exceed available stock
- **Use Case**: Businesses that need precise inventory control
- **Configuration**: Both overselling options disabled
- **Example**: If you have 3 units in stock, you cannot sell 5 units

### 2. Allow Sales Until Zero (New Feature) ⭐
- **Behavior**: Allows sales until stock reaches exactly zero, but prevents negative stock
- **Use Case**: Businesses that want to utilize all available stock without overselling
- **Configuration**: Enable "Allow Sales Until Zero" option
- **Example**: If you have 3 units in stock, you can sell exactly 3 units but not 4

### 3. Allow Unlimited Overselling
- **Behavior**: Allows sales even when stock is insufficient (negative stock)
- **Use Case**: Businesses with reliable suppliers who can handle backorders
- **Configuration**: Enable "Allow Overselling" option
- **Example**: If you have 3 units in stock, you can sell 10 units (stock becomes -7)

## How to Configure

### Step 1: Access Business Settings
1. Go to **Settings** → **Business Settings**
2. Click on the **Sales** tab

### Step 2: Choose Your Stock Control Mode

#### For Strict Stock Control (Default):
- ❌ Uncheck "Allow Overselling"
- ❌ Uncheck "Allow Sales Until Zero"

#### For Sales Until Zero (Recommended):
- ❌ Uncheck "Allow Overselling"
- ✅ Check "Allow Sales Until Zero"

#### For Unlimited Overselling:
- ✅ Check "Allow Overselling"
- ❌ Uncheck "Allow Sales Until Zero"

### Step 3: Save Settings
Click **Update** to save your configuration.

## Error Messages

### Before the Update
- **Error**: "ERROR: NOT ALLOWED: Mismatch between sold and purchase quantity"
- **Cause**: Trying to sell more than available stock with strict control enabled

### After the Update
- **Strict Control**: Same error as before
- **Sales Until Zero**: "ERROR: NOT ALLOWED: Mismatch between sold and purchase quantity. Product: [Product Name] Available: [X] Requested: [Y] (Cannot sell more than available stock)"
- **Unlimited Overselling**: No error, sale proceeds

## Recommendations

### For Most Businesses: Use "Allow Sales Until Zero"
This new option provides the best balance:
- ✅ Prevents overselling issues
- ✅ Maximizes stock utilization
- ✅ Avoids lost sales due to small stock discrepancies
- ✅ Maintains inventory accuracy

### For Strict Inventory Control: Use "Strict Stock Control"
Choose this if:
- You need precise inventory tracking
- You have complex supply chain requirements
- You prefer to manually manage low stock situations

### For Flexible Operations: Use "Allow Unlimited Overselling"
Choose this if:
- You have very reliable suppliers
- You can handle backorder situations
- You prioritize sales over inventory accuracy

## Technical Details

The new feature modifies the stock allocation logic in `TransactionUtil.php`:
- Checks current stock level before allowing sales
- Prevents sales that would result in negative stock
- Provides detailed error messages with available vs. requested quantities

## Troubleshooting

### Issue: Still getting mismatch errors after enabling "Sales Until Zero"
**Solution**: 
1. Verify the setting is saved correctly
2. Clear any cached data
3. Check that the product has stock tracking enabled

### Issue: Want to sell exact available stock but getting blocked
**Solution**: 
1. Enable "Allow Sales Until Zero" option
2. This allows selling exactly the available quantity

### Issue: Need to sell more than available stock occasionally
**Solution**: 
1. Enable "Allow Overselling" for unlimited flexibility
2. Or manually adjust stock levels before the sale

## Support

If you encounter any issues with the new stock control features, please check:
1. Business Settings → Sales → Stock control options
2. Product settings → Enable stock tracking
3. Current stock levels in inventory reports

For technical support, refer to the system logs for detailed error messages.
