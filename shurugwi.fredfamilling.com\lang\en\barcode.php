<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Brand Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for Brand CRUD operations.
    |
    */

    'barcodes' => 'Barcodes',
    'barcode_settings' => 'Barcode Settings',
    'manage_your_barcodes' => 'Manage your barcode settings',
    'all_your_barcode' => 'All your barcode settings',
    'setting_name' => 'Sticker Sheet setting Name',
    'setting_description' => 'Sticker Sheet setting Description',
    'added_success' => 'Barcode setting added successfully',
    'updated_success' => 'Barcode setting updated successfully',
    'deleted_success' => 'Barcode setting deleted successfully',
    'add_new_setting' => 'Add new setting',
    'add_barcode_setting' => 'Add barcode sticker setting',
    'edit_barcode_setting' => 'Edit barcode sticker setting',
    'in_in' => 'In Inches',
    'width' => 'Width of sticker',
    'height' => 'Height of sticker',
    'top_margin' => 'Additional top margin',
    'left_margin' => 'Additional left margin',
    'row_distance' => 'Distance between two rows',
    'col_distance' => 'Distance between two columns',
    'blank_stickers' => 'Blank Stickers',
    'stickers_in_one_row' => 'Stickers in one row',
    'delete_confirm' => 'This setting will be deleted \n Are you sure?',
    'set_as_default' => 'Set as default',
    'default' => 'Default',
    'default_set_success' => 'Default set successfully',
    'stickers_in_one_sheet' => 'No. of Stickers per sheet',
    'is_continuous' => 'Continous feed or rolls',
    'products' => 'Products',
    'no_of_labels' => 'No. of labels',
    'info_in_labels' => 'Information to show in Labels',
    'print_name' => 'Product Name',
    'print_variations' => 'Product Variation (recommended)',
    'print_price' => 'Product Price',
    'show_price' => 'Show Price',
    'preview' => 'Preview',
    'print_business_name' => 'Business name',
    'barcode_setting' => 'Barcode setting',
    'paper_width' => 'Paper width',
    'paper_height' => 'Paper height',
    'print_labels' => 'Print Labels',
    'labels' => 'Labels',
];
