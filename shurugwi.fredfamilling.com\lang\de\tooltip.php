<?php

 return [
     'product_stock_alert' => "Produkte mit geringem Lagerbestand. <br/> <small class = 'text-muted'> Basierend auf der Produktalarmmenge, die im Bildschirm Produkt hinzufügen festgelegt wurde. <br> <PERSON><PERSON><PERSON> Sie diese Produkte, bevor der Lagerbestand endet. </small>",
     'payment_dues' => "Ausstehende Zahlung für Einkäufe. <br/> <small class = 'text-muted'> Basierend auf der Zahlungsbedingung des Lieferanten. <br/> Anzeigen von Zahlungen, die innerhalb von 7 Tagen oder weniger bezahlt werden. </small>",
     'input_tax' => 'Gesamtsteuer für Verkäufe innerhalb des ausgewählten Zeitraums gesammelt.',
     'output_tax' => 'Gesamtsteuer für Käufe für den ausgewählten Zeitraum.',
     'tax_overall' => 'Differenz zwischen der gesamten Steuereinnahme und der Gesamtsteuerzahlung innerhalb des ausgewählten Zeitraums.',
     'purchase_due' => 'Total unbezahlter Betrag für Einkäufe.',
     'sell_due' => 'Vom Verkauf zu erhaltender Gesamtbetrag',
     'over_all_sell_purchase' => '-ve value = Zu zahlender Betrag <br> + ve Wert = Zu erhaltender Betrag',
     'no_of_products_for_trending_products' => 'Anzahl der Top-Trending-Produkte, die in der folgenden Tabelle verglichen werden.',
     'top_trending_products' => "Meistverkaufte Produkte Ihres Shops. <br/> <small class = 'text-muted'> Filter anwenden, um Trendprodukte für bestimmte Kategorien, Marken, Geschäftslokationen usw. zu kennen. </small>",
     'sku' => "Eindeutige Produkt-ID oder Stock Keeping Unit <br> <br> Halte es frei, um automatisch sku zu generieren. <br> <small class = 'text-muted'> Sie können das SKU-Präfix in den Business-Einstellungen ändern . </small> ",
     'enable_stock' => 'Aktivieren oder deaktivieren Sie die Bestandsverwaltung für ein Produkt.',
     'alert_quantity' => "Werden Sie gewarnt, wenn der Produktbestand die angegebene Menge erreicht oder unterschreitet. <br> <br> <small class = 'text-muted'> Produkte mit geringem Bestand werden im Dashboard - Produktbestands-Warnmeldung angezeigt. </small>",
     'product_type' => '<b> Einzelprodukt </ b>: Produkt ohne Variationen. <br> <b> Variables Produkt </ b>: Produkt mit Variationen wie Größe, Farbe usw.',
     'profit_percent' => "Standard-Gewinnmarge für das Produkt. <br> <small class = 'text-muted'> (<i> Sie können die Standard-Gewinnspanne in den Business-Einstellungen verwalten. </ I>) </small>",
     'pay_term' => "Zahlungen für Käufe innerhalb des angegebenen Zeitraums. <br/> <small class = 'text-muted'> Alle anstehenden oder fälligen Zahlungen werden im Dashboard angezeigt - Abschnitt Zahlungspflichtig </small>",
     'order_status' => 'Produkte in diesem Kauf werden nur dann zum Verkauf angeboten, wenn der <b> Bestellstatus </ b> <b> empfangen ist </ b>.',
     'purchase_location' => 'Geschäftsstandort, an dem das gekaufte Produkt zum Verkauf angeboten wird.',
     'sale_location' => 'Geschäftsstandort von wo aus Sie verkaufen möchten',
     'sale_discount' => "Setzen Sie 'Default Sale Discount' für alle Verkäufe in den Business-Einstellungen. Klicken Sie auf das Bearbeitungssymbol unten, um den Rabatt hinzuzufügen / zu aktualisieren.",
     'sale_tax' => 'Legen Sie die Standardverkaufssteuer für alle Verkäufe in den Geschäftseinstellungen fest. Klicken Sie auf das Bearbeitungssymbol, um die Umsatzsteuer hinzuzufügen / zu aktualisieren.',
     'default_profit_percent' => "Standard-Gewinnspanne eines Produkts. <br> <small class = 'text-muted'> Wird verwendet, um den Verkaufspreis basierend auf dem eingegebenen Einkaufspreis zu berechnen. <br/> Sie können diesen Wert für einzelne Produkte ändern, indem Sie </small> hinzufügen ",
     'fy_start_month' => 'Startmonat des Geschäftsjahres für Ihr Unternehmen',
     'business_tax' => 'Registrierte Steuernummer für Ihr Unternehmen.',
     'invoice_scheme' => "Rechnungsschema bezeichnet das Format der Rechnungsnummerierung. Wählen Sie das für diesen Unternehmensstandort zu verwendende Schema aus. <small class = 'text-muted'> <i> Sie können ein neues Rechnungsschema </ b> in den Rechnungseinstellungen hinzufügen </ i > </small> ",
     'invoice_layout' => "Rechnungslayout für diesen Unternehmensstandort verwenden <br> <small class = 'text-muted'> (<i> Sie können ein neues <b> Rechnungslayout </ b> in <b> Rechnungseinstellungen <b> </ i>) </small> ",
     'invoice_scheme_name' => 'Geben Sie dem Rechnungsschema einen aussagekräftigen Namen.',
     'invoice_scheme_prefix' => 'Präfix für ein Rechnungsschema. <br> Ein Präfix kann ein benutzerdefinierter Text oder ein aktuelles Jahr sein. Beispiel: # XXXX0001, # 2018-0002',
     'invoice_scheme_start_number' => "Startnummer für die Rechnungsnummerierung. <br> <small class = 'text-muted'> Sie können 1 oder jede andere Nummer eingeben, ab der die Nummerierung beginnt. </small>",
     'invoice_scheme_count' => 'Gesamtzahl der für das Rechnungsschema generierten Rechnungen',
     'invoice_scheme_total_digits' => 'Länge der Rechnungsnummer ohne Rechnungspräfix',
     'tax_groups' => 'Gruppensteuersätze - oben definiert, die in Kombination in Kauf / Verkauf-Abschnitten verwendet werden.',
     'unit_allow_decimal' => 'Decimals ermöglicht es Ihnen, die verwandten Produkte in Bruchteilen zu verkaufen.',
     'print_label' => 'Produkte hinzufügen -> Informationen auswählen, die in Etiketten angezeigt werden sollen -> Barcode-Einstellung auswählen -> Etikettenvorschau -> Drucken',
     'expense_for' => 'Wählen Sie den Benutzer, auf den sich die Ausgaben beziehen. <I> (Optional) </ i> <br/> <small> Beispiel: Gehalt eines Mitarbeiters. </small>',
     'all_location_permission' => 'Wenn <b> Alle Standorte </ b> ausgewählt ist, hat diese Rolle die Berechtigung, auf alle Unternehmensstandorte zuzugreifen',
     'dashboard_permission' => 'Wenn diese Option deaktiviert ist, wird nur die Willkommensnachricht in Home angezeigt.',
     'access_locations_permission' => 'Wählen Sie alle Orte aus, auf die diese Rolle zugreifen kann. Alle Daten für den ausgewählten Ort werden nur dem Benutzer angezeigt. <br/> <br/> <small> Beispiel: Sie können damit <i> Store Manager / Kassierer definieren / Lagerleiter / Filialleiter, </ i> eines bestimmten Standorts. </small> ',
     'print_receipt_on_invoice' => 'Automatisches Drucken der Rechnung beim Finalisieren aktivieren oder deaktivieren',
     'receipt_printer_type' => '<i> Browserbasiertes Drucken </ i>: Druckdialogfeld im Browser mit Vorschau der Rechnung anzeigen <br/> <br/> <i> Konfigurierten Belegdrucker verwenden </ i>: Wählen Sie einen konfigurierten Beleg / Thermodrucker für Drucken',
     'adjustment_type' => '<i> Normal </ i>: Anpassung für normale Gründe wie Leckage, Schaden usw. <br/> <br/> <i> Abnormal </ i>: Anpassung aus Gründen wie Feuer, Unfall etc.',
     'total_amount_recovered' => 'Betrag, der von der Versicherung oder dem Verkauf von Schrott oder von anderen zurückgewonnen wird',
     'express_checkout' => 'Markiere komplette Barzahlung und checke',
     'total_card_slips' => 'Gesamtzahl der in diesem Register verwendeten Kartenzahlungen',
     'total_cheques' => 'Gesamtzahl der in diesem Register verwendeten Schecks',
     'capability_profile' => "Die Unterstützung für Befehle und Codepages variiert zwischen Druckeranbietern und Modellen. Wenn Sie sich nicht sicher sind, ist es eine gute Idee, das 'einfache' Fähigkeitsprofil zu verwenden.",
     'purchase_different_currency' => 'Wählen Sie diese Option, wenn Sie in einer anderen Währung als Ihrer Geschäftswährung kaufen',
     'currency_exchange_factor' => "1 Kaufwährung = ? Basiswährung <br> <small class='text-muted'> Sie können 'Kauf in anderer Währung' in den Geschäftseinstellungen aktivieren / deaktivieren. </small>",
     'accounting_method' => 'Buchhaltungsmethode',
     'transaction_edit_days' => 'Anzahl der Tage ab Transaktionsdatum, bis zu der eine Transaktion bearbeitet werden kann.',
     'stock_expiry_alert' => "Liste der Aktien, die ablaufen in :days Tage <br> <small class='text-muted'> Sie können die Anzahl der Tage in den Business-Einstellungen festlegen </small>",
 ];
