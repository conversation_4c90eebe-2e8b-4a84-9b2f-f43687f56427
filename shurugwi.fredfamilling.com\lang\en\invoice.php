<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Invoice Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are for invoice screen
    |
    */
    'invoice_settings' => 'Invoice Settings',
    'manage_your_invoices' => 'Manage your invoice settings',
    'all_your_invoice_schemes' => 'All your invoice schemes',
    'added_success' => 'Invoice setting added successfully',
    'updated_success' => 'Invoice setting updated successfully',
    'deleted_success' => 'Invoice setting deleted successfully',
    'add_invoice' => 'Add new invoice scheme',
    'edit_invoice' => 'Edit invoice scheme',
    'name' => 'Name',
    'prefix' => 'Prefix',
    'start_number' => 'Start from',
    'total_digits' => 'Number of digits',
    'preview' => 'Preview',
    'not_selected' => 'Not selected',
    'invoice_count' => 'Invoice Count',
    'invoice_schemes' => 'Invoice Schemes',
    'invoice_layouts' => 'Invoice Layouts',
    'invoice_layout' => 'Invoice layout',
    'all_your_invoice_layouts' => 'All your invoice layouts',
    'add_invoice_layout' => 'Add new invoice layout',
    'layout_name' => 'Layout name',
    'invoice_scheme' => 'Invoice scheme',
    'header_text' => 'Header text',
    'invoice_no_prefix' => 'Invoice no. label',
    'invoice_heading' => 'Invoice heading',
    'sub_total_label' => 'Subtotal label',
    'discount_label' => 'Discount label',
    'tax_label' => 'Tax label',
    'total_label' => 'Total label',
    'fields_to_be_shown_in_address' => 'Fields to be shown in location address',
    'highlight_color' => 'Highlight color',
    'footer_text' => 'Footer text',
    'layout_added_success' => 'Invoice layout added successfully',
    'edit_invoice_layout' => 'Edit invoice layout',
    'layout_updated_success' => 'Invoice layout updated successfully',
    'used_in_locations' => 'Used in locations',
    'show_business_name' => 'Show business name',
    'show_location_name' => 'Show location name',
    'show_mobile_number' => 'Mobile number',
    'show_alternate_number' => 'Alternate number',
    'show_email' => 'Email',
    'show_tax_1' => 'Tax 1 details',
    'show_tax_2' => 'Tax 2 details',
    'fields_to_shown_for_communication' => 'Fields for Communication details',
    'fields_to_shown_for_tax' => 'Fields for Tax details',
    'invoice_logo' => 'Invoice Logo',
    'show_logo' => 'Show invoice Logo',
    'show_barcode' => 'Show Barcode',
    'total_due_label' => 'Total Due Label',
    'invoice_heading_not_paid' => 'Heading Suffix for not paid',
    'invoice_heading_paid' => 'Heading Suffix for paid',
    'show_payments' => 'Show Payment information',
    'show_customer' => 'Show Customer information',
    'paid_label' => 'Amount Paid Label',
    'customer_label' => 'Customer Label',
    'invoice_scheme_for_pos' => 'Invoice scheme for POS',
    'invoice_scheme_for_sale' => 'Invoice scheme for sale',
    'number_type' => 'Numbering Type',
    'sequential' => 'Sequential',
    'random' => 'Aleatory/Random',
    'number_type_tooltip' => 'Sequential will generate number serially like 1,2,3,4 <br/> Aleatory will generate number randomly'
];
