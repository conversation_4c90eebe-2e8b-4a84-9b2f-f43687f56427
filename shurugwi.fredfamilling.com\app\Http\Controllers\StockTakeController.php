<!-- stock_take.blade.php -->
@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>Stock Take</h1>

                <!-- Button to display the form -->
                <button class="btn btn-primary mb-3" id="showStockTakeForm">Perform Stock Take</button>

                <!-- Stock Take Form (initially hidden) -->
                <form action="{{ route('stocktake.store') }}" method="POST" id="stockTakeForm" style="display: none;">
                    @csrf

                    <!-- Form fields go here -->
                    @foreach ($products as $product)
                        <div class="form-group">
                            <label for="physical_count[{{ $product->id }}]">{{ $product->name }}</label>
                            <input type="number" class="form-control" id="physical_count[{{ $product->id }}]" name="physical_count[{{ $product->id }}]" value="{{ old('physical_count.'.$product->id) ?? $product->stock }}" min="0" required>
                        </div>
                    @endforeach

                    <button type="submit" class="btn btn-success">Submit Stock Take</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // JavaScript to show/hide the form when the button is clicked
        document.getElementById('showStockTakeForm').addEventListener('click', function() {
            document.getElementById('stockTakeForm').style.display = 'block';
        });
    </script>
@endsection
