<?php

 return [
     'product_stock_alert' => "منتجات منخفضة المخزون. <br/> <small class = 'text-muted'> استنادًا إلى كمية تنبيه المنتج المحددة في شاشة إضافة المنتج. <br> اشتر هذا المنتج قبل نهاية المخزون. </small>",
     'payment_dues' => "دفعة مشتريات معلقة. <br/> <small class = 'text-muted'> استنادًا إلى حد دفع المورد. <br/> عرض الدفعات المدفوعة خلال 7 أيام أو أقل. </small>",
     'input_tax' => 'مجموع الضرائب المحصلة للمبيعات في الفترة المحددة.',
     'output_tax' => 'مجموع الضرائب المدفوعة للمشتريات للفترة المحددة.',
     'tax_overall' => 'الفرق بين إجمالي الضرائب المحصلة وإجمالي الضرائب المدفوعة خلال الفترة المختارة.',
     'purchase_due' => 'المبلغ الإجمالي غير المسدد للمشتريات.',
     'sell_due' => 'المبلغ الإجمالي لتلقي المبيعات',
     'over_all_sell_purchase' => '- القيمة = المبلغ الواجب دفعه <br> + القيمة = المبلغ الواجب استلامه',
     'no_of_products_for_trending_products' => 'عدد المنتجات الشائعة للمقارنة في الجدول أدناه.',
     'top_trending_products' => "أفضل المنتجات مبيعا لمتجرك. <br/> <small class = 'text-muted'> طبق التصفيات لإيجاد المنتجات الشائعة  لفئة محددة ، والعلامة التجارية ، ومكان العمل ، إلخ. </small>",
     'sku' => "معرف المنتج الفريد أو وحدة إدارة المخزون <br> <br>يبقيه فارغة لتوليد sku تلقائيا. <br> <small class = 'text-muted'> يمكنك تغيير بادئة sku في الإعدادات التجارية </small>",
     'enable_stock' => 'تفعيل أو تعطيل إدارة المخزون لمنتج ما.',
     'alert_quantity' => "يتم تنبيهك عند وصول مخزون المنتجات أو انخفاضه إلى أقل من الكمية المحددة. <br> <br> <small class = 'text-muted'> سيتم عرض المنتجات ذات المخزون المنخفض في لوحة المعلومات - قسم تنبيه مخزون المنتجات. </small>",
     'product_type' => '<b> منتج واحد </b>: المنتج بدون تبيانات <br> <b> المنتج المتباين </b>: المنتج ذو الاختلافات مثل الحجم واللون وما إلى ذلك',
     'profit_percent' => "هامش الربح الافتراضي للمنتج. <br> <small class = 'text-muted'> (<i> يمكنك إدارة الهامش الافتراضي في إعدادات المؤسسة. </small>",
     'pay_term' => "المدفوعات التي يتعين دفعها مقابل المشتريات في الفترة الزمنية المحددة. <br/> <small class = 'text-muted'> سيتم عرض جميع الدفعات المعلقة والحالية في الرئيسية - القسم المستحق للدفع </small>",
     'order_status' => 'ستكون المنتجات المتوفرة في عملية الشراء هذه متاحة للبيع فقط إذا كانت <b> حالة الطلب </b> هي <b> العناصر مستلمة </b>.',
     'purchase_location' => 'الفرع الذى سيكون المنتج المشترى متاحًا للبيع.',
     'sale_location' => 'الفرع الذي تريد البيع منه',
     'sale_discount' => 'قم بتعيين الخصم على البيع الافتراضي لجميع المبيعات في إعدادات الشركة .اضغط على أيقونة التعديل أدناه لإضافة / تحديث الخصم.',
     'sale_tax' => 'عيّن ضريبة المبيعات الافتراضية لجميع المبيعات في إعدادات الشركة ، انقر على رمز التعديل أدناه لإضافة / تحديث ضريبة الطلبات. ',
     'default_profit_percent' => "هامش الربح الافتراضي للمنتج. <br> <small class = 'text-muted'> يستخدم لحساب سعر البيع بناءً على سعر الشراء الذي تم إدخاله. <br/> يمكنك تغيير هذه القيمة للمنتجات الفردية عن طريق الإضافة </small>",
     'fy_start_month' => 'بداية السنة المالية لعملك',
     'business_tax' => 'الرقم الضريبي المسجل لنشاطك التجاري.',
     'invoice_scheme' => "يشير نموذج الفاتورة إلى نسق ترقيم الفاتورة. حدد النموذج المراد استخدامه لهذا الفرع <small class = 'text-muted'> <i> يمكنك إضافة نموذج فاتورة جديد </b> في إعدادات الفاتورة </i> </small>",
     'invoice_layout' => "تصميم الفاتورة لاستخدامه في هذه الخاصية <br> <small class = 'text-muted'> (<i> يمكنك إضافة تصميم <> فاتورة جديد <b/> في <b> إعدادات لـ الفاتورة <b> </i>) </small>",
     'invoice_scheme_name' => 'امنح نموذج الفاتورة اسمًا قصيرًا وذو معنى.',
     'invoice_scheme_prefix' => 'البادئة لمخطط الفاتورة. <br> البادئة يمكن أن تكون نصًا مخصصًا أو عامًا حاليًا. على سبيل المثال: # XXXX0001 ، # 2018-0002',
     'invoice_scheme_start_number' => "رقم البداية لترقيم الفاتورة.  <small/> يمكنك إنشاء رقم واحد أو أي رقم آخر يبدأ منه الترقيم. <br><small class = 'text-muted'> ",
     'invoice_scheme_count' => 'إجمالي عدد الفواتير التي تم إنشاؤها لنموذج الفاتورة',
     'invoice_scheme_total_digits' => 'طول رقم الفاتورة باستثناء بادئة الفاتورة',
     'tax_groups' => 'مجموعة  معدل ضريبة - المحدد أعلاه ، لاستخدامه في تركيبة في أقسام المبيعات / المشتريات.',
     'unit_allow_decimal' => 'الأرقام العشرية تسمح لك ببيع المنتجات ذات الصلة في الكسور.',
     'print_label' => 'إضافة منتجات -> اختر المعلومات التي تريد عرضها في الملصقات -> حدد إعداد الباركود -> معاينة الملصقات -> طباعة',
     'expense_for' => 'اختر المستخدم الذي تتعلق به المصاريف.<I> (اختياري) </i> <br/> <small> مثال: راتب الموظف. </small>',
     'all_location_permission' => 'إذا تم تحديد <b> جميع الفروع </b> ، فسيكون لهذه الصلاحية إذن للوصول إلى جميع الفروع العمل',
     'dashboard_permission' => 'إذا لم يتم تحديده ، فسيتم عرض رسالة الترحيب فقط في الصفحة الرئيسية.',
     'access_locations_permission' => 'اختر جميع الفروع التي يمكن لهذه الصلاحية الوصول إليها. سيتم عرض جميع البيانات في الفرع المحدد للمستخدم فقط. <br/> <br/> <small> على سبيل المثال: يمكنك استخدام هذا لتعيين مدير المتجر / أمين الصندوق / مدير المخزون / مدير الفرع ، </i> إلى فرع معين. </small>',
     'print_receipt_on_invoice' => 'تفعيل أو تعطيل الطباعة التلقائية للفاتورة أثناء انهاء الفاتورة',
     'receipt_printer_type' => '<i> الطباعة المستندة إلى المتصفح</i>: عرض مربع حوار الطباعة في المتصفح الذي يحتوي على معاينة للفواتير <br/> <br/> <i> استخدم اعدادات طابعة الإيصال</i >: حدد إيصال / طابعة حرارية تم تكوينها للطباعة',
     'adjustment_type' => '<I> عادي </i>: التعديل لأسباب طبيعية مثل التسريب ، التلف ، إلخ <br/> <br/> <i> غير طبيعي </i>: التعديل لأسباب مثل الحريق والحوادث ، وما إلى ذلك',
     'total_amount_recovered' => 'المبلغ المسترد من التأمين أو بيع التلفيات أو غيرها',
     'express_checkout' => 'حدد الدفع السريع',
     'total_card_slips' => 'العدد الإجمالي لمدفوعات البطاقات المستخدمة في هذه المناوبة',
     'total_cheques' => 'مجموع عدد الشيكات المستخدمة في هذه المناوبة',
     'capability_profile' => 'يختلف دعم الأوامر وصفحات التعليمات البرمجية بين موردي الطابعات والنماذج ، وإذا لم تكن متأكدًا ، فمن الأفضل استخدام ملف التعريفات البسيطة.',
     'purchase_different_currency' => 'حدد هذا الخيار إذا كنت تشتري بعملة مختلفة عن العملة التجارية الخاصة بك',
     'currency_exchange_factor' => "1 عملة الشراء =؟ العملة الأساسية <br> <small class = 'text-muted'> يمكنك تفعيل / تعطيل \"الشراء بعملة أخرى\" في إعدادات المؤسسة. </small>",
     'accounting_method' => 'طريقة المحاسبة',
     'transaction_edit_days' => 'عدد الأيام من تاريخ المعاملة حتى لا يمكن تغيير المعاملة.',
     'stock_expiry_alert' => "قائمة المخزونات التي تنتهي صلاحيتها في days: يوم(أيام) <small class = 'text-muted'> يمكنك تعيين عدد الأيام في إعدادات المؤسسة </small>",
 ];
