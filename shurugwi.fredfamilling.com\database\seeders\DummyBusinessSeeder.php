<?php

namespace Database\Seeders;

use App\NotificationTemplate;
use App\User;
use App\Utils\InstallUtil;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DummyBusinessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();

        $password = Hash::make('123456');

        // $timezone = 'America/Phoenix'
        // config(['app.timezone' => $timezone]);
        // date_default_timezone_set($timezone);

        $today = \Carbon::now()->format('Y-m-d H:i:s');
        $yesterday = \Carbon::now()->subDays(2)->format('Y-m-d H:i:s');
        $last_week = \Carbon::now()->subDays(7)->format('Y-m-d H:i:s');
        $last_15th_day = \Carbon::now()->subDays(15)->format('Y-m-d H:i:s');
        $last_month = \Carbon::now()->subDays(30)->format('Y-m-d H:i:s');

        $next_6_month = \Carbon::now()->addMonths(6)->format('Y-m-d');
        $next_12_month = \Carbon::now()->addMonths(12)->format('Y-m-d');
        $next_18_month = \Carbon::now()->addMonths(18)->format('Y-m-d');

        $start_of_week = \Carbon::now()->startOfWeek()->format('Y-m-d');
        $end_of_week = \Carbon::now()->endOfWeek()->format('Y-m-d');

        DB::statement('SET FOREIGN_KEY_CHECKS = 0');

        $shortcuts = '{"pos":{"express_checkout":"shift+e","pay_n_ckeckout":"shift+p","draft":"shift+d","cancel":"shift+c","edit_discount":"shift+i","edit_order_tax":"shift+t","add_payment_row":"shift+r","finalize_payment":"shift+f","recent_product_quantity":"f2","add_new_product":"f4"}}';

        $prefixes = '{"purchase":"PO","stock_transfer":"ST","stock_adjustment":"SA","sell_return":"CN","expense":"EP","contacts":"CO","purchase_payment":"PP","sell_payment":"SP","business_location":"BL"}';

        $business = [
            ['id' => '1', 'name' => 'Awesome Shop', 'currency_id' => '2', 'start_date' => '2018-01-01', 'tax_number_1' => '**********', 'tax_label_1' => 'GSTIN', 'tax_number_2' => null, 'tax_label_2' => null, 'default_sales_tax' => null, 'default_profit_percent' => '25.00', 'owner_id' => '1', 'time_zone' => 'America/Phoenix', 'fy_start_month' => '1', 'accounting_method' => 'fifo', 'default_sales_discount' => '10.00', 'sell_price_tax' => 'includes', 'logo' => null, 'sku_prefix' => 'AS', 'enable_product_expiry' => '0', 'expiry_type' => 'add_expiry', 'on_product_expiry' => 'keep_selling', 'stop_selling_before' => '0', 'enable_tooltip' => '1', 'purchase_in_diff_currency' => '0', 'purchase_currency_id' => null, 'p_exchange_rate' => '1.000', 'transaction_edit_days' => '30', 'stock_expiry_alert_days' => '30', 'keyboard_shortcuts' => $shortcuts, 'pos_settings' => null, 'enable_brand' => '1', 'enable_category' => '1', 'enable_sub_category' => '1', 'enable_price_tax' => '1', 'enable_purchase_status' => '1', 'enable_lot_number' => '0', 'default_unit' => null, 'enable_racks' => '0', 'enable_row' => '0', 'enable_position' => '0', 'enable_editing_product_from_purchase' => '1', 'sales_cmsn_agnt' => null, 'item_addition_method' => '1', 'enable_inline_tax' => '1', 'currency_symbol_placement' => 'before', 'enabled_modules' => '["purchases","add_sale","pos_sale","stock_transfers","stock_adjustment","expenses","account"]', 'date_format' => 'm/d/Y', 'time_format' => '24', 'repair_settings' => null, 'ref_no_prefixes' => $prefixes, 'created_at' => '2018-01-04 02:15:19', 'updated_at' => '2018-01-04 02:17:08', 'common_settings' => null],
            ['id' => '2', 'name' => 'Awesome Pharmacy', 'currency_id' => '2', 'start_date' => '2018-04-10', 'tax_number_1' => '**********', 'tax_label_1' => 'VAT', 'tax_number_2' => null, 'tax_label_2' => null, 'default_sales_tax' => null, 'default_profit_percent' => '25.00', 'owner_id' => '4', 'time_zone' => 'America/Chicago', 'fy_start_month' => '1', 'accounting_method' => 'fifo', 'default_sales_discount' => null, 'sell_price_tax' => 'includes', 'logo' => null, 'sku_prefix' => 'AP', 'enable_product_expiry' => '1', 'expiry_type' => 'add_manufacturing', 'on_product_expiry' => 'stop_selling', 'stop_selling_before' => '0', 'enable_tooltip' => '1', 'purchase_in_diff_currency' => '0', 'purchase_currency_id' => null, 'p_exchange_rate' => '1.000', 'transaction_edit_days' => '30', 'stock_expiry_alert_days' => '30', 'keyboard_shortcuts' => $shortcuts, 'pos_settings' => null, 'enable_brand' => '1', 'enable_category' => '1', 'enable_sub_category' => '1', 'enable_price_tax' => '1', 'enable_purchase_status' => '1', 'enable_lot_number' => '1', 'default_unit' => '4', 'enable_racks' => '0', 'enable_row' => '0', 'enable_position' => '0', 'enable_editing_product_from_purchase' => '1', 'sales_cmsn_agnt' => null, 'item_addition_method' => '1', 'enable_inline_tax' => '0', 'currency_symbol_placement' => 'before', 'enabled_modules' => '["purchases","add_sale","pos_sale","stock_transfers","stock_adjustment","expenses","account"]', 'date_format' => 'm/d/Y', 'time_format' => '24', 'repair_settings' => null, 'ref_no_prefixes' => $prefixes, 'created_at' => '2018-04-10 08:12:40', 'updated_at' => '2018-04-10 10:21:38', 'common_settings' => null],
            ['id' => '3', 'name' => 'Ultimate Electronics', 'currency_id' => '2', 'start_date' => '2018-04-10', 'tax_number_1' => '***********', 'tax_label_1' => 'GST', 'tax_number_2' => null, 'tax_label_2' => null, 'default_sales_tax' => null, 'default_profit_percent' => '25.00', 'owner_id' => '5', 'time_zone' => 'America/Chicago', 'fy_start_month' => '1', 'accounting_method' => 'fifo', 'default_sales_discount' => null, 'sell_price_tax' => 'includes', 'logo' => null, 'sku_prefix' => 'AE', 'enable_product_expiry' => '0', 'expiry_type' => 'add_expiry', 'on_product_expiry' => 'keep_selling', 'stop_selling_before' => '0', 'enable_tooltip' => '1', 'purchase_in_diff_currency' => '0', 'purchase_currency_id' => null, 'p_exchange_rate' => '1.000', 'transaction_edit_days' => '30', 'stock_expiry_alert_days' => '30', 'keyboard_shortcuts' => $shortcuts, 'pos_settings' => null, 'enable_brand' => '1', 'enable_category' => '1', 'enable_sub_category' => '1', 'enable_price_tax' => '1', 'enable_purchase_status' => '1', 'enable_lot_number' => '0', 'default_unit' => '5', 'enable_racks' => '0', 'enable_row' => '0', 'enable_position' => '0', 'enable_editing_product_from_purchase' => '1', 'sales_cmsn_agnt' => null, 'item_addition_method' => '1', 'enable_inline_tax' => '0', 'currency_symbol_placement' => 'before', 'enabled_modules' => '["purchases","add_sale","pos_sale","stock_transfers","stock_adjustment","expenses","account","subscription"]', 'date_format' => 'm/d/Y', 'time_format' => '24', 'repair_settings' => null, 'ref_no_prefixes' => $prefixes, 'created_at' => '2018-04-10 10:46:15', 'updated_at' => '2018-04-10 11:53:35', 'common_settings' => '{"enable_product_warranty":"1","default_datatable_page_entries":"25"}'],
            ['id' => '4', 'name' => 'Awesome Services', 'currency_id' => '124', 'start_date' => '2018-03-10', 'tax_number_1' => '**********', 'tax_label_1' => 'GST', 'tax_number_2' => null, 'tax_label_2' => null, 'default_sales_tax' => null, 'default_profit_percent' => '25.00', 'owner_id' => '6', 'time_zone' => 'America/Chicago', 'fy_start_month' => '1', 'accounting_method' => 'fifo', 'default_sales_discount' => null, 'sell_price_tax' => 'includes', 'logo' => null, 'sku_prefix' => 'AS', 'enable_product_expiry' => '0', 'expiry_type' => 'add_expiry', 'on_product_expiry' => 'keep_selling', 'stop_selling_before' => '0', 'enable_tooltip' => '1', 'purchase_in_diff_currency' => '0', 'purchase_currency_id' => null, 'p_exchange_rate' => '1.000', 'transaction_edit_days' => '30', 'stock_expiry_alert_days' => '30', 'keyboard_shortcuts' => $shortcuts, 'pos_settings' => null, 'enable_brand' => '1', 'enable_category' => '1', 'enable_sub_category' => '0', 'enable_price_tax' => '1', 'enable_purchase_status' => '1', 'enable_lot_number' => '0', 'default_unit' => null, 'enable_racks' => '0', 'enable_row' => '0', 'enable_position' => '0', 'enable_editing_product_from_purchase' => '1', 'sales_cmsn_agnt' => null, 'item_addition_method' => '1', 'enable_inline_tax' => '0', 'currency_symbol_placement' => 'before', 'enabled_modules' => '["purchases","add_sale","pos_sale","expenses","account","service_staff"]', 'date_format' => 'm/d/Y', 'time_format' => '24', 'repair_settings' => '{"repair_tc_condition":"<p><strong>30- Day Service Warranty :<\\/strong> If your device encounters the same issue within 30 days of the repair, we will fix the issue at our own cost with no further question asked. You have your peace of mind.&nbsp;<\\/p>\\r\\n<p><strong>Service Disclaimers :<\\/strong> Please make sure that you collect your item within two months of servic-ing, Otherwise, we will not be liable if your item gets lost or damaged.&nbsp;<\\/p>\\r\\n<p><strong>Terms &amp;Conditions:<\\/strong> Please also be aware that we can only service your gadget at your own risk. Even though we do our best in \\"Awesome Services\\" to make things better. However, in the worst-case scenario, your gadget may be damaged during repairs. We will not be liable if such things happen. Visit our website to know more our Policy.<\\/p>","job_sheet_prefix":"JS","problem_reported_by_customer":"No backup, Battery life mediocre, Slow boot and application launch, Too glossy screen, Touchpad requires improvement, Keyboard requires improvement, RAM needs to be expanded, Lacks SD card slot","product_condition":"No physical damage, Screen fine, Screen scratches, body broken, Wet in water, colourful display, Great battery life, Excellent audio quality, Lacks SD card slot","product_configuration":"2 Gb RAM, 2GB ROM, Cpu,  Core i3 Processor, Core i5 Processor, Core i7 Processor, 16GB RAM, 4 GB RAM, 1TB HDD, 520 SSD, USB Type-C port, USB Type-A prot, 1080p IPS Display","default_status":"3"}', 'ref_no_prefixes' => $prefixes, 'created_at' => '2018-04-10 12:20:42', 'updated_at' => '2018-04-10 12:23:40', 'common_settings' => null],

            ['id' => '5', 'name' => 'Awesome Restaurant', 'currency_id' => '2', 'start_date' => '2018-07-12', 'tax_number_1' => '**********', 'tax_label_1' => 'IGST', 'tax_number_2' => '**********', 'tax_label_2' => 'SGST', 'default_sales_tax' => null, 'default_profit_percent' => '25.00', 'owner_id' => '7', 'time_zone' => 'America/Chicago', 'fy_start_month' => '1', 'accounting_method' => 'fifo', 'default_sales_discount' => null, 'sell_price_tax' => 'includes', 'logo' => null, 'sku_prefix' => 'AR', 'enable_product_expiry' => '0', 'expiry_type' => 'add_expiry', 'on_product_expiry' => 'keep_selling', 'stop_selling_before' => '0', 'enable_tooltip' => '1', 'purchase_in_diff_currency' => '0', 'purchase_currency_id' => null, 'p_exchange_rate' => '1.000', 'transaction_edit_days' => '30', 'stock_expiry_alert_days' => '30', 'keyboard_shortcuts' => $shortcuts, 'pos_settings' => '{"disable_pay_checkout":0,"disable_draft":0,"disable_express_checkout":0,"hide_product_suggestion":0,"hide_recent_trans":0,"disable_discount":0,"disable_order_tax":0}', 'enable_brand' => '1', 'enable_category' => '1', 'enable_sub_category' => '1', 'enable_price_tax' => '1', 'enable_purchase_status' => '1', 'enable_lot_number' => '0', 'default_unit' => null, 'enable_racks' => '0', 'enable_row' => '0', 'enable_position' => '0', 'enable_editing_product_from_purchase' => '1', 'sales_cmsn_agnt' => null, 'item_addition_method' => '1', 'enable_inline_tax' => '0', 'currency_symbol_placement' => 'before', 'enabled_modules' => '["purchases","add_sale","pos_sale","stock_transfers","stock_adjustment","expenses","tables","modifiers","service_staff","kitchen","types_of_service","booking"]', 'date_format' => 'm/d/Y', 'time_format' => '24', 'repair_settings' => null, 'ref_no_prefixes' => $prefixes, 'created_at' => '2018-07-13 16:42:11', 'updated_at' => '2018-07-13 07:38:24', 'common_settings' => null],
            ['id' => '6', 'name' => 'Manufacturers Demo', 'currency_id' => '124', 'start_date' => '2019-08-18', 'tax_number_1' => null, 'tax_label_1' => null, 'tax_number_2' => null, 'tax_label_2' => null, 'default_sales_tax' => null, 'default_profit_percent' => '25.00', 'owner_id' => '12', 'time_zone' => 'Asia/Kolkata', 'fy_start_month' => '1', 'accounting_method' => 'fifo', 'default_sales_discount' => null, 'sell_price_tax' => 'includes', 'logo' => null, 'sku_prefix' => null, 'enable_product_expiry' => '0', 'expiry_type' => 'add_expiry', 'on_product_expiry' => 'keep_selling', 'stop_selling_before' => '0', 'enable_tooltip' => '1', 'purchase_in_diff_currency' => '0', 'purchase_currency_id' => null, 'p_exchange_rate' => '1.000', 'transaction_edit_days' => '30', 'stock_expiry_alert_days' => '30', 'keyboard_shortcuts' => $shortcuts, 'pos_settings' => '{"disable_pay_checkout":0,"disable_draft":0,"disable_express_checkout":0,"hide_product_suggestion":0,"hide_recent_trans":0,"disable_discount":0,"disable_order_tax":0,"is_pos_subtotal_editable":0}', 'enable_brand' => '1', 'enable_category' => '1', 'enable_sub_category' => '1', 'enable_price_tax' => '1', 'enable_purchase_status' => '1', 'enable_lot_number' => '0', 'default_unit' => null, 'enable_racks' => '0', 'enable_row' => '0', 'enable_position' => '0', 'enable_editing_product_from_purchase' => '1', 'sales_cmsn_agnt' => null, 'item_addition_method' => '1', 'enable_inline_tax' => '0', 'currency_symbol_placement' => 'before', 'enabled_modules' => '["purchases","add_sale","pos_sale","stock_transfers","stock_adjustment","expenses"]', 'date_format' => 'm/d/Y', 'time_format' => '24', 'repair_settings' => null, 'ref_no_prefixes' => $prefixes, 'created_at' => '2019-08-18 18:41:28', 'updated_at' => '2019-08-18 18:43:22', 'common_settings' => null],
        ];
        DB::table('business')->insert($business);

        $business_locations = [
            ['id' => '1', 'business_id' => '1', 'location_id' => null, 'name' => 'Awesome Shop', 'landmark' => 'Linking Street', 'country' => 'USA', 'state' => 'Arizona', 'city' => 'Phoenix', 'zip_code' => '85001', 'invoice_scheme_id' => '1', 'invoice_layout_id' => '1', 'sale_invoice_layout_id' => '1', 'selling_price_group_id' => null, 'print_receipt_on_invoice' => '1', 'receipt_printer_type' => 'browser', 'printer_id' => null, 'mobile' => null, 'alternate_number' => null, 'email' => null, 'website' => null, 'is_active' => '1', 'default_payment_accounts' => '{"cash":{"is_enabled":"1","account":null},"card":{"is_enabled":"1","account":null},"cheque":{"is_enabled":"1","account":null},"bank_transfer":{"is_enabled":"1","account":null},"other":{"is_enabled":"1","account":null},"custom_pay_1":{"is_enabled":"1","account":null},"custom_pay_2":{"is_enabled":"1","account":null},"custom_pay_3":{"is_enabled":"1","account":null}}', 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-04 02:15:20', 'updated_at' => '2019-12-11 04:53:39'],
            ['id' => '2', 'business_id' => '2', 'location_id' => null, 'name' => 'Awesome Pharmacy', 'landmark' => 'Linking Street', 'country' => 'USA', 'state' => 'Arizona', 'city' => 'Phoenix', 'zip_code' => '492001', 'invoice_scheme_id' => '2', 'invoice_layout_id' => '2', 'sale_invoice_layout_id' => '2', 'selling_price_group_id' => null, 'print_receipt_on_invoice' => '1', 'receipt_printer_type' => 'browser', 'printer_id' => null, 'mobile' => null, 'alternate_number' => null, 'email' => null, 'website' => null, 'is_active' => '1', 'default_payment_accounts' => '{"cash":{"is_enabled":"1"},"card":{"is_enabled":"1"},"cheque":{"is_enabled":"1"},"bank_transfer":{"is_enabled":"1"},"other":{"is_enabled":"1"},"custom_pay_1":{"is_enabled":"1"},"custom_pay_2":{"is_enabled":"1"},"custom_pay_3":{"is_enabled":"1"}}', 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:12:40', 'updated_at' => '2019-12-11 06:00:26'],
            ['id' => '3', 'business_id' => '3', 'location_id' => null, 'name' => 'Ultimate Electronics', 'landmark' => 'Linking Street', 'country' => 'USA', 'state' => 'Arizona', 'city' => 'Phoenix', 'zip_code' => '492001', 'invoice_scheme_id' => '3', 'invoice_layout_id' => '3', 'sale_invoice_layout_id' => '3', 'selling_price_group_id' => null, 'print_receipt_on_invoice' => '1', 'receipt_printer_type' => 'browser', 'printer_id' => null, 'mobile' => '', 'alternate_number' => '', 'email' => '', 'website' => null, 'is_active' => '1', 'default_payment_accounts' => '{"cash":{"is_enabled":"1"},"card":{"is_enabled":"1"},"cheque":{"is_enabled":"1"},"bank_transfer":{"is_enabled":"1"},"other":{"is_enabled":"1"},"custom_pay_1":{"is_enabled":"1"},"custom_pay_2":{"is_enabled":"1"},"custom_pay_3":{"is_enabled":"1"}}', 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 10:46:16', 'updated_at' => '2018-04-10 10:46:16'],
            ['id' => '4', 'business_id' => '4', 'location_id' => null, 'name' => 'Awesome Services', 'landmark' => 'Linking Street', 'country' => 'USA', 'state' => 'Arizona', 'city' => 'Phoenix', 'zip_code' => '282001', 'invoice_scheme_id' => '4', 'invoice_layout_id' => '4', 'sale_invoice_layout_id' => '4', 'selling_price_group_id' => null, 'print_receipt_on_invoice' => '1', 'receipt_printer_type' => 'browser', 'printer_id' => null, 'mobile' => '', 'alternate_number' => '', 'email' => '', 'website' => null, 'is_active' => '1', 'default_payment_accounts' => '{"cash":{"is_enabled":"1"},"card":{"is_enabled":"1"},"cheque":{"is_enabled":"1"},"bank_transfer":{"is_enabled":"1"},"other":{"is_enabled":"1"},"custom_pay_1":{"is_enabled":"1"},"custom_pay_2":{"is_enabled":"1"},"custom_pay_3":{"is_enabled":"1"}}', 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:20:43', 'updated_at' => '2018-04-10 12:20:43'],
            ['id' => '5', 'business_id' => '5', 'location_id' => null, 'name' => 'Awesome Restaurant', 'landmark' => 'Linking Street', 'country' => 'USA', 'state' => 'Arizona', 'city' => 'Phoenix', 'zip_code' => '85001', 'invoice_scheme_id' => '5', 'invoice_layout_id' => '5', 'sale_invoice_layout_id' => '5', 'selling_price_group_id' => null, 'print_receipt_on_invoice' => '1', 'receipt_printer_type' => 'browser', 'printer_id' => null, 'mobile' => '', 'alternate_number' => '', 'email' => '', 'website' => null, 'is_active' => '1', 'default_payment_accounts' => '{"cash":{"is_enabled":"1"},"card":{"is_enabled":"1"},"cheque":{"is_enabled":"1"},"bank_transfer":{"is_enabled":"1"},"other":{"is_enabled":"1"},"custom_pay_1":{"is_enabled":"1"},"custom_pay_2":{"is_enabled":"1"},"custom_pay_3":{"is_enabled":"1"}}', 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 11:12:11', 'updated_at' => '2018-07-13 11:12:11'],
            ['id' => '6', 'business_id' => '6', 'location_id' => null, 'name' => 'Awesome Manufacturers', 'landmark' => 'Infront of XYZ', 'country' => 'USA', 'state' => 'S', 'city' => 'C', 'zip_code' => 'Z', 'invoice_scheme_id' => '6', 'invoice_layout_id' => '6', 'sale_invoice_layout_id' => '6', 'selling_price_group_id' => null, 'print_receipt_on_invoice' => '1', 'receipt_printer_type' => 'browser', 'printer_id' => null, 'mobile' => null, 'alternate_number' => null, 'email' => null, 'website' => null, 'is_active' => '1', 'default_payment_accounts' => '{"cash":{"is_enabled":"1"},"card":{"is_enabled":"1"},"cheque":{"is_enabled":"1"},"bank_transfer":{"is_enabled":"1"},"other":{"is_enabled":"1"},"custom_pay_1":{"is_enabled":"1"},"custom_pay_2":{"is_enabled":"1"},"custom_pay_3":{"is_enabled":"1"}}', 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2019-08-18 13:11:28', 'updated_at' => '2019-12-11 17:36:49'],
        ];

        DB::table('business_locations')->insert($business_locations);

        // `pos`.`cash_register_transactions`
        $cash_register_transactions = [
            ['id' => '1', 'cash_register_id' => '1', 'amount' => '0.00', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'initial', 'transaction_id' => null, 'created_at' => '2018-07-13 07:39:34', 'updated_at' => '2018-07-13 07:39:34'],
            ['id' => '2', 'cash_register_id' => '1', 'amount' => '42.50', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '41', 'created_at' => '2018-07-13 07:44:40', 'updated_at' => '2018-07-13 07:44:40'],
            ['id' => '3', 'cash_register_id' => '1', 'amount' => '0.00', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '41', 'created_at' => '2018-07-13 07:44:40', 'updated_at' => '2018-07-13 07:44:40'],
            ['id' => '4', 'cash_register_id' => '1', 'amount' => '77.50', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '42', 'created_at' => '2018-07-13 07:45:29', 'updated_at' => '2018-07-13 07:45:29'],
            ['id' => '5', 'cash_register_id' => '1', 'amount' => '0.00', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '42', 'created_at' => '2018-07-13 07:45:29', 'updated_at' => '2018-07-13 07:45:29'],
            ['id' => '6', 'cash_register_id' => '1', 'amount' => '33.75', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '43', 'created_at' => '2018-07-13 07:46:04', 'updated_at' => '2018-07-13 07:46:04'],
            ['id' => '7', 'cash_register_id' => '1', 'amount' => '0.00', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '43', 'created_at' => '2018-07-13 07:46:04', 'updated_at' => '2018-07-13 07:46:04'],
            ['id' => '8', 'cash_register_id' => '1', 'amount' => '71.25', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '44', 'created_at' => '2018-07-13 07:46:36', 'updated_at' => '2018-07-13 07:46:36'],
            ['id' => '9', 'cash_register_id' => '1', 'amount' => '0.00', 'pay_method' => 'cash', 'type' => 'credit', 'transaction_type' => 'sell', 'transaction_id' => '44', 'created_at' => '2018-07-13 07:46:36', 'updated_at' => '2018-07-13 07:46:36'],
        ];
        DB::table('cash_register_transactions')->insert($cash_register_transactions);

        DB::insert("INSERT INTO users (id, surname, first_name, last_name, username, email, password, language, contact_no, address, remember_token, business_id, is_cmmsn_agnt, cmmsn_percent, deleted_at, created_at, updated_at) VALUES
                    (1, 'Mr', 'Admin', NULL, 'admin', '<EMAIL>', '$password', 'en', NULL, NULL, '6wUbpN3xEjDDyQwCfHiGqO7JkIQgjYoDFeQMxcp09YQXq1Ih1e5EqydddBMz', 1, 0, '0.00', NULL, '2018-01-04 02:15:19', '2018-01-04 02:15:19'),
(2, 'Mr', 'Demo', 'Cashier', 'cashier', '<EMAIL>', '$password', 'en', NULL, NULL, NULL, 1, 0, '0.00', NULL, '2018-01-04 02:20:58', '2018-01-04 02:20:58'),
(3, 'Mr.', 'Demo', 'Admin', 'demo-admin', '<EMAIL>', '$password', 'en', NULL, NULL, NULL, 1, 0, '0.00', NULL, '2018-01-06 07:10:57', '2018-01-06 07:10:57'),

(4, 'Mr', 'Demo', 'Admin', 'admin-pharmacy', '<EMAIL>', '$password', 'en', NULL, NULL, 'MJnleh4kefXZtw3FCI2yHKEwr45Rs5nZxyQyGSFRfjIcQn93sJZyw7BAzSUq', 2, 0, '0.00', NULL, '2018-04-10 08:12:40', '2018-04-10 08:12:40'),

(5, 'Mr', 'Demo', 'Admin', 'admin-electronics', '<EMAIL>', '$password', 'en', NULL, NULL, 'YzGFoRAfllUjyB12qIZhowlteUPhvR1cd77YREQuqemSUftHp72aRLZMrzwR', 3, 0, '0.00', NULL, '2018-04-10 10:46:15', '2018-04-10 10:46:15'),

(6, 'Mr', 'Demo', 'Admin', 'admin-services', '<EMAIL>', '$password', 'en', NULL, NULL, 'CdBNjmTIXVU0nEgcEg85L97w6JEjvuE6CfF7mvhmz7Ad2K7IxJVd9WPLdxcg', 4, 0, '0.00', NULL, '2018-04-10 12:20:42', '2018-04-10 12:20:42'),

(7, 'Mr', 'Demo', 'Admin', 'admin-restaurant', '<EMAIL>', '$password', 'en', NULL, NULL, 'ZzGDokwQe0vR0YpbBBWwbCWOGAkTgMS4wZlQOzAgWs2yAnovdGFkRwjofy1Y', 5, 0, '0.00', NULL, '2018-07-13 11:12:11', '2018-07-13 11:12:11'),
(8, 'Mr', 'Kevin', 'Nicols', 'kevin-nicols', '<EMAIL>', '$password', 'en', NULL, NULL, '57dRoj3knI8dJ9UPSqFo2EH0CVTco5WJHnDgrmB2CY4lvJR6ZTtmzHi7NdLa', 5, 0, '0.00', NULL, '2018-07-13 02:13:20', '2018-07-13 02:13:20'),

(9, 'Mr.', 'Super', 'Admin', 'superadmin', '<EMAIL>', '$password', 'en', NULL, NULL, NULL, 1, 0, '0.00', NULL, '2018-08-02 04:05:55', '2018-08-02 04:05:55'), 
(10, 'Mr.', 'WooCommerce', 'User', 'woocommerce_user', '<EMAIL>', '$password', 'en', NULL, NULL, NULL, 1, 0, '0.00', NULL, '2018-08-02 04:05:55', '2018-08-02 04:05:55'),
(11, 'Mr', 'Admin Essential', NULL, 'admin-essentials', '<EMAIL>', '$password', 'en', NULL, NULL, '6wUbpN3xEjDDyQwCfHiGqO7JkIQgjYoDFeQMxcp09YQXq1Ih1e5EqydddBMz', 1, 0, '0.00', NULL, '2018-01-04 02:15:19', '2018-01-04 02:15:19'),

(12, 'Mr.', 'mike', 'lee', 'manufacturer-demo', '<EMAIL>', '$password', 'en', NULL, NULL, NULL, 6, 0, '0.00', NULL, '2019-08-18 13:11:28', '2019-08-18 13:11:28');
");

        DB::insert("INSERT INTO brands (id, business_id, name, description, created_by, deleted_at, created_at, updated_at) VALUES
                    (1, 1, 'Levis', NULL, 1, NULL, '2018-01-03 21:19:47', '2018-01-03 21:19:47'),
(2, 1, 'Espirit', NULL, 1, NULL, '2018-01-03 21:19:58', '2018-01-03 21:19:58'),
(3, 1, 'U.S. Polo Assn.', NULL, 1, NULL, '2018-01-03 21:20:26', '2018-01-03 21:20:26'),
(4, 1, 'Nike', NULL, 1, NULL, '2018-01-03 21:20:34', '2018-01-03 21:20:34'),
(5, 1, 'Puma', NULL, 1, NULL, '2018-01-03 21:20:40', '2018-01-03 21:20:40'),
(6, 1, 'Adidas', NULL, 1, NULL, '2018-01-03 21:20:46', '2018-01-03 21:20:46'),
(7, 1, 'Samsung', NULL, 1, NULL, '2018-01-06 05:40:14', '2018-01-06 05:40:14'),
(8, 1, 'Apple', NULL, 1, NULL, '2018-01-06 05:40:23', '2018-01-06 05:40:23'),
(9, 1, 'Acer', NULL, 1, NULL, '2018-01-06 06:03:37', '2018-01-06 06:03:37'),
(10, 1, 'Bowflex', NULL, 1, NULL, '2018-01-06 06:15:31', '2018-01-06 06:15:31'),
(11, 1, 'Oreo', NULL, 1, NULL, '2018-01-06 06:35:00', '2018-01-06 06:35:00'),
(12, 1, 'Sharewood', NULL, 1, NULL, '2018-01-06 06:40:52', '2018-01-06 06:40:52'),
(13, 1, 'Barilla', NULL, 1, NULL, '2018-01-06 06:44:59', '2018-01-06 06:44:59'),
(14, 1, 'Lipton', NULL, 1, NULL, '2018-01-06 06:48:12', '2018-01-06 06:48:12'),
(15, 2, 'Acino', NULL, 4, NULL, '2018-04-10 08:14:44', '2018-04-10 08:14:44'),
(16, 2, 'Cipla', NULL, 4, NULL, '2018-04-10 08:15:04', '2018-04-10 08:15:04'),
(17, 2, 'BAYER', NULL, 4, NULL, '2018-04-10 08:15:32', '2018-04-10 08:15:32'),
(18, 3, 'Samsung', NULL, 5, NULL, '2018-04-10 10:57:28', '2018-04-10 10:57:28'),
(19, 3, 'LG', NULL, 5, NULL, '2018-04-10 10:57:35', '2018-04-10 10:57:35'),
(20, 3, 'Apple', NULL, 5, NULL, '2018-04-10 10:57:42', '2018-04-10 10:57:42'),
(21, 3, 'Sony', NULL, 5, NULL, '2018-04-10 10:57:49', '2018-04-10 10:57:49'),
(22, 3, 'Panasonic', NULL, 5, NULL, '2018-04-10 10:57:57', '2018-04-10 10:57:57'),
(23, 3, 'HP', NULL, 5, NULL, '2018-04-10 11:07:21', '2018-04-10 11:07:21'),
(24, 4, 'Samsung', NULL, 1, NULL, '2018-01-06 05:40:14', '2018-01-06 05:40:14'),
(25, 4, 'Apple', NULL, 1, NULL, '2018-01-06 05:40:23', '2018-01-06 05:40:23')");

        $categories = [
            ['id' => '1', 'name' => 'Men\'s', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:06:34', 'updated_at' => '2018-01-03 21:06:34'],
            ['id' => '2', 'name' => 'Women\'s', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:06:46', 'updated_at' => '2018-01-03 21:06:46'],
            ['id' => '3', 'name' => 'Accessories', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:07:03', 'updated_at' => '2018-01-03 21:07:03'],
            ['id' => '4', 'name' => 'Jeans', 'business_id' => '1', 'short_code' => null, 'parent_id' => '1', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:07:34', 'updated_at' => '2018-01-03 21:07:34'],
            ['id' => '5', 'name' => 'Shirts', 'business_id' => '1', 'short_code' => null, 'parent_id' => '1', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:08:18', 'updated_at' => '2018-01-03 21:08:18'],
            ['id' => '6', 'name' => 'Belts', 'business_id' => '1', 'short_code' => null, 'parent_id' => '3', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:08:41', 'updated_at' => '2018-01-03 21:08:41'],
            ['id' => '8', 'name' => 'Shoes', 'business_id' => '1', 'short_code' => null, 'parent_id' => '3', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:09:04', 'updated_at' => '2018-01-03 21:09:04'],
            ['id' => '10', 'name' => 'Sandal', 'business_id' => '1', 'short_code' => null, 'parent_id' => '3', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:09:23', 'updated_at' => '2018-01-03 21:09:23'],
            ['id' => '11', 'name' => 'Wallets', 'business_id' => '1', 'short_code' => null, 'parent_id' => '3', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 23:05:50', 'updated_at' => '2018-01-03 23:05:50'],
            ['id' => '12', 'name' => 'Electronics', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:24:34', 'updated_at' => '2018-01-06 05:24:34'],
            ['id' => '13', 'name' => 'Cell Phones', 'business_id' => '1', 'short_code' => null, 'parent_id' => '12', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:24:57', 'updated_at' => '2018-01-06 05:24:57'],
            ['id' => '14', 'name' => 'Computers', 'business_id' => '1', 'short_code' => null, 'parent_id' => '12', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:25:55', 'updated_at' => '2018-01-06 05:25:55'],
            ['id' => '15', 'name' => 'Sports', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:27:33', 'updated_at' => '2018-01-06 05:27:33'],
            ['id' => '16', 'name' => 'Athletic Clothing', 'business_id' => '1', 'short_code' => null, 'parent_id' => '15', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:28:40', 'updated_at' => '2018-01-06 05:28:40'],
            ['id' => '17', 'name' => 'Exercise & Fitness', 'business_id' => '1', 'short_code' => null, 'parent_id' => '15', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:29:19', 'updated_at' => '2018-01-06 05:29:19'],
            ['id' => '18', 'name' => 'Books', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:29:59', 'updated_at' => '2018-01-06 05:29:59'],
            ['id' => '19', 'name' => 'Autobiography', 'business_id' => '1', 'short_code' => null, 'parent_id' => '18', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:30:16', 'updated_at' => '2018-01-06 05:30:16'],
            ['id' => '20', 'name' => 'Children\'s books', 'business_id' => '1', 'short_code' => null, 'parent_id' => '18', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:30:58', 'updated_at' => '2018-01-06 05:30:58'],
            ['id' => '21', 'name' => 'Food & Grocery', 'business_id' => '1', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 05:31:35', 'updated_at' => '2018-01-06 05:31:35'],
            ['id' => '22', 'name' => 'Capsule', 'business_id' => '2', 'short_code' => null, 'parent_id' => '0', 'created_by' => '4', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:19:58', 'updated_at' => '2018-04-10 08:20:54'],
            ['id' => '23', 'name' => 'Bandages', 'business_id' => '2', 'short_code' => null, 'parent_id' => '0', 'created_by' => '4', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:20:48', 'updated_at' => '2018-04-10 08:20:48'],
            ['id' => '24', 'name' => 'Cream', 'business_id' => '2', 'short_code' => null, 'parent_id' => '0', 'created_by' => '4', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:21:23', 'updated_at' => '2018-04-10 08:21:23'],
            ['id' => '25', 'name' => 'Drops', 'business_id' => '2', 'short_code' => null, 'parent_id' => '0', 'created_by' => '4', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:21:31', 'updated_at' => '2018-04-10 08:21:31'],
            ['id' => '26', 'name' => 'Gel', 'business_id' => '2', 'short_code' => null, 'parent_id' => '0', 'created_by' => '4', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:21:57', 'updated_at' => '2018-04-10 08:21:57'],
            ['id' => '27', 'name' => 'Bottle', 'business_id' => '2', 'short_code' => null, 'parent_id' => '0', 'created_by' => '4', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:22:30', 'updated_at' => '2018-04-10 08:22:30'],
            ['id' => '28', 'name' => 'Mobile Phones', 'business_id' => '3', 'short_code' => null, 'parent_id' => '0', 'created_by' => '5', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 10:59:49', 'updated_at' => '2018-04-10 10:59:49'],
            ['id' => '29', 'name' => 'Tablets', 'business_id' => '3', 'short_code' => null, 'parent_id' => '0', 'created_by' => '5', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:00:02', 'updated_at' => '2018-04-10 11:00:02'],
            ['id' => '30', 'name' => 'Laptops', 'business_id' => '3', 'short_code' => null, 'parent_id' => '0', 'created_by' => '5', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:00:09', 'updated_at' => '2018-04-10 11:00:09'],
            ['id' => '31', 'name' => 'TVs', 'business_id' => '3', 'short_code' => null, 'parent_id' => '0', 'created_by' => '5', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:00:15', 'updated_at' => '2018-04-10 11:00:15'],
            ['id' => '32', 'name' => 'Cameras', 'business_id' => '3', 'short_code' => null, 'parent_id' => '0', 'created_by' => '5', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:00:29', 'updated_at' => '2018-04-10 11:00:29'],
            ['id' => '33', 'name' => 'Accessories', 'business_id' => '3', 'short_code' => null, 'parent_id' => '0', 'created_by' => '5', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:00:57', 'updated_at' => '2018-04-10 11:00:57'],
            ['id' => '34', 'name' => 'Car Services', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '6', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:25:33', 'updated_at' => '2018-04-10 12:30:10'],
            ['id' => '35', 'name' => 'Hair Styling', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '6', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:28:32', 'updated_at' => '2018-04-10 12:29:18'],
            ['id' => '36', 'name' => 'Plumbing', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '6', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:30:41', 'updated_at' => '2018-04-10 12:30:41'],
            ['id' => '37', 'name' => 'Bodycare', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '6', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:30:52', 'updated_at' => '2018-04-10 12:30:52'],
            ['id' => '38', 'name' => 'Spa', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '6', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:31:01', 'updated_at' => '2018-04-10 12:31:01'],
            ['id' => '39', 'name' => 'Starter', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 00:49:08', 'updated_at' => '2018-07-13 00:49:08'],
            ['id' => '40', 'name' => 'Main Menu', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 00:49:24', 'updated_at' => '2018-07-13 00:49:24'],
            ['id' => '41', 'name' => 'Salad', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 00:50:15', 'updated_at' => '2018-07-13 00:50:15'],
            ['id' => '42', 'name' => 'Pizza', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 00:50:29', 'updated_at' => '2018-07-13 00:50:29'],
            ['id' => '43', 'name' => 'Desserts', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 00:50:40', 'updated_at' => '2018-07-13 00:50:40'],
            ['id' => '44', 'name' => 'Drinks', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 00:50:54', 'updated_at' => '2018-07-13 00:50:54'],
            ['id' => '45', 'name' => 'Bakery and Snacks', 'business_id' => '5', 'short_code' => null, 'parent_id' => '0', 'created_by' => '7', 'category_type' => 'product', 'description' => null, 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 01:58:40', 'updated_at' => '2018-07-13 01:58:40'],
            ['id' => '46', 'name' => 'Laptop', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'device', 'description' => 'Laptop', 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2020-05-07 21:04:38', 'updated_at' => '2020-05-07 21:04:38'],
            ['id' => '47', 'name' => 'Desktop', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'device', 'description' => 'Desktop', 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2020-05-07 21:04:47', 'updated_at' => '2020-05-07 21:04:47'],
            ['id' => '48', 'name' => 'Mobile', 'business_id' => '4', 'short_code' => null, 'parent_id' => '0', 'created_by' => '1', 'category_type' => 'device', 'description' => 'Mobile', 'slug' => null, 'woocommerce_cat_id' => null, 'deleted_at' => null, 'created_at' => '2020-05-07 21:05:01', 'updated_at' => '2020-05-07 21:05:01'],
        ];
        DB::table('categories')->insert($categories);
        $contacts = [
            ['id' => '1', 'business_id' => '1', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Walk-In Customer', 'name' => 'Walk-In Customer', 'email' => null, 'contact_id' => 'CO0005', 'tax_number' => null, 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => 'Linking Street', 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '1', 'is_default' => '1', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 20:45:20', 'updated_at' => '2018-06-11 22:22:05'],
            ['id' => '2', 'business_id' => '1', 'type' => 'supplier', 'supplier_business_name' => 'Alpha Clothings', 'first_name' => 'Michael', 'name' => 'Michael', 'email' => null, 'contact_id' => 'CO0001', 'tax_number' => '4590091535', 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => 'Linking Street', 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '15', 'pay_term_type' => 'days', 'created_by' => '1', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 20:59:38', 'updated_at' => '2018-06-11 22:21:03'],
            ['id' => '3', 'business_id' => '1', 'type' => 'supplier', 'supplier_business_name' => 'Manhattan Clothing Ltd.', 'first_name' => 'Philip', 'name' => 'Philip', 'email' => null, 'contact_id' => 'CO0003', 'tax_number' => '***********', 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => 'Linking Street', 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '15', 'pay_term_type' => 'days', 'created_by' => '1', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:00:55', 'updated_at' => '2018-06-11 22:21:36'],
            ['id' => '4', 'business_id' => '1', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Harry', 'name' => 'Harry', 'email' => null, 'contact_id' => 'CO0006', 'tax_number' => null, 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => 'Linking Street', 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '1', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-03 21:01:40', 'updated_at' => '2018-06-11 22:22:16'],
            ['id' => '5', 'business_id' => '1', 'type' => 'supplier', 'supplier_business_name' => 'Digital Ocean', 'first_name' => 'Mike McCubbin', 'name' => 'Mike McCubbin', 'email' => null, 'contact_id' => 'CN0004', 'tax_number' => '***********', 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => 'Linking Street', 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '30', 'pay_term_type' => 'days', 'created_by' => '1', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 06:53:22', 'updated_at' => '2018-06-11 22:21:47'],
            ['id' => '6', 'business_id' => '1', 'type' => 'supplier', 'supplier_business_name' => 'Univer Suppliers', 'first_name' => 'Jackson Hill', 'name' => 'Jackson Hill', 'email' => null, 'contact_id' => 'CO0002', 'tax_number' => '5459000655', 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => 'Linking Street', 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '45', 'pay_term_type' => 'days', 'created_by' => '1', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-01-06 06:55:09', 'updated_at' => '2018-06-11 22:21:18'],
            ['id' => '7', 'business_id' => '2', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Walk-In Customer', 'name' => 'Walk-In Customer', 'email' => null, 'contact_id' => null, 'tax_number' => null, 'city' => null, 'state' => null, 'country' => null, 'address_line_1' => null, 'mobile' => '', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '4', 'is_default' => '1', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:12:40', 'updated_at' => '2018-04-10 08:12:40'],
            ['id' => '8', 'business_id' => '2', 'type' => 'supplier', 'supplier_business_name' => 'Jones Pharma', 'first_name' => 'Micheal Larson', 'name' => 'Micheal Larson', 'email' => null, 'contact_id' => '*********', 'tax_number' => null, 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => null, 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '30', 'pay_term_type' => 'days', 'created_by' => '4', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 10:02:52', 'updated_at' => '2018-04-10 10:02:52'],
            ['id' => '9', 'business_id' => '2', 'type' => 'supplier', 'supplier_business_name' => 'Axolotl Biologix', 'first_name' => 'Phillip Jenner', 'name' => 'Phillip Jenner', 'email' => null, 'contact_id' => '4585220005', 'tax_number' => null, 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => null, 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '30', 'pay_term_type' => 'days', 'created_by' => '4', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 10:04:20', 'updated_at' => '2018-04-10 10:04:20'],
            ['id' => '10', 'business_id' => '3', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Walk-In Customer', 'name' => 'Walk-In Customer', 'email' => null, 'contact_id' => null, 'tax_number' => null, 'city' => null, 'state' => null, 'country' => null, 'address_line_1' => null, 'mobile' => '', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '5', 'is_default' => '1', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 10:46:16', 'updated_at' => '2018-04-10 10:46:16'],
            ['id' => '11', 'business_id' => '3', 'type' => 'supplier', 'supplier_business_name' => 'Digital Ocean', 'first_name' => 'Micheal Krick', 'name' => 'Micheal Krick', 'email' => null, 'contact_id' => '12500079', 'tax_number' => null, 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => null, 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '30', 'pay_term_type' => 'days', 'created_by' => '5', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:36:21', 'updated_at' => '2018-04-10 11:36:21'],
            ['id' => '12', 'business_id' => '3', 'type' => 'supplier', 'supplier_business_name' => 'Neon Electronics', 'first_name' => 'Samuel Williams', 'name' => 'Samuel Williams', 'email' => null, 'contact_id' => '*********', 'tax_number' => null, 'city' => 'Phoenix', 'state' => 'Arizona', 'country' => 'USA', 'address_line_1' => null, 'mobile' => '(*************', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '30', 'pay_term_type' => 'days', 'created_by' => '5', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 11:38:33', 'updated_at' => '2018-04-10 11:38:33'],
            ['id' => '13', 'business_id' => '4', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Walk-In Customer', 'name' => 'Walk-In Customer', 'email' => null, 'contact_id' => null, 'tax_number' => null, 'city' => null, 'state' => null, 'country' => null, 'address_line_1' => null, 'mobile' => '', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '6', 'is_default' => '1', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 12:20:43', 'updated_at' => '2018-04-10 12:20:43'],
            ['id' => '14', 'business_id' => '5', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Walk-In Customer', 'name' => 'Walk-In Customer', 'email' => null, 'contact_id' => 'CO0001', 'tax_number' => null, 'city' => null, 'state' => null, 'country' => null, 'address_line_1' => null, 'mobile' => '', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '7', 'is_default' => '1', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 16:42:11', 'updated_at' => '2018-07-13 16:42:11'],
            ['id' => '15', 'business_id' => '5', 'type' => 'supplier', 'supplier_business_name' => 'Perfect Bakers', 'first_name' => 'Thomas Willson', 'name' => 'Thomas Willson', 'email' => '<EMAIL>', 'contact_id' => 'CO0002', 'tax_number' => '45781005', 'city' => null, 'state' => null, 'country' => null, 'address_line_1' => null, 'mobile' => '***********', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => '10', 'pay_term_type' => 'months', 'created_by' => '7', 'is_default' => '0', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-07-13 07:33:50', 'updated_at' => '2018-07-13 07:33:50'],
            ['id' => '16', 'business_id' => '6', 'type' => 'customer', 'supplier_business_name' => null, 'first_name' => 'Walk-In Customer', 'name' => 'Walk-In Customer', 'email' => null, 'contact_id' => null, 'tax_number' => null, 'city' => null, 'state' => null, 'country' => null, 'address_line_1' => null, 'mobile' => '', 'landline' => null, 'alternate_number' => null, 'pay_term_number' => null, 'pay_term_type' => null, 'created_by' => '12', 'is_default' => '1', 'customer_group_id' => null, 'custom_field1' => null, 'custom_field2' => null, 'custom_field3' => null, 'custom_field4' => null, 'deleted_at' => null, 'created_at' => '2018-04-10 08:12:40', 'updated_at' => '2018-04-10 08:12:40'],
        ];
        DB::table('contacts')->insert($contacts);

        $reference_counts = [
            ['id' => '1', 'ref_type' => 'purchase', 'ref_count' => '1', 'business_id' => '1', 'created_at' => '2018-06-11 22:17:07', 'updated_at' => '2018-06-11 22:17:07'],
            ['id' => '2', 'ref_type' => 'contacts', 'ref_count' => '6', 'business_id' => '1', 'created_at' => '2018-06-11 22:17:07', 'updated_at' => '2018-06-11 22:17:07'],
            ['id' => '3', 'ref_type' => 'contacts', 'ref_count' => '2', 'business_id' => '5', 'created_at' => '2018-07-13 16:42:11', 'updated_at' => '2018-07-13 07:33:50'],
            ['id' => '4', 'ref_type' => 'business_location', 'ref_count' => '1', 'business_id' => '5', 'created_at' => '2018-07-13 16:42:11', 'updated_at' => '2018-07-13 16:42:11'],
            ['id' => '5', 'ref_type' => 'purchase', 'ref_count' => '2', 'business_id' => '5', 'created_at' => '2018-07-13 07:34:45', 'updated_at' => '2018-07-13 07:38:24'],
            ['id' => '6', 'ref_type' => 'sell_payment', 'ref_count' => '4', 'business_id' => '5', 'created_at' => '2018-07-13 07:44:40', 'updated_at' => '2018-07-13 07:46:36'],
            ['id' => '7', 'ref_type' => 'contacts', 'ref_count' => '1', 'business_id' => '6', 'created_at' => '2019-08-18 18:41:28', 'updated_at' => '2019-08-18 18:41:28'],
            ['id' => '8', 'ref_type' => 'business_location', 'ref_count' => '1', 'business_id' => '6', 'created_at' => '2019-08-18 18:41:28', 'updated_at' => '2019-08-18 18:41:28'],
            ['id' => '9', 'ref_type' => 'production_purchase', 'ref_count' => '1', 'business_id' => '6', 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
        ];
        DB::table('reference_counts')->insert($reference_counts);

        $res_tables = [
            ['id' => '1', 'business_id' => '5', 'location_id' => '5', 'name' => 'Table 1', 'description' => null, 'created_by' => '7', 'deleted_at' => null, 'created_at' => '2018-07-13 07:40:57', 'updated_at' => '2018-07-13 07:40:57'],
            ['id' => '2', 'business_id' => '5', 'location_id' => '5', 'name' => 'Table 2', 'description' => null, 'created_by' => '7', 'deleted_at' => null, 'created_at' => '2018-07-13 07:41:04', 'updated_at' => '2018-07-13 07:41:04'],
            ['id' => '3', 'business_id' => '5', 'location_id' => '5', 'name' => 'Table 3', 'description' => null, 'created_by' => '7', 'deleted_at' => null, 'created_at' => '2018-07-13 07:41:10', 'updated_at' => '2018-07-13 07:41:10'],
            ['id' => '4', 'business_id' => '5', 'location_id' => '5', 'name' => 'Table 4', 'description' => null, 'created_by' => '7', 'deleted_at' => null, 'created_at' => '2018-07-13 07:41:17', 'updated_at' => '2018-07-13 07:41:17'],
        ];

        DB::table('res_tables')->insert($res_tables);

        DB::insert("INSERT INTO tax_rates (id, business_id, name, amount, is_tax_group, created_by, deleted_at, created_at, updated_at) VALUES
                    (1, 1, 'VAT@10%', 10.00, 0, 1, NULL, '2018-01-04 02:40:07', '2018-01-04 02:40:07'),
(2, 1, 'CGST@10%', 10.00, 0, 1, NULL, '2018-01-04 02:40:55', '2018-01-04 02:40:55'),
(3, 1, 'SGST@8%', 8.00, 0, 1, NULL, '2018-01-04 02:41:13', '2018-01-04 02:41:13'),
(4, 1, 'GST@18%', 18.00, 1, 1, NULL, '2018-01-04 02:42:19', '2018-01-04 02:42:19')");

        DB::insert('INSERT INTO group_sub_taxes (group_tax_id, tax_id) VALUES
                    (4, 2),
                    (4, 3)');

        DB::insert("INSERT INTO invoice_schemes (id, business_id, name, scheme_type, prefix, start_number, invoice_count, total_digits, is_default, created_at, updated_at) VALUES
                    (1, 1, 'Default', 'blank', 'AS', 1, 5, 4, 1, '2018-01-04 02:15:20', '2018-01-04 02:45:16'),
(2, 2, 'Default', 'blank', 'AP', 1, 3, 4, 1, '2018-04-10 08:12:40', '2018-04-10 10:26:12'),
(3, 3, 'Default', 'blank', 'AE', 1, 5, 4, 1, '2018-04-10 10:46:16', '2018-04-10 11:54:16'),
(4, 4, 'Default', 'blank', '', 1, 8, 4, 1, '2018-04-10 12:20:43', '2018-04-10 13:08:49'),
(5, 5, 'Default', 'blank', '', 1, 4, 4, 1, '2018-07-13 11:12:11', '2018-07-13 02:16:36'),
(6, 6, 'Default', 'blank', '', 1, 4, 4, 1, '2018-07-13 11:12:11', '2018-07-13 02:16:36')");

        $invoice_layouts = [
            ['id' => '1', 'name' => 'Default', 'header_text' => null, 'invoice_no_prefix' => 'Invoice No.', 'quotation_no_prefix' => null, 'invoice_heading' => 'Invoice', 'sub_heading_line1' => null, 'sub_heading_line2' => null, 'sub_heading_line3' => null, 'sub_heading_line4' => null, 'sub_heading_line5' => null, 'invoice_heading_not_paid' => '', 'invoice_heading_paid' => '', 'quotation_heading' => null, 'sub_total_label' => 'Subtotal', 'discount_label' => 'Discount', 'tax_label' => 'Tax', 'total_label' => 'Total', 'total_due_label' => 'Total Due', 'paid_label' => 'Total Paid', 'show_client_id' => '0', 'client_id_label' => null, 'client_tax_label' => null, 'date_label' => 'Date', 'show_time' => '1', 'show_brand' => '0', 'show_sku' => '1', 'show_cat_code' => '1', 'show_sale_description' => '0', 'table_product_label' => 'Product', 'table_qty_label' => 'Quantity', 'table_unit_price_label' => 'Unit Price', 'table_subtotal_label' => 'Subtotal', 'cat_code_label' => null, 'logo' => null, 'show_logo' => '0', 'show_business_name' => '0', 'show_location_name' => '1', 'show_landmark' => '1', 'show_city' => '1', 'show_state' => '1', 'show_zip_code' => '1', 'show_country' => '1', 'show_mobile_number' => '1', 'show_alternate_number' => '0', 'show_email' => '0', 'show_tax_1' => '1', 'show_tax_2' => '0', 'show_barcode' => '0', 'show_payments' => '1', 'show_customer' => '1', 'customer_label' => 'Customer', 'highlight_color' => '#000000', 'footer_text' => '', 'module_info' => null, 'is_default' => '1', 'business_id' => '1', 'design' => 'classic', 'cn_heading' => null, 'cn_no_label' => null, 'cn_amount_label' => null, 'created_at' => '2018-04-03 23:35:32', 'updated_at' => '2018-04-03 23:35:32'],
            ['id' => '2', 'name' => 'Default', 'header_text' => null, 'invoice_no_prefix' => 'Invoice No.', 'quotation_no_prefix' => null, 'invoice_heading' => 'Invoice', 'sub_heading_line1' => null, 'sub_heading_line2' => null, 'sub_heading_line3' => null, 'sub_heading_line4' => null, 'sub_heading_line5' => null, 'invoice_heading_not_paid' => '', 'invoice_heading_paid' => '', 'quotation_heading' => null, 'sub_total_label' => 'Subtotal', 'discount_label' => 'Discount', 'tax_label' => 'Tax', 'total_label' => 'Total', 'total_due_label' => 'Total Due', 'paid_label' => 'Total Paid', 'show_client_id' => '0', 'client_id_label' => null, 'client_tax_label' => null, 'date_label' => 'Date', 'show_time' => '1', 'show_brand' => '0', 'show_sku' => '1', 'show_cat_code' => '1', 'show_sale_description' => '0', 'table_product_label' => 'Product', 'table_qty_label' => 'Quantity', 'table_unit_price_label' => 'Unit Price', 'table_subtotal_label' => 'Subtotal', 'cat_code_label' => null, 'logo' => null, 'show_logo' => '0', 'show_business_name' => '0', 'show_location_name' => '1', 'show_landmark' => '1', 'show_city' => '1', 'show_state' => '1', 'show_zip_code' => '1', 'show_country' => '1', 'show_mobile_number' => '1', 'show_alternate_number' => '0', 'show_email' => '0', 'show_tax_1' => '1', 'show_tax_2' => '0', 'show_barcode' => '0', 'show_payments' => '1', 'show_customer' => '1', 'customer_label' => 'Customer', 'highlight_color' => '#000000', 'footer_text' => '', 'module_info' => null, 'is_default' => '1', 'business_id' => '2', 'design' => 'classic', 'cn_heading' => null, 'cn_no_label' => null, 'cn_amount_label' => null, 'created_at' => '2018-04-10 08:12:40', 'updated_at' => '2018-04-10 08:12:40'],
            ['id' => '3', 'name' => 'Default', 'header_text' => null, 'invoice_no_prefix' => 'Invoice No.', 'quotation_no_prefix' => null, 'invoice_heading' => 'Invoice', 'sub_heading_line1' => null, 'sub_heading_line2' => null, 'sub_heading_line3' => null, 'sub_heading_line4' => null, 'sub_heading_line5' => null, 'invoice_heading_not_paid' => '', 'invoice_heading_paid' => '', 'quotation_heading' => null, 'sub_total_label' => 'Subtotal', 'discount_label' => 'Discount', 'tax_label' => 'Tax', 'total_label' => 'Total', 'total_due_label' => 'Total Due', 'paid_label' => 'Total Paid', 'show_client_id' => '0', 'client_id_label' => null, 'client_tax_label' => null, 'date_label' => 'Date', 'show_time' => '1', 'show_brand' => '0', 'show_sku' => '1', 'show_cat_code' => '1', 'show_sale_description' => '0', 'table_product_label' => 'Product', 'table_qty_label' => 'Quantity', 'table_unit_price_label' => 'Unit Price', 'table_subtotal_label' => 'Subtotal', 'cat_code_label' => null, 'logo' => null, 'show_logo' => '0', 'show_business_name' => '0', 'show_location_name' => '1', 'show_landmark' => '1', 'show_city' => '1', 'show_state' => '1', 'show_zip_code' => '1', 'show_country' => '1', 'show_mobile_number' => '1', 'show_alternate_number' => '0', 'show_email' => '0', 'show_tax_1' => '1', 'show_tax_2' => '0', 'show_barcode' => '0', 'show_payments' => '1', 'show_customer' => '1', 'customer_label' => 'Customer', 'highlight_color' => '#000000', 'footer_text' => '', 'module_info' => null, 'is_default' => '1', 'business_id' => '3', 'design' => 'classic', 'cn_heading' => null, 'cn_no_label' => null, 'cn_amount_label' => null, 'created_at' => '2018-04-10 10:46:16', 'updated_at' => '2018-04-10 10:46:16'],
            ['id' => '4', 'name' => 'Default', 'header_text' => null, 'invoice_no_prefix' => 'Invoice No.', 'quotation_no_prefix' => null, 'invoice_heading' => 'Invoice', 'sub_heading_line1' => null, 'sub_heading_line2' => null, 'sub_heading_line3' => null, 'sub_heading_line4' => null, 'sub_heading_line5' => null, 'invoice_heading_not_paid' => '', 'invoice_heading_paid' => '', 'quotation_heading' => null, 'sub_total_label' => 'Subtotal', 'discount_label' => 'Discount', 'tax_label' => 'Tax', 'total_label' => 'Total', 'total_due_label' => 'Total Due', 'paid_label' => 'Total Paid', 'show_client_id' => '0', 'client_id_label' => null, 'client_tax_label' => null, 'date_label' => 'Date', 'show_time' => '1', 'show_brand' => '0', 'show_sku' => '1', 'show_cat_code' => '1', 'show_sale_description' => '0', 'table_product_label' => 'Product', 'table_qty_label' => 'Quantity', 'table_unit_price_label' => 'Unit Price', 'table_subtotal_label' => 'Subtotal', 'cat_code_label' => null, 'logo' => null, 'show_logo' => '0', 'show_business_name' => '0', 'show_location_name' => '1', 'show_landmark' => '1', 'show_city' => '1', 'show_state' => '1', 'show_zip_code' => '1', 'show_country' => '1', 'show_mobile_number' => '1', 'show_alternate_number' => '0', 'show_email' => '0', 'show_tax_1' => '1', 'show_tax_2' => '0', 'show_barcode' => '0', 'show_payments' => '1', 'show_customer' => '1', 'customer_label' => 'Customer', 'highlight_color' => '#000000', 'footer_text' => '', 'module_info' => null, 'is_default' => '1', 'business_id' => '4', 'design' => 'classic', 'cn_heading' => null, 'cn_no_label' => null, 'cn_amount_label' => null, 'created_at' => '2018-04-10 12:20:43', 'updated_at' => '2018-04-10 12:20:43'],
            ['id' => '5', 'name' => 'Default', 'header_text' => null, 'invoice_no_prefix' => 'Invoice No.', 'quotation_no_prefix' => null, 'invoice_heading' => 'Invoice', 'sub_heading_line1' => null, 'sub_heading_line2' => null, 'sub_heading_line3' => null, 'sub_heading_line4' => null, 'sub_heading_line5' => null, 'invoice_heading_not_paid' => '', 'invoice_heading_paid' => '', 'quotation_heading' => null, 'sub_total_label' => 'Subtotal', 'discount_label' => 'Discount', 'tax_label' => 'Tax', 'total_label' => 'Total', 'total_due_label' => 'Total Due', 'paid_label' => 'Total Paid', 'show_client_id' => '0', 'client_id_label' => null, 'client_tax_label' => null, 'date_label' => 'Date', 'show_time' => '1', 'show_brand' => '0', 'show_sku' => '1', 'show_cat_code' => '1', 'show_sale_description' => '0', 'table_product_label' => 'Product', 'table_qty_label' => 'Quantity', 'table_unit_price_label' => 'Unit Price', 'table_subtotal_label' => 'Subtotal', 'cat_code_label' => null, 'logo' => null, 'show_logo' => '0', 'show_business_name' => '0', 'show_location_name' => '1', 'show_landmark' => '1', 'show_city' => '1', 'show_state' => '1', 'show_zip_code' => '1', 'show_country' => '1', 'show_mobile_number' => '1', 'show_alternate_number' => '0', 'show_email' => '0', 'show_tax_1' => '1', 'show_tax_2' => '0', 'show_barcode' => '0', 'show_payments' => '1', 'show_customer' => '1', 'customer_label' => 'Customer', 'highlight_color' => '#000000', 'footer_text' => '', 'module_info' => null, 'is_default' => '1', 'business_id' => '5', 'design' => 'classic', 'cn_heading' => null, 'cn_no_label' => null, 'cn_amount_label' => null, 'created_at' => '2018-07-13 16:42:11', 'updated_at' => '2018-07-13 16:42:11'],
            ['id' => '6', 'name' => 'Default', 'header_text' => null, 'invoice_no_prefix' => 'Invoice No.', 'quotation_no_prefix' => null, 'invoice_heading' => 'Invoice', 'sub_heading_line1' => null, 'sub_heading_line2' => null, 'sub_heading_line3' => null, 'sub_heading_line4' => null, 'sub_heading_line5' => null, 'invoice_heading_not_paid' => '', 'invoice_heading_paid' => '', 'quotation_heading' => null, 'sub_total_label' => 'Subtotal', 'discount_label' => 'Discount', 'tax_label' => 'Tax', 'total_label' => 'Total', 'total_due_label' => 'Total Due', 'paid_label' => 'Total Paid', 'show_client_id' => '0', 'client_id_label' => null, 'client_tax_label' => null, 'date_label' => 'Date', 'show_time' => '1', 'show_brand' => '0', 'show_sku' => '1', 'show_cat_code' => '1', 'show_sale_description' => '0', 'table_product_label' => 'Product', 'table_qty_label' => 'Quantity', 'table_unit_price_label' => 'Unit Price', 'table_subtotal_label' => 'Subtotal', 'cat_code_label' => null, 'logo' => null, 'show_logo' => '0', 'show_business_name' => '0', 'show_location_name' => '1', 'show_landmark' => '1', 'show_city' => '1', 'show_state' => '1', 'show_zip_code' => '1', 'show_country' => '1', 'show_mobile_number' => '1', 'show_alternate_number' => '0', 'show_email' => '0', 'show_tax_1' => '1', 'show_tax_2' => '0', 'show_barcode' => '0', 'show_payments' => '1', 'show_customer' => '1', 'customer_label' => 'Customer', 'highlight_color' => '#000000', 'footer_text' => '', 'module_info' => null, 'is_default' => '1', 'business_id' => '6', 'design' => 'classic', 'cn_heading' => null, 'cn_no_label' => null, 'cn_amount_label' => null, 'created_at' => '2019-08-18 18:41:28', 'updated_at' => '2019-08-18 18:41:28'],
        ];
        DB::table('invoice_layouts')->insert($invoice_layouts);

        DB::insert("INSERT INTO units (id, business_id, actual_name, short_name, allow_decimal, base_unit_id, base_unit_multiplier, created_by, deleted_at, created_at, updated_at) VALUES
                    (1, 1, 'Pieces', 'Pc(s)', 0, NULL, NULL, 1, NULL, '2018-01-03 15:15:20', '2018-01-03 15:15:20'),
(2, 1, 'Packets', 'packets', 0, NULL, NULL, 1, NULL, '2018-01-06 01:07:01', '2018-01-06 01:08:36'),
(3, 1, 'Grams', 'g', 1, NULL, NULL, 1, NULL, '2018-01-06 01:10:34', '2018-01-06 01:10:34'),
(4, 2, 'Pieces', 'Pc(s)', 0, NULL, NULL, 4, NULL, '2018-04-10 02:42:40', '2018-04-10 02:42:40'),
(5, 3, 'Units', 'units', 0, NULL, NULL, 5, NULL, '2018-04-10 05:16:16', '2018-04-10 05:29:19'),
(6, 4, 'Units', 'units', 0, NULL, NULL, 6, NULL, '2018-04-10 06:50:43', '2018-04-10 07:02:04'),
(7, 5, 'Pieces', 'Pc(s)', 0, NULL, NULL, 7, NULL, '2018-07-13 05:42:11', '2018-07-13 05:42:11'),
(8, 5, 'Packets', 'packets', 0, NULL, NULL, 7, NULL, '2018-07-12 20:27:16', '2018-07-12 20:27:16'),
(9, 6, 'Pieces', 'Pc(s)', 0, NULL, NULL, 12, NULL, '2019-08-18 13:11:28', '2019-08-18 13:11:28'),
(10, 6, 'Grams', 'Gram', 1, NULL, NULL, 12, NULL, '2019-08-18 13:17:54', '2019-08-18 13:17:54'),
(11, 6, 'Kilogram', 'KG', 1, 10, '1000.0000', 12, NULL, '2019-08-18 13:18:17', '2019-08-18 13:18:17'),
(12, 6, '300 grams  Packets', '300 gm pkt', 0, 10, '300.0000', 12, NULL, '2019-08-18 13:19:30', '2019-08-18 13:19:30'),
(13, 6, 'Meters', 'Mtr', 1, NULL, NULL, 12, NULL, '2019-08-18 13:45:01', '2019-08-18 13:45:01')");

        $products = [
            ['id' => '1', 'name' => 'Men\'s Reverse Fleece Crew', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '1', 'category_id' => '1', 'sub_category_id' => '5', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '5.0000', 'sku' => 'AS0001', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728059_fleece_crew.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 21:29:08', 'updated_at' => '2018-06-11 07:40:59'],
            ['id' => '2', 'name' => 'Levis Men\'s Slimmy Fit Jeans', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '1', 'category_id' => '1', 'sub_category_id' => '4', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0002', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727964_levis_jeans.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2018-06-11 07:39:24'],
            ['id' => '3', 'name' => 'Men\'s Cozy Hoodie Sweater', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '2', 'category_id' => '1', 'sub_category_id' => '5', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0003', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728035_cozy_sweater.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 22:51:52', 'updated_at' => '2018-06-11 07:40:35'],
            ['id' => '4', 'name' => 'Puma Brown Sneaker', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '5', 'category_id' => '3', 'sub_category_id' => '8', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '5.0000', 'sku' => 'AS0004', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728147_puma_brown_sneaker.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 22:54:33', 'updated_at' => '2018-06-11 07:42:27'],
            ['id' => '8', 'name' => 'Nike Fashion Sneaker', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '4', 'category_id' => '3', 'sub_category_id' => '8', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0008', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728076_nike_sneaker.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 23:10:10', 'updated_at' => '2018-06-11 07:41:16'],
            ['id' => '9', 'name' => 'PUMA Men\'s Black Sneaker', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '5', 'category_id' => '3', 'sub_category_id' => '8', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0009', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728163_puma_brown_sneaker.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 23:11:57', 'updated_at' => '2018-06-11 07:42:43'],
            ['id' => '10', 'name' => 'NIKE Men\'s Running Shoe', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '4', 'category_id' => '3', 'sub_category_id' => '8', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0010', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728095_nike_running_shoe.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2018-06-11 07:41:35'],
            ['id' => '11', 'name' => 'U.S. Polo Men\'s Leather Belt', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '3', 'category_id' => '3', 'sub_category_id' => '6', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '15.0000', 'sku' => 'AS0011', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727768_belt.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 23:14:35', 'updated_at' => '2018-06-11 07:36:08'],
            ['id' => '12', 'name' => 'Unisex Brown Leather Wallet', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '1', 'category_id' => '3', 'sub_category_id' => '11', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0012', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727750_unisex_brown_wallet.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 23:15:50', 'updated_at' => '2018-06-11 07:35:50'],
            ['id' => '13', 'name' => 'Men Full sleeve T Shirt', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '2', 'category_id' => '1', 'sub_category_id' => '5', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '15.0000', 'sku' => 'AS0013', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728019_mens_tshirt.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-03 23:17:59', 'updated_at' => '2018-06-11 07:40:19'],
            ['id' => '14', 'name' => 'Samsung Galaxy S8', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '7', 'category_id' => '12', 'sub_category_id' => '13', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0014', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728193_s8.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 05:42:19', 'updated_at' => '2018-06-11 07:43:13'],
            ['id' => '15', 'name' => 'Apple iPhone 8', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '8', 'category_id' => '12', 'sub_category_id' => '13', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0015', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727817_iphone8.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2018-06-11 07:36:57'],
            ['id' => '16', 'name' => 'Samsung Galaxy J7 Pro', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '7', 'category_id' => '12', 'sub_category_id' => '13', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0016', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728178_j7_pro.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 05:54:48', 'updated_at' => '2018-06-11 07:42:58'],
            ['id' => '17', 'name' => 'Acer Aspire E 15', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '9', 'category_id' => '12', 'sub_category_id' => '14', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '70.0000', 'sku' => 'AS0017', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727793_acerE15.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:05:01', 'updated_at' => '2018-06-11 07:36:33'],
            ['id' => '18', 'name' => 'Apple MacBook Air', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '8', 'category_id' => '12', 'sub_category_id' => '14', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AS0018', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727849_macbookair.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:07:30', 'updated_at' => '2018-06-11 07:37:29'],
            ['id' => '19', 'name' => 'Cushion Crew Socks', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '4', 'category_id' => '15', 'sub_category_id' => '16', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0019', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727903_socks.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:10:28', 'updated_at' => '2018-06-11 07:38:23'],
            ['id' => '20', 'name' => 'Sports Tights Pants', 'business_id' => '1', 'type' => 'variable', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '6', 'category_id' => '15', 'sub_category_id' => '16', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '60.0000', 'sku' => 'AS0020', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728250_sports_pant.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:13:00', 'updated_at' => '2018-06-11 07:44:10'],
            ['id' => '21', 'name' => 'Pair Of Dumbbells', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => '10', 'category_id' => '15', 'sub_category_id' => '17', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '45.0000', 'sku' => 'AS0021', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728133_pair_of_dumbell.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:16:35', 'updated_at' => '2018-06-11 07:42:13'],
            ['id' => '22', 'name' => 'Diary of a Wimpy Kid', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '18', 'sub_category_id' => '20', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AS0022', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727917_diary_of_whimp_kid.jpeg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:25:09', 'updated_at' => '2018-06-11 07:38:37'],
            ['id' => '23', 'name' => 'Sneezy the Snowman', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '18', 'sub_category_id' => '20', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AS0023', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728231_sneezy.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:26:27', 'updated_at' => '2018-06-11 07:43:51'],
            ['id' => '24', 'name' => 'Etched in Sand Autobiography', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '18', 'sub_category_id' => '19', 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AS0024', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727933_etched_in_stone.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:31:22', 'updated_at' => '2018-06-11 07:38:53'],
            ['id' => '25', 'name' => 'Five Presidents', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '18', 'sub_category_id' => '19', 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AS0025', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727949_five_pesident.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:32:22', 'updated_at' => '2018-06-11 07:39:09'],
            ['id' => '26', 'name' => 'Oreo Cookies', 'business_id' => '1', 'type' => 'single', 'unit_id' => '2', 'sub_unit_ids' => null, 'brand_id' => '11', 'category_id' => '21', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '500.0000', 'sku' => 'AS0026', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728111_oreo.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:35:51', 'updated_at' => '2018-06-11 07:41:51'],
            ['id' => '27', 'name' => 'Butter Cookies', 'business_id' => '1', 'type' => 'single', 'unit_id' => '2', 'sub_unit_ids' => null, 'brand_id' => '12', 'category_id' => '21', 'sub_category_id' => null, 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0027', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727881_butter_cookies.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:43:16', 'updated_at' => '2018-06-11 07:38:01'],
            ['id' => '28', 'name' => 'Barilla Pasta', 'business_id' => '1', 'type' => 'single', 'unit_id' => '2', 'sub_unit_ids' => null, 'brand_id' => '13', 'category_id' => '21', 'sub_category_id' => null, 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => 'AS0028', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727865_barilla_pasta.jpeg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:45:47', 'updated_at' => '2018-06-11 07:37:45'],
            ['id' => '29', 'name' => 'Thin Spaghetti', 'business_id' => '1', 'type' => 'single', 'unit_id' => '2', 'sub_unit_ids' => null, 'brand_id' => '13', 'category_id' => '21', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0029', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528728274_thin_spegatti.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:46:53', 'updated_at' => '2018-06-11 07:44:34'],
            ['id' => '30', 'name' => 'Lipton Black Tea Bags', 'business_id' => '1', 'type' => 'single', 'unit_id' => '2', 'sub_unit_ids' => null, 'brand_id' => '14', 'category_id' => '21', 'sub_category_id' => null, 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => 'AS0030', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528727999_lipton_tea.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-01-06 06:48:59', 'updated_at' => '2018-06-11 07:39:59'],
            ['id' => '31', 'name' => 'Dolocare 1g paracetol', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '16', 'category_id' => '27', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => 'AP0031', 'barcode_type' => 'C128', 'expiry_period' => '24.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:06:29', 'updated_at' => '2018-04-10 10:06:29'],
            ['id' => '32', 'name' => 'Lexin 500mg capsule', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '16', 'category_id' => '22', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AP0032', 'barcode_type' => 'C128', 'expiry_period' => '24.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:07:52', 'updated_at' => '2018-04-10 10:07:52'],
            ['id' => '33', 'name' => 'Oflen-75', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '15', 'category_id' => '22', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AP0033', 'barcode_type' => 'C128', 'expiry_period' => '36.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:09:45', 'updated_at' => '2018-04-10 10:09:45'],
            ['id' => '34', 'name' => 'Cistiben Forte', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '16', 'category_id' => '22', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '25.0000', 'sku' => 'AP0034', 'barcode_type' => 'C128', 'expiry_period' => '12.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:10:59', 'updated_at' => '2018-04-10 10:10:59'],
            ['id' => '35', 'name' => 'Disflatyl drop 30ml', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '17', 'category_id' => '25', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AP0035', 'barcode_type' => 'C128', 'expiry_period' => '12.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:12:53', 'updated_at' => '2018-04-10 10:12:53'],
            ['id' => '36', 'name' => 'Mycoheal 40', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '17', 'category_id' => '26', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AP0036', 'barcode_type' => 'C128', 'expiry_period' => '6.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:14:18', 'updated_at' => '2018-04-10 10:14:18'],
            ['id' => '37', 'name' => 'Laboxyl', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '16', 'category_id' => '24', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AP0037', 'barcode_type' => 'C128', 'expiry_period' => '12.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:31:29', 'updated_at' => '2018-04-10 10:31:29'],
            ['id' => '38', 'name' => 'Fertilex plus men', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '15', 'category_id' => '22', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '30.0000', 'sku' => 'AP0038', 'barcode_type' => 'C128', 'expiry_period' => '12.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:32:35', 'updated_at' => '2018-04-10 10:32:35'],
            ['id' => '39', 'name' => 'vitamin E AIWA', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '15', 'category_id' => '22', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AP0039', 'barcode_type' => 'C128', 'expiry_period' => '12.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:33:26', 'updated_at' => '2018-04-10 10:33:26'],
            ['id' => '40', 'name' => 'Glycerol 4g', 'business_id' => '2', 'type' => 'single', 'unit_id' => '4', 'sub_unit_ids' => null, 'brand_id' => '16', 'category_id' => '22', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AP0040', 'barcode_type' => 'C128', 'expiry_period' => '12.00', 'expiry_period_type' => 'months', 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '4', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 10:34:32', 'updated_at' => '2018-04-10 10:34:49'],
            ['id' => '41', 'name' => 'HP 15-AY020TU', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '23', 'category_id' => '30', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'inclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AE0041', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:08:27', 'updated_at' => '2018-04-10 11:08:27'],
            ['id' => '42', 'name' => 'HP Pavilion 15-AU624TX', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '23', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AE0042', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:15:52', 'updated_at' => '2018-04-10 11:51:21'],
            ['id' => '43', 'name' => 'iPhone 6s plus', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '20', 'category_id' => '28', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AE0043', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:18:01', 'updated_at' => '2018-04-10 11:18:01'],
            ['id' => '44', 'name' => 'Samsung J7 Pro', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '18', 'category_id' => '28', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AE0044', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:19:10', 'updated_at' => '2018-04-10 11:19:10'],
            ['id' => '45', 'name' => 'Samsung Galaxy S7 Edge', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '18', 'category_id' => '28', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AE0045', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:20:21', 'updated_at' => '2018-04-10 11:20:21'],
            ['id' => '46', 'name' => 'LG G6 64 GB', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '19', 'category_id' => '28', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AE0046', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:22:04', 'updated_at' => '2018-04-10 11:22:04'],
            ['id' => '47', 'name' => 'Panasonic TH-58D300DX', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '22', 'category_id' => '31', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AE0047', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:23:45', 'updated_at' => '2018-04-10 11:23:45'],
            ['id' => '48', 'name' => 'Samsung 50MU6100', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '18', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '7.0000', 'sku' => 'AE0048', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:24:42', 'updated_at' => '2018-04-10 11:52:07'],
            ['id' => '49', 'name' => 'LG 55E7T', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '19', 'category_id' => '31', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '5.0000', 'sku' => 'AE0049', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:25:55', 'updated_at' => '2018-04-10 11:25:55'],
            ['id' => '50', 'name' => 'SONY ILCE-6000L/B', 'business_id' => '3', 'type' => 'single', 'unit_id' => '5', 'sub_unit_ids' => null, 'brand_id' => '21', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AE0050', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '5', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 11:34:32', 'updated_at' => '2018-04-10 11:34:32'],
            ['id' => '51', 'name' => 'Oil and filter change', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '34', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0051', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 12:34:57', 'updated_at' => '2018-04-10 12:34:57'],
            ['id' => '52', 'name' => 'Removal of wheels and brakes checked', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '34', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0052', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 12:35:55', 'updated_at' => '2018-04-10 12:35:55'],
            ['id' => '53', 'name' => 'Full body wash', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '34', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0053', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 12:39:15', 'updated_at' => '2018-04-10 12:39:15'],
            ['id' => '54', 'name' => 'Hair Cut', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '35', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0054', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 12:54:16', 'updated_at' => '2018-04-10 12:54:16'],
            ['id' => '55', 'name' => 'Wash And Blow Dry', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '35', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0055', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 12:56:18', 'updated_at' => '2018-04-10 12:56:18'],
            ['id' => '56', 'name' => 'Thai massage', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '38', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0056', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 13:01:59', 'updated_at' => '2018-04-10 13:01:59'],
            ['id' => '57', 'name' => 'Toilet Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '36', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0057', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 13:03:01', 'updated_at' => '2018-04-10 13:03:01'],
            ['id' => '58', 'name' => 'Sewer Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '36', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0058', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 13:03:37', 'updated_at' => '2018-04-10 13:03:37'],
            ['id' => '59', 'name' => 'Refrigerator Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0059', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 13:06:45', 'updated_at' => '2018-04-10 13:06:45'],
            ['id' => '60', 'name' => 'AC Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0060', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '6', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '61', 'name' => 'Red Wine', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '21', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0061', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528779460_wine.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-06-11 21:57:41', 'updated_at' => '2018-06-11 21:57:41'],
            ['id' => '62', 'name' => 'Pinot Noir Red Wine', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0062', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528779737_wine2.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-06-11 22:02:17', 'updated_at' => '2018-06-11 22:02:17'],
            ['id' => '63', 'name' => 'Banana', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => 'AS0063', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528780092_banana.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-06-11 22:08:12', 'updated_at' => '2018-06-11 22:08:12'],
            ['id' => '64', 'name' => 'Apple - Fuji', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '21', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => 'AS0064', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528780234_apples.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-06-11 22:10:34', 'updated_at' => '2018-06-11 22:10:34'],
            ['id' => '65', 'name' => 'Organic Egg', 'business_id' => '1', 'type' => 'single', 'unit_id' => '1', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '21', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0065', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1528780470_eggs.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-06-11 22:14:00', 'updated_at' => '2018-06-11 22:14:30'],
            ['id' => '66', 'name' => 'Honey Roast Chicken', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '39', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0066', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '300 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531481431_honey-roast-chicken.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 06:30:31', 'updated_at' => '2018-07-13 06:30:31'],
            ['id' => '67', 'name' => 'Mushroom and Herb Filled Tomatoes', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '39', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0067', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '150 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531481687_tomtoes-with-mushroom_med.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 06:34:47', 'updated_at' => '2018-07-13 06:34:47'],
            ['id' => '68', 'name' => 'Pasta Carbonara', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '40', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0068', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '180 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531481935_pasta.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 06:38:55', 'updated_at' => '2018-07-13 06:38:55'],
            ['id' => '69', 'name' => 'Chicken Doner Pita with Hummus', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '40', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0069', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '180 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531482350_pita.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 06:45:50', 'updated_at' => '2018-07-13 06:45:51'],
            ['id' => '70', 'name' => 'Falafal Doner Salad', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '41', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0070', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '200 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531483057_salad-doner.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 06:57:37', 'updated_at' => '2018-07-13 06:57:37'],
            ['id' => '71', 'name' => 'Lamb Doner Salad', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '41', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0071', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '220 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531483241_lamb-salad.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:00:41', 'updated_at' => '2018-07-13 07:00:41'],
            ['id' => '72', 'name' => 'Chicago-Style Pan Pizza', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '42', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0072', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '190 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531483502_pizza1.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:05:02', 'updated_at' => '2018-07-13 07:05:03'],
            ['id' => '73', 'name' => 'Pizza Margherita', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '42', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0073', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => '200 gm', 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531483649_margherita-pizza.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:07:29', 'updated_at' => '2018-07-13 07:07:29'],
            ['id' => '74', 'name' => 'Blueberry-Ginger Mojito', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '44', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0074', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531483843_mojito.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:10:43', 'updated_at' => '2018-07-13 07:10:44'],
            ['id' => '75', 'name' => 'Chocolate Brownie', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '43', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0075', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531483989_Chocolate-Brownies.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:13:09', 'updated_at' => '2018-07-13 07:13:09'],
            ['id' => '76', 'name' => 'Pepsi Soft Drink', 'business_id' => '5', 'type' => 'single', 'unit_id' => '7', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '44', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => 'AR0076', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531484313_pepsi.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:18:33', 'updated_at' => '2018-07-13 07:18:33'],
            ['id' => '77', 'name' => 'Brown Bread', 'business_id' => '5', 'type' => 'single', 'unit_id' => '8', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => '45', 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => 'AR0077', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => '1531484661_brown-bread.jpg', 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-13 07:24:21', 'updated_at' => '2018-07-13 07:28:56'],
            ['id' => '78', 'name' => 'Toppings', 'business_id' => '5', 'type' => 'modifier', 'unit_id' => null, 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'inclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0078', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-16 07:58:04', 'updated_at' => '2018-07-16 07:58:04'],
            ['id' => '79', 'name' => 'Extra', 'business_id' => '5', 'type' => 'modifier', 'unit_id' => null, 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'inclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AR0079', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '7', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2018-07-16 08:05:37', 'updated_at' => '2018-07-16 08:05:37'],
            ['id' => '81', 'name' => 'Pizza', 'business_id' => '6', 'type' => 'single', 'unit_id' => '9', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'Pizza', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:52:09', 'updated_at' => '2019-08-18 18:52:09'],
            ['id' => '82', 'name' => 'Pizza Base Bread', 'business_id' => '6', 'type' => 'single', 'unit_id' => '9', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => '0082', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:54:10', 'updated_at' => '2019-08-18 18:54:10'],
            ['id' => '83', 'name' => 'Tomatoes', 'business_id' => '6', 'type' => 'single', 'unit_id' => '10', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '1000.0000', 'sku' => '0083', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:54:59', 'updated_at' => '2019-08-18 18:54:59'],
            ['id' => '84', 'name' => 'Olive Oil', 'business_id' => '6', 'type' => 'single', 'unit_id' => '10', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '1000.0000', 'sku' => '0084', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:55:28', 'updated_at' => '2019-08-18 18:55:28'],
            ['id' => '85', 'name' => 'Salt', 'business_id' => '6', 'type' => 'single', 'unit_id' => '10', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '1000.0000', 'sku' => '0085', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:55:58', 'updated_at' => '2019-08-18 18:55:58'],
            ['id' => '86', 'name' => 'Garlic', 'business_id' => '6', 'type' => 'single', 'unit_id' => '9', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '50.0000', 'sku' => '0086', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:56:41', 'updated_at' => '2019-08-18 18:56:41'],
            ['id' => '87', 'name' => 'Chili Flakes', 'business_id' => '6', 'type' => 'single', 'unit_id' => '10', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => '0087', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:57:35', 'updated_at' => '2019-08-18 18:57:35'],
            ['id' => '88', 'name' => 'Cheese', 'business_id' => '6', 'type' => 'single', 'unit_id' => '10', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '1000.0000', 'sku' => '0088', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 18:58:43', 'updated_at' => '2019-08-18 18:58:43'],
            ['id' => '89', 'name' => 'Silk Fabric', 'business_id' => '6', 'type' => 'single', 'unit_id' => '13', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => '0089', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 19:15:23', 'updated_at' => '2019-08-18 19:21:40'],
            ['id' => '90', 'name' => 'Yarn', 'business_id' => '6', 'type' => 'single', 'unit_id' => '13', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '5.0000', 'sku' => '0090', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 19:16:50', 'updated_at' => '2019-08-18 19:16:50'],
            ['id' => '91', 'name' => 'Buttons', 'business_id' => '6', 'type' => 'single', 'unit_id' => '9', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => '0091', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 19:17:36', 'updated_at' => '2019-08-18 19:17:36'],
            ['id' => '92', 'name' => 'Zipper', 'business_id' => '6', 'type' => 'single', 'unit_id' => '9', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => '0092', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 19:18:26', 'updated_at' => '2019-08-18 19:18:26'],
            ['id' => '93', 'name' => 'Formal Shirt', 'business_id' => '6', 'type' => 'single', 'unit_id' => '9', 'sub_unit_ids' => null, 'brand_id' => null, 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '100.0000', 'sku' => '0093', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '12', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2019-08-18 19:19:55', 'updated_at' => '2019-08-18 19:20:39'],
            ['id' => '95', 'name' => 'Ram', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0095', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => null, 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-07 21:18:57', 'updated_at' => '2020-05-07 21:18:58'],
            ['id' => '99', 'name' => 'Display', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '7', 'category_id' => null, 'sub_category_id' => null, 'tax' => '1', 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '20.0000', 'sku' => 'AS0099', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '1', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:47:54', 'updated_at' => '2020-05-11 12:47:54'],
            ['id' => '100', 'name' => 'Mother Board', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '52.0000', 'sku' => 'AS0100', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '1', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:48:40', 'updated_at' => '2020-05-11 12:48:41'],
            ['id' => '101', 'name' => 'Battery Kit', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '32.0000', 'sku' => 'AS0101', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '1', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:49:21', 'updated_at' => '2020-05-11 12:49:21'],
            ['id' => '102', 'name' => 'Mother Board Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0102', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:54:22', 'updated_at' => '2020-05-11 12:54:23'],
            ['id' => '103', 'name' => 'Device Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0103', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '1', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:55:24', 'updated_at' => '2020-05-11 12:55:24'],
            ['id' => '104', 'name' => 'Battery Kit Replacement', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0104', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '1', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:56:45', 'updated_at' => '2020-05-11 12:56:45'],
            ['id' => '105', 'name' => 'Display', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '55.0000', 'sku' => 'AS0105', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:58:12', 'updated_at' => '2020-05-11 12:58:12'],
            ['id' => '106', 'name' => 'Mother Board', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '66.0000', 'sku' => 'AS0106', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:58:50', 'updated_at' => '2020-05-11 12:58:50'],
            ['id' => '107', 'name' => 'Back Glass', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '36.0000', 'sku' => 'AS0107', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '1', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 12:59:22', 'updated_at' => '2020-05-11 12:59:22'],
            ['id' => '108', 'name' => 'Mother board repair service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0108', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:00:23', 'updated_at' => '2020-05-11 13:00:23'],
            ['id' => '109', 'name' => 'Device repair service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0109', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:01:11', 'updated_at' => '2020-05-11 13:01:11'],
            ['id' => '110', 'name' => 'Battry repair service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0110', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '2', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:01:56', 'updated_at' => '2020-05-11 13:01:56'],
            ['id' => '111', 'name' => 'Mother Board', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '10.0000', 'sku' => 'AS0111', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '3', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:10:25', 'updated_at' => '2020-05-11 13:10:25'],
            ['id' => '112', 'name' => 'RAM', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '23.0000', 'sku' => 'AS0112', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '3', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:10:57', 'updated_at' => '2020-05-11 13:10:57'],
            ['id' => '113', 'name' => 'Display', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '24.0000', 'sku' => 'AS0113', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '3', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:12:17', 'updated_at' => '2020-05-11 13:12:17'],
            ['id' => '114', 'name' => 'Monitor Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0114', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '3', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:12:47', 'updated_at' => '2020-05-11 13:12:47'],
            ['id' => '115', 'name' => 'CPU Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0115', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '3', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:13:30', 'updated_at' => '2020-05-11 13:13:30'],
            ['id' => '116', 'name' => 'CPU', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '56.0000', 'sku' => 'AS0116', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '4', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:14:18', 'updated_at' => '2020-05-11 13:14:18'],
            ['id' => '117', 'name' => 'Hard Drive', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '13.0000', 'sku' => 'AS0117', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '3', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:15:05', 'updated_at' => '2020-05-11 13:15:05'],
            ['id' => '118', 'name' => 'Optical Drive', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '15.0000', 'sku' => 'AS0118', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '4', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:15:45', 'updated_at' => '2020-05-11 13:15:45'],
            ['id' => '119', 'name' => 'Video Card', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '21.0000', 'sku' => 'AS0119', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '4', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:17:11', 'updated_at' => '2020-05-11 13:17:11'],
            ['id' => '120', 'name' => 'CPU Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0120', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '4', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:17:54', 'updated_at' => '2020-05-11 13:17:54'],
            ['id' => '121', 'name' => 'Processor Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0121', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '4', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:18:38', 'updated_at' => '2020-05-11 13:18:38'],
            ['id' => '122', 'name' => 'Key Board', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '22.0000', 'sku' => 'AS0122', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '5', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:19:40', 'updated_at' => '2020-05-11 13:19:40'],
            ['id' => '123', 'name' => 'Mouse', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '56.0000', 'sku' => 'AS0123', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '5', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:20:15', 'updated_at' => '2020-05-11 13:20:15'],
            ['id' => '124', 'name' => 'Optical Drive', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '5.0000', 'sku' => 'AS0124', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '5', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:20:50', 'updated_at' => '2020-05-11 13:20:51'],
            ['id' => '125', 'name' => 'Key Board Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0125', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '5', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:21:42', 'updated_at' => '2020-05-11 13:21:42'],
            ['id' => '126', 'name' => 'Video Card Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0126', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '5', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:22:43', 'updated_at' => '2020-05-11 13:22:43'],
            ['id' => '127', 'name' => 'Tower(Case) Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0127', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '5', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:23:29', 'updated_at' => '2020-05-11 13:23:29'],
            ['id' => '128', 'name' => 'Tower (Case)', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '56.0000', 'sku' => 'AS0128', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:24:57', 'updated_at' => '2020-05-11 13:24:57'],
            ['id' => '129', 'name' => 'Hard Drive', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0129', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:25:41', 'updated_at' => '2020-05-11 13:25:41'],
            ['id' => '130', 'name' => 'Battery Kit', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0130', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:26:17', 'updated_at' => '2020-05-11 13:26:17'],
            ['id' => '131', 'name' => 'Mouse', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0131', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:27:51', 'updated_at' => '2020-05-11 13:27:51'],
            ['id' => '132', 'name' => 'Touchpad Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0132', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:30:08', 'updated_at' => '2020-05-11 13:30:09'],
            ['id' => '133', 'name' => 'Antenna Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0133', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:31:14', 'updated_at' => '2020-05-11 13:31:14'],
            ['id' => '134', 'name' => 'Cooling Fan Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '24', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0134', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '6', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:32:01', 'updated_at' => '2020-05-11 13:32:01'],
            ['id' => '135', 'name' => 'MacBook Air Cooling Heatsink', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '2.0000', 'sku' => 'AS0135', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:34:38', 'updated_at' => '2020-05-11 13:34:38'],
            ['id' => '136', 'name' => 'Loudspeaker internal speaker', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '1.0000', 'sku' => 'AS0136', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:35:40', 'updated_at' => '2020-05-11 13:35:40'],
            ['id' => '137', 'name' => 'Macbook IC CHIP X', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => '2.0000', 'sku' => 'AS0137', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:36:24', 'updated_at' => '2020-05-11 13:36:25'],
            ['id' => '138', 'name' => 'Mac  185W Power Supply', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0138', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:37:28', 'updated_at' => '2020-05-11 13:37:28'],
            ['id' => '139', 'name' => 'Processor Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0139', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:38:24', 'updated_at' => '2020-05-11 13:38:25'],
            ['id' => '140', 'name' => 'Screen Cable Connector Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0140', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:39:16', 'updated_at' => '2020-05-11 13:39:16'],
            ['id' => '141', 'name' => 'Macbook Pro ME664/665 USB board Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0141', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '7', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:40:54', 'updated_at' => '2020-05-11 13:40:55'],
            ['id' => '142', 'name' => 'iMac Retina 13 Left&Right Speakers', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0142', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '8', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:42:14', 'updated_at' => '2020-05-11 13:42:14'],
            ['id' => '143', 'name' => 'QWERTY Standard  Keyboard No backlight', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0143', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '8', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:43:25', 'updated_at' => '2020-05-11 13:43:25'],
            ['id' => '144', 'name' => 'WIFI bluetooth cable', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0144', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '8', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:44:10', 'updated_at' => '2020-05-11 13:44:10'],
            ['id' => '145', 'name' => 'Wifi Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0145', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '8', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:44:52', 'updated_at' => '2020-05-11 13:44:53'],
            ['id' => '146', 'name' => 'iMac Unibody Trackpad Touchpad Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0146', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '8', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:46:18', 'updated_at' => '2020-05-11 13:46:18'],
            ['id' => '147', 'name' => 'BIOS chip Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0147', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '8', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:47:32', 'updated_at' => '2020-05-11 13:47:32'],
            ['id' => '148', 'name' => 'iPhone X Screen', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0148', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:48:51', 'updated_at' => '2020-05-11 13:48:51'],
            ['id' => '149', 'name' => 'Lightning to USB Charging Cable', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0149', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:49:20', 'updated_at' => '2020-05-11 13:49:20'],
            ['id' => '150', 'name' => 'iHold EVO', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '1', 'alert_quantity' => null, 'sku' => 'AS0150', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:49:45', 'updated_at' => '2020-05-11 13:49:45'],
            ['id' => '151', 'name' => 'iPhone X Replacement Battery', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0151', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:50:12', 'updated_at' => '2020-05-11 13:50:12'],
            ['id' => '152', 'name' => 'Lightning Connector Assembly Repair', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0152', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:51:17', 'updated_at' => '2020-05-11 13:51:17'],
            ['id' => '153', 'name' => 'iHold EVO Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0153', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:52:03', 'updated_at' => '2020-05-11 13:52:03'],
            ['id' => '154', 'name' => 'Mother board Service', 'business_id' => '4', 'type' => 'single', 'unit_id' => '6', 'sub_unit_ids' => null, 'brand_id' => '25', 'category_id' => null, 'sub_category_id' => null, 'tax' => null, 'tax_type' => 'exclusive', 'enable_stock' => '0', 'alert_quantity' => '0.0000', 'sku' => 'AS0154', 'barcode_type' => 'C128', 'expiry_period' => null, 'expiry_period_type' => null, 'enable_sr_no' => '0', 'weight' => null, 'product_custom_field1' => null, 'product_custom_field2' => null, 'product_custom_field3' => null, 'product_custom_field4' => null, 'image' => null, 'woocommerce_media_id' => null, 'product_description' => null, 'created_by' => '1', 'warranty_id' => null, 'is_inactive' => '0', 'repair_model_id' => '9', 'not_for_selling' => '0', 'woocommerce_product_id' => null, 'woocommerce_disable_sync' => '0', 'created_at' => '2020-05-11 13:52:47', 'updated_at' => '2020-05-11 13:52:47'],
        ];

        DB::table('products')->insert($products);

        $product_variations = [
            ['id' => '1', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '1', 'is_dummy' => '1', 'created_at' => '2018-01-03 21:29:08', 'updated_at' => '2018-01-03 21:29:08'],
            ['id' => '2', 'variation_template_id' => '5', 'name' => 'Waist Size', 'product_id' => '2', 'is_dummy' => '0', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2020-05-08 09:24:13'],
            ['id' => '3', 'variation_template_id' => '6', 'name' => 'Size', 'product_id' => '3', 'is_dummy' => '0', 'created_at' => '2018-01-03 22:51:52', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '4', 'variation_template_id' => '6', 'name' => 'Size', 'product_id' => '4', 'is_dummy' => '0', 'created_at' => '2018-01-03 22:54:34', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '8', 'variation_template_id' => '6', 'name' => 'Size', 'product_id' => '8', 'is_dummy' => '0', 'created_at' => '2018-01-03 23:10:10', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '9', 'variation_template_id' => '6', 'name' => 'Size', 'product_id' => '9', 'is_dummy' => '0', 'created_at' => '2018-01-03 23:11:57', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '10', 'variation_template_id' => '6', 'name' => 'Size', 'product_id' => '10', 'is_dummy' => '0', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '11', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '11', 'is_dummy' => '1', 'created_at' => '2018-01-03 23:14:35', 'updated_at' => '2018-01-03 23:14:35'],
            ['id' => '12', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '12', 'is_dummy' => '1', 'created_at' => '2018-01-03 23:15:50', 'updated_at' => '2018-01-03 23:15:50'],
            ['id' => '13', 'variation_template_id' => '6', 'name' => 'Size', 'product_id' => '13', 'is_dummy' => '0', 'created_at' => '2018-01-03 23:17:59', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '14', 'variation_template_id' => '4', 'name' => 'Color', 'product_id' => '14', 'is_dummy' => '0', 'created_at' => '2018-01-06 05:42:19', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '15', 'variation_template_id' => '7', 'name' => 'Internal Memory', 'product_id' => '14', 'is_dummy' => '0', 'created_at' => '2018-01-06 05:44:14', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '16', 'variation_template_id' => '4', 'name' => 'Color', 'product_id' => '15', 'is_dummy' => '0', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '17', 'variation_template_id' => '7', 'name' => 'Internal Memory', 'product_id' => '15', 'is_dummy' => '0', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '18', 'variation_template_id' => '4', 'name' => 'Color', 'product_id' => '16', 'is_dummy' => '0', 'created_at' => '2018-01-06 05:54:48', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '19', 'variation_template_id' => '4', 'name' => 'Color', 'product_id' => '17', 'is_dummy' => '0', 'created_at' => '2018-01-06 06:05:01', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '20', 'variation_template_id' => '8', 'name' => 'Storage', 'product_id' => '18', 'is_dummy' => '0', 'created_at' => '2018-01-06 06:07:30', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '21', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '19', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:10:28', 'updated_at' => '2018-01-06 06:10:28'],
            ['id' => '22', 'variation_template_id' => '4', 'name' => 'Color', 'product_id' => '20', 'is_dummy' => '0', 'created_at' => '2018-01-06 06:13:00', 'updated_at' => '2020-05-08 09:24:14'],
            ['id' => '23', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '21', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:16:35', 'updated_at' => '2018-01-06 06:16:35'],
            ['id' => '24', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '22', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:25:09', 'updated_at' => '2018-01-06 06:25:09'],
            ['id' => '25', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '23', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:26:27', 'updated_at' => '2018-01-06 06:26:27'],
            ['id' => '26', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '24', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:31:22', 'updated_at' => '2018-01-06 06:31:22'],
            ['id' => '27', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '25', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:32:23', 'updated_at' => '2018-01-06 06:32:23'],
            ['id' => '28', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '26', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:35:51', 'updated_at' => '2018-01-06 06:35:51'],
            ['id' => '29', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '27', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:43:16', 'updated_at' => '2018-01-06 06:43:16'],
            ['id' => '30', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '28', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:45:47', 'updated_at' => '2018-01-06 06:45:47'],
            ['id' => '31', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '29', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:46:53', 'updated_at' => '2018-01-06 06:46:53'],
            ['id' => '32', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '30', 'is_dummy' => '1', 'created_at' => '2018-01-06 06:48:59', 'updated_at' => '2018-01-06 06:48:59'],
            ['id' => '33', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '31', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:06:29', 'updated_at' => '2018-04-10 10:06:29'],
            ['id' => '34', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '32', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:07:52', 'updated_at' => '2018-04-10 10:07:52'],
            ['id' => '35', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '33', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:09:45', 'updated_at' => '2018-04-10 10:09:45'],
            ['id' => '36', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '34', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:10:59', 'updated_at' => '2018-04-10 10:10:59'],
            ['id' => '37', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '35', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:12:53', 'updated_at' => '2018-04-10 10:12:53'],
            ['id' => '38', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '36', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:14:18', 'updated_at' => '2018-04-10 10:14:18'],
            ['id' => '39', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '37', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:31:29', 'updated_at' => '2018-04-10 10:31:29'],
            ['id' => '40', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '38', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:32:35', 'updated_at' => '2018-04-10 10:32:35'],
            ['id' => '41', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '39', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:33:26', 'updated_at' => '2018-04-10 10:33:26'],
            ['id' => '42', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '40', 'is_dummy' => '1', 'created_at' => '2018-04-10 10:34:32', 'updated_at' => '2018-04-10 10:34:32'],
            ['id' => '43', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '41', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:08:27', 'updated_at' => '2018-04-10 11:08:27'],
            ['id' => '44', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '42', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:15:52', 'updated_at' => '2018-04-10 11:15:52'],
            ['id' => '45', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '43', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:18:01', 'updated_at' => '2018-04-10 11:18:01'],
            ['id' => '46', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '44', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:19:10', 'updated_at' => '2018-04-10 11:19:10'],
            ['id' => '47', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '45', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:20:22', 'updated_at' => '2018-04-10 11:20:22'],
            ['id' => '48', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '46', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:22:04', 'updated_at' => '2018-04-10 11:22:04'],
            ['id' => '49', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '47', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:23:45', 'updated_at' => '2018-04-10 11:23:45'],
            ['id' => '50', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '48', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:24:42', 'updated_at' => '2018-04-10 11:24:42'],
            ['id' => '51', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '49', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:25:55', 'updated_at' => '2018-04-10 11:25:55'],
            ['id' => '52', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '50', 'is_dummy' => '1', 'created_at' => '2018-04-10 11:34:32', 'updated_at' => '2018-04-10 11:34:32'],
            ['id' => '53', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '51', 'is_dummy' => '1', 'created_at' => '2018-04-10 12:34:57', 'updated_at' => '2018-04-10 12:34:57'],
            ['id' => '54', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '52', 'is_dummy' => '1', 'created_at' => '2018-04-10 12:35:55', 'updated_at' => '2018-04-10 12:35:55'],
            ['id' => '55', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '53', 'is_dummy' => '1', 'created_at' => '2018-04-10 12:39:15', 'updated_at' => '2018-04-10 12:39:15'],
            ['id' => '56', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '54', 'is_dummy' => '1', 'created_at' => '2018-04-10 12:54:16', 'updated_at' => '2018-04-10 12:54:16'],
            ['id' => '57', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '55', 'is_dummy' => '1', 'created_at' => '2018-04-10 12:56:18', 'updated_at' => '2018-04-10 12:56:18'],
            ['id' => '58', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '56', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:01:59', 'updated_at' => '2018-04-10 13:01:59'],
            ['id' => '59', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '57', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:03:01', 'updated_at' => '2018-04-10 13:03:01'],
            ['id' => '60', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '58', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:03:37', 'updated_at' => '2018-04-10 13:03:37'],
            ['id' => '61', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '59', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:06:45', 'updated_at' => '2018-04-10 13:06:45'],
            ['id' => '62', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '60', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '63', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '61', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '64', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '62', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '65', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '63', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '66', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '64', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '67', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '65', 'is_dummy' => '1', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08'],
            ['id' => '68', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '66', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:00:31', 'updated_at' => '2018-07-13 01:00:31'],
            ['id' => '69', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '67', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:04:47', 'updated_at' => '2018-07-13 01:04:47'],
            ['id' => '70', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '68', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:08:55', 'updated_at' => '2018-07-13 01:08:55'],
            ['id' => '71', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '69', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:15:51', 'updated_at' => '2018-07-13 01:15:51'],
            ['id' => '72', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '70', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:27:37', 'updated_at' => '2018-07-13 01:27:37'],
            ['id' => '73', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '71', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:30:41', 'updated_at' => '2018-07-13 01:30:41'],
            ['id' => '74', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '72', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:35:03', 'updated_at' => '2018-07-13 01:35:03'],
            ['id' => '75', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '73', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:37:29', 'updated_at' => '2018-07-13 01:37:29'],
            ['id' => '76', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '74', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:40:44', 'updated_at' => '2018-07-13 01:40:44'],
            ['id' => '77', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '75', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:43:09', 'updated_at' => '2018-07-13 01:43:09'],
            ['id' => '78', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '76', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:48:33', 'updated_at' => '2018-07-13 01:48:33'],
            ['id' => '79', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '77', 'is_dummy' => '1', 'created_at' => '2018-07-13 01:54:22', 'updated_at' => '2018-07-13 01:54:22'],
            ['id' => '80', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '78', 'is_dummy' => '0', 'created_at' => '2018-07-16 07:58:04', 'updated_at' => '2018-07-16 07:58:04'],
            ['id' => '81', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '79', 'is_dummy' => '0', 'created_at' => '2018-07-16 08:05:37', 'updated_at' => '2018-07-16 08:05:37'],
            ['id' => '83', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '81', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:52:09', 'updated_at' => '2019-08-18 18:52:09'],
            ['id' => '84', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '82', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:54:10', 'updated_at' => '2019-08-18 18:54:10'],
            ['id' => '85', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '83', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:54:59', 'updated_at' => '2019-08-18 18:54:59'],
            ['id' => '86', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '84', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:55:28', 'updated_at' => '2019-08-18 18:55:28'],
            ['id' => '87', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '85', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:55:58', 'updated_at' => '2019-08-18 18:55:58'],
            ['id' => '88', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '86', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:56:41', 'updated_at' => '2019-08-18 18:56:41'],
            ['id' => '89', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '87', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:57:35', 'updated_at' => '2019-08-18 18:57:35'],
            ['id' => '90', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '88', 'is_dummy' => '1', 'created_at' => '2019-08-18 18:58:43', 'updated_at' => '2019-08-18 18:58:43'],
            ['id' => '91', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '89', 'is_dummy' => '1', 'created_at' => '2019-08-18 19:15:23', 'updated_at' => '2019-08-18 19:15:23'],
            ['id' => '92', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '90', 'is_dummy' => '1', 'created_at' => '2019-08-18 19:16:50', 'updated_at' => '2019-08-18 19:16:50'],
            ['id' => '93', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '91', 'is_dummy' => '1', 'created_at' => '2019-08-18 19:17:36', 'updated_at' => '2019-08-18 19:17:36'],
            ['id' => '94', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '92', 'is_dummy' => '1', 'created_at' => '2019-08-18 19:18:26', 'updated_at' => '2019-08-18 19:18:26'],
            ['id' => '95', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '93', 'is_dummy' => '1', 'created_at' => '2019-08-18 19:19:55', 'updated_at' => '2019-08-18 19:19:55'],
            ['id' => '96', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '94', 'is_dummy' => '1', 'created_at' => '2020-05-07 21:18:14', 'updated_at' => '2020-05-07 21:18:14'],
            ['id' => '97', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '95', 'is_dummy' => '1', 'created_at' => '2020-05-07 21:18:58', 'updated_at' => '2020-05-07 21:18:58'],
            ['id' => '98', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '96', 'is_dummy' => '1', 'created_at' => '2020-05-07 21:20:36', 'updated_at' => '2020-05-07 21:20:36'],
            ['id' => '99', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '97', 'is_dummy' => '1', 'created_at' => '2020-05-07 21:21:08', 'updated_at' => '2020-05-07 21:21:08'],
            ['id' => '100', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '98', 'is_dummy' => '1', 'created_at' => '2020-05-07 21:21:48', 'updated_at' => '2020-05-07 21:21:48'],
            ['id' => '101', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '99', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:47:54', 'updated_at' => '2020-05-11 12:47:54'],
            ['id' => '102', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '100', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:48:41', 'updated_at' => '2020-05-11 12:48:41'],
            ['id' => '103', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '101', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:49:21', 'updated_at' => '2020-05-11 12:49:21'],
            ['id' => '104', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '102', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:54:23', 'updated_at' => '2020-05-11 12:54:23'],
            ['id' => '105', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '103', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:55:24', 'updated_at' => '2020-05-11 12:55:24'],
            ['id' => '106', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '104', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:56:45', 'updated_at' => '2020-05-11 12:56:45'],
            ['id' => '107', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '105', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:58:12', 'updated_at' => '2020-05-11 12:58:12'],
            ['id' => '108', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '106', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:58:50', 'updated_at' => '2020-05-11 12:58:50'],
            ['id' => '109', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '107', 'is_dummy' => '1', 'created_at' => '2020-05-11 12:59:22', 'updated_at' => '2020-05-11 12:59:22'],
            ['id' => '110', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '108', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:00:23', 'updated_at' => '2020-05-11 13:00:23'],
            ['id' => '111', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '109', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:01:11', 'updated_at' => '2020-05-11 13:01:11'],
            ['id' => '112', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '110', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:01:56', 'updated_at' => '2020-05-11 13:01:56'],
            ['id' => '113', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '111', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:10:25', 'updated_at' => '2020-05-11 13:10:25'],
            ['id' => '114', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '112', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:10:57', 'updated_at' => '2020-05-11 13:10:57'],
            ['id' => '115', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '113', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:12:17', 'updated_at' => '2020-05-11 13:12:17'],
            ['id' => '116', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '114', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:12:47', 'updated_at' => '2020-05-11 13:12:47'],
            ['id' => '117', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '115', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:13:30', 'updated_at' => '2020-05-11 13:13:30'],
            ['id' => '118', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '116', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:14:18', 'updated_at' => '2020-05-11 13:14:18'],
            ['id' => '119', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '117', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:15:05', 'updated_at' => '2020-05-11 13:15:05'],
            ['id' => '120', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '118', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:15:45', 'updated_at' => '2020-05-11 13:15:45'],
            ['id' => '121', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '119', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:17:11', 'updated_at' => '2020-05-11 13:17:11'],
            ['id' => '122', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '120', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:17:54', 'updated_at' => '2020-05-11 13:17:54'],
            ['id' => '123', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '121', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:18:38', 'updated_at' => '2020-05-11 13:18:38'],
            ['id' => '124', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '122', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:19:40', 'updated_at' => '2020-05-11 13:19:40'],
            ['id' => '125', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '123', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:20:15', 'updated_at' => '2020-05-11 13:20:15'],
            ['id' => '126', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '124', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:20:50', 'updated_at' => '2020-05-11 13:20:50'],
            ['id' => '127', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '125', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:21:42', 'updated_at' => '2020-05-11 13:21:42'],
            ['id' => '128', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '126', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:22:43', 'updated_at' => '2020-05-11 13:22:43'],
            ['id' => '129', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '127', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:23:29', 'updated_at' => '2020-05-11 13:23:29'],
            ['id' => '130', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '128', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:24:57', 'updated_at' => '2020-05-11 13:24:57'],
            ['id' => '131', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '129', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:25:41', 'updated_at' => '2020-05-11 13:25:41'],
            ['id' => '132', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '130', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:26:17', 'updated_at' => '2020-05-11 13:26:17'],
            ['id' => '133', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '131', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:27:51', 'updated_at' => '2020-05-11 13:27:51'],
            ['id' => '134', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '132', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:30:09', 'updated_at' => '2020-05-11 13:30:09'],
            ['id' => '135', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '133', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:31:14', 'updated_at' => '2020-05-11 13:31:14'],
            ['id' => '136', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '134', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:32:01', 'updated_at' => '2020-05-11 13:32:01'],
            ['id' => '137', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '135', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:34:38', 'updated_at' => '2020-05-11 13:34:38'],
            ['id' => '138', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '136', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:35:40', 'updated_at' => '2020-05-11 13:35:40'],
            ['id' => '139', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '137', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:36:25', 'updated_at' => '2020-05-11 13:36:25'],
            ['id' => '140', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '138', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:37:28', 'updated_at' => '2020-05-11 13:37:28'],
            ['id' => '141', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '139', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:38:24', 'updated_at' => '2020-05-11 13:38:24'],
            ['id' => '142', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '140', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:39:16', 'updated_at' => '2020-05-11 13:39:16'],
            ['id' => '143', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '141', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:40:55', 'updated_at' => '2020-05-11 13:40:55'],
            ['id' => '144', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '142', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:42:14', 'updated_at' => '2020-05-11 13:42:14'],
            ['id' => '145', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '143', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:43:25', 'updated_at' => '2020-05-11 13:43:25'],
            ['id' => '146', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '144', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:44:10', 'updated_at' => '2020-05-11 13:44:10'],
            ['id' => '147', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '145', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:44:53', 'updated_at' => '2020-05-11 13:44:53'],
            ['id' => '148', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '146', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:46:18', 'updated_at' => '2020-05-11 13:46:18'],
            ['id' => '149', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '147', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:47:32', 'updated_at' => '2020-05-11 13:47:32'],
            ['id' => '150', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '148', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:48:51', 'updated_at' => '2020-05-11 13:48:51'],
            ['id' => '151', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '149', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:49:20', 'updated_at' => '2020-05-11 13:49:20'],
            ['id' => '152', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '150', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:49:45', 'updated_at' => '2020-05-11 13:49:45'],
            ['id' => '153', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '151', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:50:12', 'updated_at' => '2020-05-11 13:50:12'],
            ['id' => '154', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '152', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:51:17', 'updated_at' => '2020-05-11 13:51:17'],
            ['id' => '155', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '153', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:52:03', 'updated_at' => '2020-05-11 13:52:03'],
            ['id' => '156', 'variation_template_id' => null, 'name' => 'DUMMY', 'product_id' => '154', 'is_dummy' => '1', 'created_at' => '2020-05-11 13:52:47', 'updated_at' => '2020-05-11 13:52:47'],
        ];
        DB::table('product_variations')->insert($product_variations);

        $variations = [
            ['id' => '1', 'name' => 'DUMMY', 'product_id' => '1', 'sub_sku' => 'AS0001', 'product_variation_id' => '1', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '130.0000', 'dpp_inc_tax' => '143.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '130.0000', 'sell_price_inc_tax' => '143.0000', 'created_at' => '2018-01-03 21:29:08', 'updated_at' => '2018-06-11 07:40:59', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '2', 'name' => '28', 'product_id' => '2', 'sub_sku' => 'AS0002-1', 'product_variation_id' => '2', 'woocommerce_variation_id' => null, 'variation_value_id' => '21', 'default_purchase_price' => '70.0000', 'dpp_inc_tax' => '77.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '70.0000', 'sell_price_inc_tax' => '77.0000', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2020-05-08 09:24:13', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '3', 'name' => '30', 'product_id' => '2', 'sub_sku' => 'AS0002-2', 'product_variation_id' => '2', 'woocommerce_variation_id' => null, 'variation_value_id' => '22', 'default_purchase_price' => '70.0000', 'dpp_inc_tax' => '77.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '70.0000', 'sell_price_inc_tax' => '77.0000', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2020-05-08 09:24:13', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '4', 'name' => '32', 'product_id' => '2', 'sub_sku' => 'AS0002-3', 'product_variation_id' => '2', 'woocommerce_variation_id' => null, 'variation_value_id' => '23', 'default_purchase_price' => '70.0000', 'dpp_inc_tax' => '77.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '70.0000', 'sell_price_inc_tax' => '77.0000', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2020-05-08 09:24:13', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '5', 'name' => '34', 'product_id' => '2', 'sub_sku' => 'AS0002-4', 'product_variation_id' => '2', 'woocommerce_variation_id' => null, 'variation_value_id' => '24', 'default_purchase_price' => '72.0000', 'dpp_inc_tax' => '79.2000', 'profit_percent' => '0.0000', 'default_sell_price' => '72.0000', 'sell_price_inc_tax' => '79.2000', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2020-05-08 09:24:13', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '6', 'name' => '36', 'product_id' => '2', 'sub_sku' => 'AS0002-5', 'product_variation_id' => '2', 'woocommerce_variation_id' => null, 'variation_value_id' => '25', 'default_purchase_price' => '72.0000', 'dpp_inc_tax' => '79.2000', 'profit_percent' => '0.0000', 'default_sell_price' => '72.0000', 'sell_price_inc_tax' => '79.2000', 'created_at' => '2018-01-03 21:30:35', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '7', 'name' => 'S', 'product_id' => '3', 'sub_sku' => 'AS0003-1', 'product_variation_id' => '3', 'woocommerce_variation_id' => null, 'variation_value_id' => '26', 'default_purchase_price' => '190.0000', 'dpp_inc_tax' => '209.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '190.0000', 'sell_price_inc_tax' => '209.0000', 'created_at' => '2018-01-03 22:51:52', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '8', 'name' => 'M', 'product_id' => '3', 'sub_sku' => 'AS0003-2', 'product_variation_id' => '3', 'woocommerce_variation_id' => null, 'variation_value_id' => '27', 'default_purchase_price' => '190.0000', 'dpp_inc_tax' => '209.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '190.0000', 'sell_price_inc_tax' => '209.0000', 'created_at' => '2018-01-03 22:51:52', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '9', 'name' => 'L', 'product_id' => '3', 'sub_sku' => 'AS0003-3', 'product_variation_id' => '3', 'woocommerce_variation_id' => null, 'variation_value_id' => '28', 'default_purchase_price' => '190.0000', 'dpp_inc_tax' => '209.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '190.0000', 'sell_price_inc_tax' => '209.0000', 'created_at' => '2018-01-03 22:51:52', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '10', 'name' => 'XL', 'product_id' => '3', 'sub_sku' => 'AS0003-4', 'product_variation_id' => '3', 'woocommerce_variation_id' => null, 'variation_value_id' => '29', 'default_purchase_price' => '191.0000', 'dpp_inc_tax' => '210.1000', 'profit_percent' => '0.0000', 'default_sell_price' => '191.0000', 'sell_price_inc_tax' => '210.1000', 'created_at' => '2018-01-03 22:51:52', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '11', 'name' => '6', 'product_id' => '4', 'sub_sku' => 'AS0004-1', 'product_variation_id' => '4', 'woocommerce_variation_id' => null, 'variation_value_id' => '30', 'default_purchase_price' => '165.0000', 'dpp_inc_tax' => '181.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '165.0000', 'sell_price_inc_tax' => '181.5000', 'created_at' => '2018-01-03 22:54:34', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '12', 'name' => '7', 'product_id' => '4', 'sub_sku' => 'AS0004-2', 'product_variation_id' => '4', 'woocommerce_variation_id' => null, 'variation_value_id' => '31', 'default_purchase_price' => '165.0000', 'dpp_inc_tax' => '181.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '165.0000', 'sell_price_inc_tax' => '181.5000', 'created_at' => '2018-01-03 22:54:34', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '13', 'name' => '8', 'product_id' => '4', 'sub_sku' => 'AS0004-3', 'product_variation_id' => '4', 'woocommerce_variation_id' => null, 'variation_value_id' => '32', 'default_purchase_price' => '165.0000', 'dpp_inc_tax' => '181.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '165.0000', 'sell_price_inc_tax' => '181.5000', 'created_at' => '2018-01-03 22:54:34', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '14', 'name' => '9', 'product_id' => '4', 'sub_sku' => 'AS0004-4', 'product_variation_id' => '4', 'woocommerce_variation_id' => null, 'variation_value_id' => '33', 'default_purchase_price' => '166.0000', 'dpp_inc_tax' => '182.6000', 'profit_percent' => '0.0000', 'default_sell_price' => '166.0000', 'sell_price_inc_tax' => '182.6000', 'created_at' => '2018-01-03 22:54:34', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '27', 'name' => '6', 'product_id' => '8', 'sub_sku' => 'AS0008-1', 'product_variation_id' => '8', 'woocommerce_variation_id' => null, 'variation_value_id' => '30', 'default_purchase_price' => '110.0000', 'dpp_inc_tax' => '121.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '110.0000', 'sell_price_inc_tax' => '121.0000', 'created_at' => '2018-01-03 23:10:10', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '28', 'name' => '7', 'product_id' => '8', 'sub_sku' => 'AS0008-2', 'product_variation_id' => '8', 'woocommerce_variation_id' => null, 'variation_value_id' => '31', 'default_purchase_price' => '110.0000', 'dpp_inc_tax' => '121.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '110.0000', 'sell_price_inc_tax' => '121.0000', 'created_at' => '2018-01-03 23:10:10', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '29', 'name' => '8', 'product_id' => '8', 'sub_sku' => 'AS0008-3', 'product_variation_id' => '8', 'woocommerce_variation_id' => null, 'variation_value_id' => '32', 'default_purchase_price' => '110.0000', 'dpp_inc_tax' => '121.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '110.0000', 'sell_price_inc_tax' => '121.0000', 'created_at' => '2018-01-03 23:10:10', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '30', 'name' => '9', 'product_id' => '8', 'sub_sku' => 'AS0008-4', 'product_variation_id' => '8', 'woocommerce_variation_id' => null, 'variation_value_id' => '33', 'default_purchase_price' => '110.0000', 'dpp_inc_tax' => '121.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '110.0000', 'sell_price_inc_tax' => '121.0000', 'created_at' => '2018-01-03 23:10:10', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '31', 'name' => '6', 'product_id' => '9', 'sub_sku' => 'AS0009-1', 'product_variation_id' => '9', 'woocommerce_variation_id' => null, 'variation_value_id' => '30', 'default_purchase_price' => '135.0000', 'dpp_inc_tax' => '148.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '135.0000', 'sell_price_inc_tax' => '148.5000', 'created_at' => '2018-01-03 23:11:57', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '32', 'name' => '7', 'product_id' => '9', 'sub_sku' => 'AS0009-2', 'product_variation_id' => '9', 'woocommerce_variation_id' => null, 'variation_value_id' => '31', 'default_purchase_price' => '135.0000', 'dpp_inc_tax' => '148.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '135.0000', 'sell_price_inc_tax' => '148.5000', 'created_at' => '2018-01-03 23:11:57', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '33', 'name' => '8', 'product_id' => '9', 'sub_sku' => 'AS0009-3', 'product_variation_id' => '9', 'woocommerce_variation_id' => null, 'variation_value_id' => '32', 'default_purchase_price' => '135.0000', 'dpp_inc_tax' => '148.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '135.0000', 'sell_price_inc_tax' => '148.5000', 'created_at' => '2018-01-03 23:11:57', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '34', 'name' => '9', 'product_id' => '9', 'sub_sku' => 'AS0009-4', 'product_variation_id' => '9', 'woocommerce_variation_id' => null, 'variation_value_id' => '33', 'default_purchase_price' => '135.0000', 'dpp_inc_tax' => '148.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '135.0000', 'sell_price_inc_tax' => '148.5000', 'created_at' => '2018-01-03 23:11:57', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '35', 'name' => '5', 'product_id' => '10', 'sub_sku' => 'AS0010-1', 'product_variation_id' => '10', 'woocommerce_variation_id' => null, 'variation_value_id' => '34', 'default_purchase_price' => '150.0000', 'dpp_inc_tax' => '165.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '150.0000', 'sell_price_inc_tax' => '165.0000', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '36', 'name' => '6', 'product_id' => '10', 'sub_sku' => 'AS0010-2', 'product_variation_id' => '10', 'woocommerce_variation_id' => null, 'variation_value_id' => '30', 'default_purchase_price' => '150.0000', 'dpp_inc_tax' => '165.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '150.0000', 'sell_price_inc_tax' => '165.0000', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '37', 'name' => '7', 'product_id' => '10', 'sub_sku' => 'AS0010-3', 'product_variation_id' => '10', 'woocommerce_variation_id' => null, 'variation_value_id' => '31', 'default_purchase_price' => '150.0000', 'dpp_inc_tax' => '165.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '150.0000', 'sell_price_inc_tax' => '165.0000', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '38', 'name' => '8', 'product_id' => '10', 'sub_sku' => 'AS0010-4', 'product_variation_id' => '10', 'woocommerce_variation_id' => null, 'variation_value_id' => '32', 'default_purchase_price' => '150.0000', 'dpp_inc_tax' => '165.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '150.0000', 'sell_price_inc_tax' => '165.0000', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '39', 'name' => '9', 'product_id' => '10', 'sub_sku' => 'AS0010-5', 'product_variation_id' => '10', 'woocommerce_variation_id' => null, 'variation_value_id' => '33', 'default_purchase_price' => '150.0000', 'dpp_inc_tax' => '165.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '150.0000', 'sell_price_inc_tax' => '165.0000', 'created_at' => '2018-01-03 23:13:02', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '40', 'name' => 'DUMMY', 'product_id' => '11', 'sub_sku' => 'AS0011', 'product_variation_id' => '11', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '30.0000', 'dpp_inc_tax' => '33.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '30.0000', 'sell_price_inc_tax' => '33.0000', 'created_at' => '2018-01-03 23:14:35', 'updated_at' => '2018-06-11 07:36:08', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '41', 'name' => 'DUMMY', 'product_id' => '12', 'sub_sku' => 'AS0012', 'product_variation_id' => '12', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '25.0000', 'dpp_inc_tax' => '27.5000', 'profit_percent' => '0.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '27.5000', 'created_at' => '2018-01-03 23:15:50', 'updated_at' => '2018-06-11 07:35:50', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '42', 'name' => 'M', 'product_id' => '13', 'sub_sku' => 'AS0013-1', 'product_variation_id' => '13', 'woocommerce_variation_id' => null, 'variation_value_id' => '27', 'default_purchase_price' => '60.0000', 'dpp_inc_tax' => '66.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '60.0000', 'sell_price_inc_tax' => '66.0000', 'created_at' => '2018-01-03 23:17:59', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '43', 'name' => 'L', 'product_id' => '13', 'sub_sku' => 'AS0013-2', 'product_variation_id' => '13', 'woocommerce_variation_id' => null, 'variation_value_id' => '28', 'default_purchase_price' => '60.0000', 'dpp_inc_tax' => '66.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '60.0000', 'sell_price_inc_tax' => '66.0000', 'created_at' => '2018-01-03 23:17:59', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '44', 'name' => 'XL', 'product_id' => '13', 'sub_sku' => 'AS0013-3', 'product_variation_id' => '13', 'woocommerce_variation_id' => null, 'variation_value_id' => '29', 'default_purchase_price' => '60.0000', 'dpp_inc_tax' => '66.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '60.0000', 'sell_price_inc_tax' => '66.0000', 'created_at' => '2018-01-03 23:17:59', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '45', 'name' => 'Gray', 'product_id' => '14', 'sub_sku' => 'AS0014-1', 'product_variation_id' => '14', 'woocommerce_variation_id' => null, 'variation_value_id' => '35', 'default_purchase_price' => '700.0000', 'dpp_inc_tax' => '770.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '875.0000', 'sell_price_inc_tax' => '962.5000', 'created_at' => '2018-01-06 05:42:19', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '46', 'name' => 'Black', 'product_id' => '14', 'sub_sku' => 'AS0014-2', 'product_variation_id' => '14', 'woocommerce_variation_id' => null, 'variation_value_id' => '16', 'default_purchase_price' => '700.0000', 'dpp_inc_tax' => '770.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '875.0000', 'sell_price_inc_tax' => '962.5000', 'created_at' => '2018-01-06 05:42:19', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '47', 'name' => '64 GB', 'product_id' => '14', 'sub_sku' => 'AS0014-1', 'product_variation_id' => '15', 'woocommerce_variation_id' => null, 'variation_value_id' => '36', 'default_purchase_price' => '700.0000', 'dpp_inc_tax' => '770.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '875.0000', 'sell_price_inc_tax' => '962.5000', 'created_at' => '2018-01-06 05:44:14', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '48', 'name' => '128 GB', 'product_id' => '14', 'sub_sku' => 'AS0014-2', 'product_variation_id' => '15', 'woocommerce_variation_id' => null, 'variation_value_id' => '37', 'default_purchase_price' => '800.0000', 'dpp_inc_tax' => '880.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1000.0000', 'sell_price_inc_tax' => '1100.0000', 'created_at' => '2018-01-06 05:44:14', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '49', 'name' => 'White', 'product_id' => '15', 'sub_sku' => 'AS0015-1', 'product_variation_id' => '16', 'woocommerce_variation_id' => null, 'variation_value_id' => '38', 'default_purchase_price' => '950.0000', 'dpp_inc_tax' => '1045.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1187.5000', 'sell_price_inc_tax' => '1306.2500', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '50', 'name' => 'Gray', 'product_id' => '15', 'sub_sku' => 'AS0015-2', 'product_variation_id' => '16', 'woocommerce_variation_id' => null, 'variation_value_id' => '35', 'default_purchase_price' => '950.0000', 'dpp_inc_tax' => '1045.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1187.5000', 'sell_price_inc_tax' => '1306.2500', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '51', 'name' => 'Black', 'product_id' => '15', 'sub_sku' => 'AS0015-3', 'product_variation_id' => '16', 'woocommerce_variation_id' => null, 'variation_value_id' => '16', 'default_purchase_price' => '950.0000', 'dpp_inc_tax' => '1045.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1187.5000', 'sell_price_inc_tax' => '1306.2500', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '52', 'name' => '32 GB', 'product_id' => '15', 'sub_sku' => 'AS0015-1', 'product_variation_id' => '17', 'woocommerce_variation_id' => null, 'variation_value_id' => '39', 'default_purchase_price' => '950.0000', 'dpp_inc_tax' => '1045.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1187.5000', 'sell_price_inc_tax' => '1306.2500', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '53', 'name' => '64 GB', 'product_id' => '15', 'sub_sku' => 'AS0015-2', 'product_variation_id' => '17', 'woocommerce_variation_id' => null, 'variation_value_id' => '36', 'default_purchase_price' => '1010.0000', 'dpp_inc_tax' => '1111.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1262.5000', 'sell_price_inc_tax' => '1388.7500', 'created_at' => '2018-01-06 05:49:51', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '54', 'name' => 'Gold', 'product_id' => '16', 'sub_sku' => 'AS0016-1', 'product_variation_id' => '18', 'woocommerce_variation_id' => null, 'variation_value_id' => '20', 'default_purchase_price' => '350.0000', 'dpp_inc_tax' => '350.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '437.5000', 'sell_price_inc_tax' => '437.5000', 'created_at' => '2018-01-06 05:54:48', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '55', 'name' => 'White', 'product_id' => '16', 'sub_sku' => 'AS0016-2', 'product_variation_id' => '18', 'woocommerce_variation_id' => null, 'variation_value_id' => '38', 'default_purchase_price' => '350.0000', 'dpp_inc_tax' => '350.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '437.5000', 'sell_price_inc_tax' => '437.5000', 'created_at' => '2018-01-06 05:54:48', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '56', 'name' => 'Black', 'product_id' => '16', 'sub_sku' => 'AS0016-3', 'product_variation_id' => '18', 'woocommerce_variation_id' => null, 'variation_value_id' => '16', 'default_purchase_price' => '350.0000', 'dpp_inc_tax' => '350.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '437.5000', 'sell_price_inc_tax' => '437.5000', 'created_at' => '2018-01-06 05:54:48', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '57', 'name' => 'Black', 'product_id' => '17', 'sub_sku' => 'AS0017-1', 'product_variation_id' => '19', 'woocommerce_variation_id' => null, 'variation_value_id' => '16', 'default_purchase_price' => '350.0000', 'dpp_inc_tax' => '350.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '437.5000', 'sell_price_inc_tax' => '437.5000', 'created_at' => '2018-01-06 06:05:01', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '58', 'name' => 'White', 'product_id' => '17', 'sub_sku' => 'AS0017-2', 'product_variation_id' => '19', 'woocommerce_variation_id' => null, 'variation_value_id' => '38', 'default_purchase_price' => '350.0000', 'dpp_inc_tax' => '350.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '437.5000', 'sell_price_inc_tax' => '437.5000', 'created_at' => '2018-01-06 06:05:01', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '59', 'name' => '256 GB', 'product_id' => '18', 'sub_sku' => 'AS0018-1', 'product_variation_id' => '20', 'woocommerce_variation_id' => null, 'variation_value_id' => '40', 'default_purchase_price' => '1350.0000', 'dpp_inc_tax' => '1350.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1687.5000', 'sell_price_inc_tax' => '1687.5000', 'created_at' => '2018-01-06 06:07:30', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '60', 'name' => '500 GB', 'product_id' => '18', 'sub_sku' => 'AS0018-2', 'product_variation_id' => '20', 'woocommerce_variation_id' => null, 'variation_value_id' => '41', 'default_purchase_price' => '1450.0000', 'dpp_inc_tax' => '1450.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1812.5000', 'sell_price_inc_tax' => '1812.5000', 'created_at' => '2018-01-06 06:07:30', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '61', 'name' => 'DUMMY', 'product_id' => '19', 'sub_sku' => 'AS0019', 'product_variation_id' => '21', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2018-01-06 06:10:28', 'updated_at' => '2018-06-11 07:38:23', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '62', 'name' => 'Gray', 'product_id' => '20', 'sub_sku' => 'AS0020-1', 'product_variation_id' => '22', 'woocommerce_variation_id' => null, 'variation_value_id' => '35', 'default_purchase_price' => '25.0000', 'dpp_inc_tax' => '27.5000', 'profit_percent' => '25.0000', 'default_sell_price' => '31.2500', 'sell_price_inc_tax' => '34.3800', 'created_at' => '2018-01-06 06:13:00', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '63', 'name' => 'Black', 'product_id' => '20', 'sub_sku' => 'AS0020-2', 'product_variation_id' => '22', 'woocommerce_variation_id' => null, 'variation_value_id' => '16', 'default_purchase_price' => '25.0000', 'dpp_inc_tax' => '27.5000', 'profit_percent' => '25.0000', 'default_sell_price' => '31.2500', 'sell_price_inc_tax' => '34.3800', 'created_at' => '2018-01-06 06:13:00', 'updated_at' => '2020-05-08 09:24:14', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '64', 'name' => 'DUMMY', 'product_id' => '21', 'sub_sku' => 'AS0021', 'product_variation_id' => '23', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2018-01-06 06:16:35', 'updated_at' => '2018-06-11 07:42:13', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '65', 'name' => 'DUMMY', 'product_id' => '22', 'sub_sku' => 'AS0022', 'product_variation_id' => '24', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.8000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '11.0000', 'created_at' => '2018-01-06 06:25:09', 'updated_at' => '2018-06-11 07:38:37', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '66', 'name' => 'DUMMY', 'product_id' => '23', 'sub_sku' => 'AS0023', 'product_variation_id' => '25', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2018-01-06 06:26:27', 'updated_at' => '2018-06-11 07:43:51', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '67', 'name' => 'DUMMY', 'product_id' => '24', 'sub_sku' => 'AS0024', 'product_variation_id' => '26', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.8000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '11.0000', 'created_at' => '2018-01-06 06:31:22', 'updated_at' => '2018-06-11 07:38:53', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '68', 'name' => 'DUMMY', 'product_id' => '25', 'sub_sku' => 'AS0025', 'product_variation_id' => '27', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '15.0000', 'dpp_inc_tax' => '15.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '18.7500', 'sell_price_inc_tax' => '18.7500', 'created_at' => '2018-01-06 06:32:23', 'updated_at' => '2018-06-11 07:39:09', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '69', 'name' => 'DUMMY', 'product_id' => '26', 'sub_sku' => 'AS0026', 'product_variation_id' => '28', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5.0000', 'dpp_inc_tax' => '5.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '6.2500', 'sell_price_inc_tax' => '6.2500', 'created_at' => '2018-01-06 06:35:51', 'updated_at' => '2018-06-11 07:41:51', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '70', 'name' => 'DUMMY', 'product_id' => '27', 'sub_sku' => 'AS0027', 'product_variation_id' => '29', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '22.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '27.5000', 'created_at' => '2018-01-06 06:43:16', 'updated_at' => '2018-06-11 07:38:01', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '71', 'name' => 'DUMMY', 'product_id' => '28', 'sub_sku' => 'AS0028', 'product_variation_id' => '30', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '11.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '13.7500', 'created_at' => '2018-01-06 06:45:47', 'updated_at' => '2018-06-11 07:37:45', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '72', 'name' => 'DUMMY', 'product_id' => '29', 'sub_sku' => 'AS0029', 'product_variation_id' => '31', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-01-06 06:46:53', 'updated_at' => '2018-06-11 07:44:34', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '73', 'name' => 'DUMMY', 'product_id' => '30', 'sub_sku' => 'AS0030', 'product_variation_id' => '32', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '40.0000', 'dpp_inc_tax' => '44.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '50.0000', 'sell_price_inc_tax' => '55.0000', 'created_at' => '2018-01-06 06:48:59', 'updated_at' => '2018-06-11 07:39:59', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '74', 'name' => 'DUMMY', 'product_id' => '31', 'sub_sku' => 'AP0031', 'product_variation_id' => '33', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '7.0000', 'dpp_inc_tax' => '7.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '8.7500', 'sell_price_inc_tax' => '8.7500', 'created_at' => '2018-04-10 10:06:29', 'updated_at' => '2018-04-10 10:06:29', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '75', 'name' => 'DUMMY', 'product_id' => '32', 'sub_sku' => 'AP0032', 'product_variation_id' => '34', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-04-10 10:07:52', 'updated_at' => '2018-04-10 10:07:52', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '76', 'name' => 'DUMMY', 'product_id' => '33', 'sub_sku' => 'AP0033', 'product_variation_id' => '35', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-04-10 10:09:45', 'updated_at' => '2018-04-10 10:09:45', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '77', 'name' => 'DUMMY', 'product_id' => '34', 'sub_sku' => 'AP0034', 'product_variation_id' => '36', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '6.0000', 'dpp_inc_tax' => '6.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7.5000', 'sell_price_inc_tax' => '7.5000', 'created_at' => '2018-04-10 10:10:59', 'updated_at' => '2018-04-10 10:10:59', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '78', 'name' => 'DUMMY', 'product_id' => '35', 'sub_sku' => 'AP0035', 'product_variation_id' => '37', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '7.0000', 'dpp_inc_tax' => '7.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '8.7500', 'sell_price_inc_tax' => '8.7500', 'created_at' => '2018-04-10 10:12:53', 'updated_at' => '2018-04-10 10:12:53', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '79', 'name' => 'DUMMY', 'product_id' => '36', 'sub_sku' => 'AP0036', 'product_variation_id' => '38', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '9.0000', 'dpp_inc_tax' => '9.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11.2500', 'sell_price_inc_tax' => '11.2500', 'created_at' => '2018-04-10 10:14:18', 'updated_at' => '2018-04-10 10:14:18', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '80', 'name' => 'DUMMY', 'product_id' => '37', 'sub_sku' => 'AP0037', 'product_variation_id' => '39', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-04-10 10:31:29', 'updated_at' => '2018-04-10 10:31:29', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '81', 'name' => 'DUMMY', 'product_id' => '38', 'sub_sku' => 'AP0038', 'product_variation_id' => '40', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '15.0000', 'dpp_inc_tax' => '15.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '18.7500', 'sell_price_inc_tax' => '18.7500', 'created_at' => '2018-04-10 10:32:35', 'updated_at' => '2018-04-10 10:32:35', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '82', 'name' => 'DUMMY', 'product_id' => '39', 'sub_sku' => 'AP0039', 'product_variation_id' => '41', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-04-10 10:33:26', 'updated_at' => '2018-04-10 10:33:26', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '83', 'name' => 'DUMMY', 'product_id' => '40', 'sub_sku' => 'AP0040', 'product_variation_id' => '42', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2018-04-10 10:34:32', 'updated_at' => '2018-04-10 10:34:49', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '84', 'name' => 'DUMMY', 'product_id' => '41', 'sub_sku' => 'AE0041', 'product_variation_id' => '43', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '500.0000', 'dpp_inc_tax' => '500.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '625.0000', 'sell_price_inc_tax' => '625.0000', 'created_at' => '2018-04-10 11:08:27', 'updated_at' => '2018-04-10 11:08:27', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '85', 'name' => 'DUMMY', 'product_id' => '42', 'sub_sku' => 'AE0042', 'product_variation_id' => '44', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '520.0000', 'dpp_inc_tax' => '520.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '650.0000', 'sell_price_inc_tax' => '650.0000', 'created_at' => '2018-04-10 11:15:52', 'updated_at' => '2018-04-10 11:51:21', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '86', 'name' => 'DUMMY', 'product_id' => '43', 'sub_sku' => 'AE0043', 'product_variation_id' => '45', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '400.0000', 'dpp_inc_tax' => '400.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '500.0000', 'sell_price_inc_tax' => '500.0000', 'created_at' => '2018-04-10 11:18:01', 'updated_at' => '2018-04-10 11:18:01', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '87', 'name' => 'DUMMY', 'product_id' => '44', 'sub_sku' => 'AE0044', 'product_variation_id' => '46', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '195.0000', 'dpp_inc_tax' => '195.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '243.7500', 'sell_price_inc_tax' => '243.7500', 'created_at' => '2018-04-10 11:19:10', 'updated_at' => '2018-04-10 11:19:10', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '88', 'name' => 'DUMMY', 'product_id' => '45', 'sub_sku' => 'AE0045', 'product_variation_id' => '47', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '300.0000', 'dpp_inc_tax' => '300.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '375.0000', 'sell_price_inc_tax' => '375.0000', 'created_at' => '2018-04-10 11:20:22', 'updated_at' => '2018-04-10 11:20:22', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '89', 'name' => 'DUMMY', 'product_id' => '46', 'sub_sku' => 'AE0046', 'product_variation_id' => '48', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '321.0000', 'dpp_inc_tax' => '321.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '401.2500', 'sell_price_inc_tax' => '401.2500', 'created_at' => '2018-04-10 11:22:04', 'updated_at' => '2018-04-10 11:22:04', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '90', 'name' => 'DUMMY', 'product_id' => '47', 'sub_sku' => 'AE0047', 'product_variation_id' => '49', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '255.0000', 'dpp_inc_tax' => '255.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '318.7500', 'sell_price_inc_tax' => '318.7500', 'created_at' => '2018-04-10 11:23:45', 'updated_at' => '2018-04-10 11:23:45', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '91', 'name' => 'DUMMY', 'product_id' => '48', 'sub_sku' => 'AE0048', 'product_variation_id' => '50', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '850.0000', 'dpp_inc_tax' => '850.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1062.5000', 'sell_price_inc_tax' => '1062.5000', 'created_at' => '2018-04-10 11:24:42', 'updated_at' => '2018-04-10 11:52:07', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '92', 'name' => 'DUMMY', 'product_id' => '49', 'sub_sku' => 'AE0049', 'product_variation_id' => '51', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '1500.0000', 'dpp_inc_tax' => '1500.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1875.0000', 'sell_price_inc_tax' => '1875.0000', 'created_at' => '2018-04-10 11:25:55', 'updated_at' => '2018-04-10 11:25:55', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '93', 'name' => 'DUMMY', 'product_id' => '50', 'sub_sku' => 'AE0050', 'product_variation_id' => '52', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '630.0000', 'dpp_inc_tax' => '630.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '787.5000', 'sell_price_inc_tax' => '787.5000', 'created_at' => '2018-04-10 11:34:32', 'updated_at' => '2018-04-10 11:34:32', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '94', 'name' => 'DUMMY', 'product_id' => '51', 'sub_sku' => 'AS0051', 'product_variation_id' => '53', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '100.0000', 'dpp_inc_tax' => '100.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '125.0000', 'sell_price_inc_tax' => '125.0000', 'created_at' => '2018-04-10 12:34:57', 'updated_at' => '2018-04-10 12:34:57', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '95', 'name' => 'DUMMY', 'product_id' => '52', 'sub_sku' => 'AS0052', 'product_variation_id' => '54', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '80.0000', 'dpp_inc_tax' => '80.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '100.0000', 'sell_price_inc_tax' => '100.0000', 'created_at' => '2018-04-10 12:35:55', 'updated_at' => '2018-04-10 12:35:55', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '96', 'name' => 'DUMMY', 'product_id' => '53', 'sub_sku' => 'AS0053', 'product_variation_id' => '55', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '105.0000', 'dpp_inc_tax' => '105.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '131.2500', 'sell_price_inc_tax' => '131.2500', 'created_at' => '2018-04-10 12:39:15', 'updated_at' => '2018-04-10 12:39:15', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '97', 'name' => 'DUMMY', 'product_id' => '54', 'sub_sku' => 'AS0054', 'product_variation_id' => '56', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2018-04-10 12:54:16', 'updated_at' => '2018-04-10 12:54:16', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '98', 'name' => 'DUMMY', 'product_id' => '55', 'sub_sku' => 'AS0055', 'product_variation_id' => '57', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2018-04-10 12:56:18', 'updated_at' => '2018-04-10 12:56:18', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '99', 'name' => 'DUMMY', 'product_id' => '56', 'sub_sku' => 'AS0056', 'product_variation_id' => '58', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '150.0000', 'dpp_inc_tax' => '150.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '187.5000', 'sell_price_inc_tax' => '187.5000', 'created_at' => '2018-04-10 13:01:59', 'updated_at' => '2018-04-10 13:01:59', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '100', 'name' => 'DUMMY', 'product_id' => '57', 'sub_sku' => 'AS0057', 'product_variation_id' => '59', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2018-04-10 13:03:01', 'updated_at' => '2018-04-10 13:03:01', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '101', 'name' => 'DUMMY', 'product_id' => '58', 'sub_sku' => 'AS0058', 'product_variation_id' => '60', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2018-04-10 13:03:37', 'updated_at' => '2018-04-10 13:03:37', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '102', 'name' => 'DUMMY', 'product_id' => '59', 'sub_sku' => 'AS0059', 'product_variation_id' => '61', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '15.0000', 'dpp_inc_tax' => '15.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '18.7500', 'sell_price_inc_tax' => '18.7500', 'created_at' => '2018-04-10 13:06:45', 'updated_at' => '2018-04-10 13:06:45', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '103', 'name' => 'DUMMY', 'product_id' => '60', 'sub_sku' => 'AS0060', 'product_variation_id' => '62', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '15.0000', 'dpp_inc_tax' => '15.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '18.7500', 'sell_price_inc_tax' => '18.7500', 'created_at' => '2018-04-10 13:07:08', 'updated_at' => '2018-04-10 13:07:08', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '104', 'name' => 'DUMMY', 'product_id' => '61', 'sub_sku' => 'AS0061', 'product_variation_id' => '63', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '34.0000', 'dpp_inc_tax' => '34.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '42.5000', 'sell_price_inc_tax' => '42.5000', 'created_at' => '2018-06-11 21:57:41', 'updated_at' => '2018-06-11 21:57:41', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '105', 'name' => 'DUMMY', 'product_id' => '62', 'sub_sku' => 'AS0062', 'product_variation_id' => '64', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '35.0000', 'dpp_inc_tax' => '35.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '43.7500', 'sell_price_inc_tax' => '43.7500', 'created_at' => '2018-06-11 22:02:17', 'updated_at' => '2018-06-11 22:02:17', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '106', 'name' => 'DUMMY', 'product_id' => '63', 'sub_sku' => 'AS0063', 'product_variation_id' => '65', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2018-06-11 22:08:12', 'updated_at' => '2018-06-11 22:08:12', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '107', 'name' => 'DUMMY', 'product_id' => '64', 'sub_sku' => 'AS0064', 'product_variation_id' => '66', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2018-06-11 22:10:34', 'updated_at' => '2018-06-11 22:10:34', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '108', 'name' => 'DUMMY', 'product_id' => '65', 'sub_sku' => 'AS0065', 'product_variation_id' => '67', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2018-06-11 22:14:00', 'updated_at' => '2018-06-11 22:14:30', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '109', 'name' => 'DUMMY', 'product_id' => '66', 'sub_sku' => 'AR0066', 'product_variation_id' => '68', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '15.0000', 'dpp_inc_tax' => '15.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '18.7500', 'sell_price_inc_tax' => '18.7500', 'created_at' => '2018-07-13 06:30:31', 'updated_at' => '2018-07-13 06:30:31', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '110', 'name' => 'DUMMY', 'product_id' => '67', 'sub_sku' => 'AR0067', 'product_variation_id' => '69', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2018-07-13 06:34:47', 'updated_at' => '2018-07-13 06:34:47', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '111', 'name' => 'DUMMY', 'product_id' => '68', 'sub_sku' => 'AR0068', 'product_variation_id' => '70', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '11.0000', 'dpp_inc_tax' => '11.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '13.7500', 'sell_price_inc_tax' => '13.7500', 'created_at' => '2018-07-13 06:38:55', 'updated_at' => '2018-07-13 06:38:55', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '112', 'name' => 'DUMMY', 'product_id' => '69', 'sub_sku' => 'AR0069', 'product_variation_id' => '71', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-07-13 06:45:51', 'updated_at' => '2018-07-13 06:45:51', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '113', 'name' => 'DUMMY', 'product_id' => '70', 'sub_sku' => 'AR0070', 'product_variation_id' => '72', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '12.0000', 'dpp_inc_tax' => '12.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '15.0000', 'sell_price_inc_tax' => '15.0000', 'created_at' => '2018-07-13 06:57:37', 'updated_at' => '2018-07-13 06:57:37', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '114', 'name' => 'DUMMY', 'product_id' => '71', 'sub_sku' => 'AR0071', 'product_variation_id' => '73', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '17.0000', 'dpp_inc_tax' => '17.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '21.2500', 'sell_price_inc_tax' => '21.2500', 'created_at' => '2018-07-13 07:00:41', 'updated_at' => '2018-07-13 07:00:41', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '115', 'name' => 'DUMMY', 'product_id' => '72', 'sub_sku' => 'AR0072', 'product_variation_id' => '74', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '18.0000', 'dpp_inc_tax' => '18.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '22.5000', 'sell_price_inc_tax' => '22.5000', 'created_at' => '2018-07-13 07:05:03', 'updated_at' => '2018-07-13 07:05:03', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '116', 'name' => 'DUMMY', 'product_id' => '73', 'sub_sku' => 'AR0073', 'product_variation_id' => '75', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2018-07-13 07:07:29', 'updated_at' => '2018-07-13 07:07:29', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '117', 'name' => 'DUMMY', 'product_id' => '74', 'sub_sku' => 'AR0074', 'product_variation_id' => '76', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '7.0000', 'dpp_inc_tax' => '7.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '8.7500', 'sell_price_inc_tax' => '8.7500', 'created_at' => '2018-07-13 07:10:45', 'updated_at' => '2018-07-13 07:10:45', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '118', 'name' => 'DUMMY', 'product_id' => '75', 'sub_sku' => 'AR0075', 'product_variation_id' => '77', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2018-07-13 07:13:09', 'updated_at' => '2018-07-13 07:13:09', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '119', 'name' => 'DUMMY', 'product_id' => '76', 'sub_sku' => 'AR0076', 'product_variation_id' => '78', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2018-07-13 07:18:33', 'updated_at' => '2018-07-13 07:18:33', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '120', 'name' => 'DUMMY', 'product_id' => '77', 'sub_sku' => 'AR0077', 'product_variation_id' => '79', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '3.0000', 'dpp_inc_tax' => '3.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '3.7500', 'sell_price_inc_tax' => '3.7500', 'created_at' => '2018-07-13 07:24:22', 'updated_at' => '2018-07-13 07:28:56', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '121', 'name' => 'Bacon', 'product_id' => '78', 'sub_sku' => 'AR0078-1', 'product_variation_id' => '80', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '2.0000', 'sell_price_inc_tax' => '2.0000', 'created_at' => '2018-07-16 07:58:04', 'updated_at' => '2018-07-16 07:58:51', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '122', 'name' => 'Black olives', 'product_id' => '78', 'sub_sku' => 'AR0078-2', 'product_variation_id' => '80', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '1.0000', 'dpp_inc_tax' => '1.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '1.0000', 'sell_price_inc_tax' => '1.0000', 'created_at' => '2018-07-16 07:58:33', 'updated_at' => '2018-07-16 07:58:51', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '123', 'name' => 'Green peppers', 'product_id' => '78', 'sub_sku' => 'AR0078-3', 'product_variation_id' => '80', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '1.0000', 'dpp_inc_tax' => '1.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '1.0000', 'sell_price_inc_tax' => '1.0000', 'created_at' => '2018-07-16 07:58:51', 'updated_at' => '2018-07-16 07:58:51', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '124', 'name' => 'Cheese', 'product_id' => '79', 'sub_sku' => 'AR0079-1', 'product_variation_id' => '81', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '2.0000', 'sell_price_inc_tax' => '2.0000', 'created_at' => '2018-07-16 08:05:37', 'updated_at' => '2018-07-16 08:06:52', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '125', 'name' => 'Vegies', 'product_id' => '79', 'sub_sku' => 'AR0079-2', 'product_variation_id' => '81', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '1.0000', 'dpp_inc_tax' => '1.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '1.0000', 'sell_price_inc_tax' => '1.0000', 'created_at' => '2018-07-16 08:05:37', 'updated_at' => '2018-07-16 08:06:52', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '126', 'name' => 'Bacon', 'product_id' => '79', 'sub_sku' => 'AR0079-3', 'product_variation_id' => '81', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '2.0000', 'sell_price_inc_tax' => '2.0000', 'created_at' => '2018-07-16 08:05:37', 'updated_at' => '2018-07-16 08:06:52', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '127', 'name' => 'Chocolate', 'product_id' => '79', 'sub_sku' => 'AR0079-4', 'product_variation_id' => '81', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '2.0000', 'sell_price_inc_tax' => '2.0000', 'created_at' => '2018-07-16 08:06:52', 'updated_at' => '2018-07-16 08:06:52', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '129', 'name' => 'DUMMY', 'product_id' => '81', 'sub_sku' => 'Pizza', 'product_variation_id' => '83', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '100.0000', 'dpp_inc_tax' => '100.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '125.0000', 'sell_price_inc_tax' => '125.0000', 'created_at' => '2019-08-18 18:52:09', 'updated_at' => '2019-08-18 18:52:09', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '130', 'name' => 'DUMMY', 'product_id' => '82', 'sub_sku' => '0082', 'product_variation_id' => '84', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2019-08-18 18:54:10', 'updated_at' => '2019-08-18 18:54:10', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '131', 'name' => 'DUMMY', 'product_id' => '83', 'sub_sku' => '0083', 'product_variation_id' => '85', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2019-08-18 18:54:59', 'updated_at' => '2019-08-18 18:54:59', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '132', 'name' => 'DUMMY', 'product_id' => '84', 'sub_sku' => '0084', 'product_variation_id' => '86', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2019-08-18 18:55:28', 'updated_at' => '2019-08-18 18:55:28', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '133', 'name' => 'DUMMY', 'product_id' => '85', 'sub_sku' => '0085', 'product_variation_id' => '87', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '2.5000', 'sell_price_inc_tax' => '2.5000', 'created_at' => '2019-08-18 18:55:58', 'updated_at' => '2019-08-18 18:55:58', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '134', 'name' => 'DUMMY', 'product_id' => '86', 'sub_sku' => '0086', 'product_variation_id' => '88', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '2.5000', 'sell_price_inc_tax' => '2.5000', 'created_at' => '2019-08-18 18:56:41', 'updated_at' => '2019-08-18 18:56:41', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '135', 'name' => 'DUMMY', 'product_id' => '87', 'sub_sku' => '0087', 'product_variation_id' => '89', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2.0000', 'dpp_inc_tax' => '2.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '2.5000', 'sell_price_inc_tax' => '2.5000', 'created_at' => '2019-08-18 18:57:35', 'updated_at' => '2019-08-18 18:57:35', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '136', 'name' => 'DUMMY', 'product_id' => '88', 'sub_sku' => '0088', 'product_variation_id' => '90', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2019-08-18 18:58:43', 'updated_at' => '2019-08-18 18:58:43', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '137', 'name' => 'DUMMY', 'product_id' => '89', 'sub_sku' => '0089', 'product_variation_id' => '91', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10.0000', 'dpp_inc_tax' => '10.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12.5000', 'sell_price_inc_tax' => '12.5000', 'created_at' => '2019-08-18 19:15:24', 'updated_at' => '2019-08-18 19:21:40', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '138', 'name' => 'DUMMY', 'product_id' => '90', 'sub_sku' => '0090', 'product_variation_id' => '92', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5.0000', 'dpp_inc_tax' => '5.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '6.2500', 'sell_price_inc_tax' => '6.2500', 'created_at' => '2019-08-18 19:16:50', 'updated_at' => '2019-08-18 19:16:50', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '139', 'name' => 'DUMMY', 'product_id' => '91', 'sub_sku' => '0091', 'product_variation_id' => '93', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '0.5000', 'dpp_inc_tax' => '0.5000', 'profit_percent' => '25.0000', 'default_sell_price' => '0.6300', 'sell_price_inc_tax' => '0.6300', 'created_at' => '2019-08-18 19:17:36', 'updated_at' => '2019-08-18 19:17:36', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '140', 'name' => 'DUMMY', 'product_id' => '92', 'sub_sku' => '0092', 'product_variation_id' => '94', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '1.0000', 'dpp_inc_tax' => '1.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1.2500', 'sell_price_inc_tax' => '1.2500', 'created_at' => '2019-08-18 19:18:26', 'updated_at' => '2019-08-18 19:18:26', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '141', 'name' => 'DUMMY', 'product_id' => '93', 'sub_sku' => '0093', 'product_variation_id' => '95', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '20.0000', 'dpp_inc_tax' => '20.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '25.0000', 'sell_price_inc_tax' => '25.0000', 'created_at' => '2019-08-18 19:19:55', 'updated_at' => '2019-08-18 19:20:39', 'deleted_at' => null, 'combo_variations' => null],
            ['id' => '142', 'name' => 'DUMMY', 'product_id' => '94', 'sub_sku' => 'AS0094', 'product_variation_id' => '96', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '58.0000', 'dpp_inc_tax' => '58.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '72.5000', 'sell_price_inc_tax' => '72.5000', 'created_at' => '2020-05-07 21:18:14', 'updated_at' => '2020-05-08 10:07:43', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '143', 'name' => 'DUMMY', 'product_id' => '95', 'sub_sku' => 'AS0095', 'product_variation_id' => '97', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '10.0000', 'sell_price_inc_tax' => '10.0000', 'created_at' => '2020-05-07 21:18:58', 'updated_at' => '2020-05-07 21:18:58', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '144', 'name' => 'DUMMY', 'product_id' => '96', 'sub_sku' => 'AS0096', 'product_variation_id' => '98', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '8.0000', 'default_sell_price' => '8.6400', 'sell_price_inc_tax' => '8.6400', 'created_at' => '2020-05-07 21:20:36', 'updated_at' => '2020-05-08 10:08:13', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '145', 'name' => 'DUMMY', 'product_id' => '97', 'sub_sku' => 'AS0097', 'product_variation_id' => '99', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '8.0000', 'default_sell_price' => '8.6400', 'sell_price_inc_tax' => '8.6400', 'created_at' => '2020-05-07 21:21:08', 'updated_at' => '2020-05-08 10:08:21', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '146', 'name' => 'DUMMY', 'product_id' => '98', 'sub_sku' => 'AS0098', 'product_variation_id' => '100', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8.0000', 'dpp_inc_tax' => '8.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '8.0000', 'sell_price_inc_tax' => '8.0000', 'created_at' => '2020-05-07 21:21:48', 'updated_at' => '2020-05-08 10:08:29', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '147', 'name' => 'DUMMY', 'product_id' => '99', 'sub_sku' => 'AS0099', 'product_variation_id' => '101', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '3509.0000', 'dpp_inc_tax' => '3859.9000', 'profit_percent' => '25.0000', 'default_sell_price' => '4386.2500', 'sell_price_inc_tax' => '4824.8800', 'created_at' => '2020-05-11 12:47:54', 'updated_at' => '2020-05-11 12:47:54', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '148', 'name' => 'DUMMY', 'product_id' => '100', 'sub_sku' => 'AS0100', 'product_variation_id' => '102', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '4513.0000', 'dpp_inc_tax' => '4513.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '5641.2500', 'sell_price_inc_tax' => '5641.2500', 'created_at' => '2020-05-11 12:48:41', 'updated_at' => '2020-05-11 12:48:41', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '149', 'name' => 'DUMMY', 'product_id' => '101', 'sub_sku' => 'AS0101', 'product_variation_id' => '103', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '783.0000', 'dpp_inc_tax' => '783.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '978.7500', 'sell_price_inc_tax' => '978.7500', 'created_at' => '2020-05-11 12:49:21', 'updated_at' => '2020-05-11 12:49:21', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '150', 'name' => 'DUMMY', 'product_id' => '102', 'sub_sku' => 'AS0102', 'product_variation_id' => '104', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '525.0000', 'dpp_inc_tax' => '525.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '656.2500', 'sell_price_inc_tax' => '656.2500', 'created_at' => '2020-05-11 12:54:23', 'updated_at' => '2020-05-11 12:54:23', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '151', 'name' => 'DUMMY', 'product_id' => '103', 'sub_sku' => 'AS0103', 'product_variation_id' => '105', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '266.0000', 'dpp_inc_tax' => '266.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '332.5000', 'sell_price_inc_tax' => '332.5000', 'created_at' => '2020-05-11 12:55:24', 'updated_at' => '2020-05-11 12:55:24', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '152', 'name' => 'DUMMY', 'product_id' => '104', 'sub_sku' => 'AS0104', 'product_variation_id' => '106', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '555.0000', 'dpp_inc_tax' => '555.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '693.7500', 'sell_price_inc_tax' => '693.7500', 'created_at' => '2020-05-11 12:56:45', 'updated_at' => '2020-05-11 12:56:45', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '153', 'name' => 'DUMMY', 'product_id' => '105', 'sub_sku' => 'AS0105', 'product_variation_id' => '107', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '555.0000', 'dpp_inc_tax' => '555.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '693.7500', 'sell_price_inc_tax' => '693.7500', 'created_at' => '2020-05-11 12:58:12', 'updated_at' => '2020-05-11 12:58:12', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '154', 'name' => 'DUMMY', 'product_id' => '106', 'sub_sku' => 'AS0106', 'product_variation_id' => '108', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '555.0000', 'dpp_inc_tax' => '555.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '693.7500', 'sell_price_inc_tax' => '693.7500', 'created_at' => '2020-05-11 12:58:50', 'updated_at' => '2020-05-11 12:58:50', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '155', 'name' => 'DUMMY', 'product_id' => '107', 'sub_sku' => 'AS0107', 'product_variation_id' => '109', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '881.0000', 'dpp_inc_tax' => '881.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1101.2500', 'sell_price_inc_tax' => '1101.2500', 'created_at' => '2020-05-11 12:59:22', 'updated_at' => '2020-05-11 12:59:22', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '156', 'name' => 'DUMMY', 'product_id' => '108', 'sub_sku' => 'AS0108', 'product_variation_id' => '110', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '884.0000', 'dpp_inc_tax' => '884.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1105.0000', 'sell_price_inc_tax' => '1105.0000', 'created_at' => '2020-05-11 13:00:23', 'updated_at' => '2020-05-11 13:00:23', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '157', 'name' => 'DUMMY', 'product_id' => '109', 'sub_sku' => 'AS0109', 'product_variation_id' => '111', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '154.0000', 'dpp_inc_tax' => '154.0000', 'profit_percent' => '0.0000', 'default_sell_price' => '154.0000', 'sell_price_inc_tax' => '154.0000', 'created_at' => '2020-05-11 13:01:11', 'updated_at' => '2020-05-11 13:01:11', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '158', 'name' => 'DUMMY', 'product_id' => '110', 'sub_sku' => 'AS0110', 'product_variation_id' => '112', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '324.0000', 'dpp_inc_tax' => '324.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '405.0000', 'sell_price_inc_tax' => '405.0000', 'created_at' => '2020-05-11 13:01:56', 'updated_at' => '2020-05-11 13:01:56', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '159', 'name' => 'DUMMY', 'product_id' => '111', 'sub_sku' => 'AS0111', 'product_variation_id' => '113', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5666.0000', 'dpp_inc_tax' => '5666.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7082.5000', 'sell_price_inc_tax' => '7082.5000', 'created_at' => '2020-05-11 13:10:25', 'updated_at' => '2020-05-11 13:10:25', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '160', 'name' => 'DUMMY', 'product_id' => '112', 'sub_sku' => 'AS0112', 'product_variation_id' => '114', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8978.0000', 'dpp_inc_tax' => '8978.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11222.5000', 'sell_price_inc_tax' => '11222.5000', 'created_at' => '2020-05-11 13:10:57', 'updated_at' => '2020-05-11 13:10:57', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '161', 'name' => 'DUMMY', 'product_id' => '113', 'sub_sku' => 'AS0113', 'product_variation_id' => '115', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '97785.0000', 'dpp_inc_tax' => '97785.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '122231.2500', 'sell_price_inc_tax' => '122231.2500', 'created_at' => '2020-05-11 13:12:17', 'updated_at' => '2020-05-11 13:12:17', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '162', 'name' => 'DUMMY', 'product_id' => '114', 'sub_sku' => 'AS0114', 'product_variation_id' => '116', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '800.0000', 'dpp_inc_tax' => '800.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1000.0000', 'sell_price_inc_tax' => '1000.0000', 'created_at' => '2020-05-11 13:12:47', 'updated_at' => '2020-05-11 13:12:47', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '163', 'name' => 'DUMMY', 'product_id' => '115', 'sub_sku' => 'AS0115', 'product_variation_id' => '117', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '899.0000', 'dpp_inc_tax' => '899.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1123.7500', 'sell_price_inc_tax' => '1123.7500', 'created_at' => '2020-05-11 13:13:30', 'updated_at' => '2020-05-11 13:13:30', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '164', 'name' => 'DUMMY', 'product_id' => '116', 'sub_sku' => 'AS0116', 'product_variation_id' => '118', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '9789.0000', 'dpp_inc_tax' => '9789.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12236.2500', 'sell_price_inc_tax' => '12236.2500', 'created_at' => '2020-05-11 13:14:18', 'updated_at' => '2020-05-11 13:14:18', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '165', 'name' => 'DUMMY', 'product_id' => '117', 'sub_sku' => 'AS0117', 'product_variation_id' => '119', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '89789.0000', 'dpp_inc_tax' => '89789.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '112236.2500', 'sell_price_inc_tax' => '112236.2500', 'created_at' => '2020-05-11 13:15:05', 'updated_at' => '2020-05-11 13:15:05', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '166', 'name' => 'DUMMY', 'product_id' => '118', 'sub_sku' => 'AS0118', 'product_variation_id' => '120', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '798478.0000', 'dpp_inc_tax' => '798478.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '998097.5000', 'sell_price_inc_tax' => '998097.5000', 'created_at' => '2020-05-11 13:15:45', 'updated_at' => '2020-05-11 13:15:45', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '167', 'name' => 'DUMMY', 'product_id' => '119', 'sub_sku' => 'AS0119', 'product_variation_id' => '121', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8978.0000', 'dpp_inc_tax' => '8978.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11222.5000', 'sell_price_inc_tax' => '11222.5000', 'created_at' => '2020-05-11 13:17:11', 'updated_at' => '2020-05-11 13:17:11', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '168', 'name' => 'DUMMY', 'product_id' => '120', 'sub_sku' => 'AS0120', 'product_variation_id' => '122', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2000.0000', 'dpp_inc_tax' => '2000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '2500.0000', 'sell_price_inc_tax' => '2500.0000', 'created_at' => '2020-05-11 13:17:54', 'updated_at' => '2020-05-11 13:17:54', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '169', 'name' => 'DUMMY', 'product_id' => '121', 'sub_sku' => 'AS0121', 'product_variation_id' => '123', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '6000.0000', 'dpp_inc_tax' => '6000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7500.0000', 'sell_price_inc_tax' => '7500.0000', 'created_at' => '2020-05-11 13:18:38', 'updated_at' => '2020-05-11 13:18:38', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '170', 'name' => 'DUMMY', 'product_id' => '122', 'sub_sku' => 'AS0122', 'product_variation_id' => '124', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '3000.0000', 'dpp_inc_tax' => '3000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '3750.0000', 'sell_price_inc_tax' => '3750.0000', 'created_at' => '2020-05-11 13:19:40', 'updated_at' => '2020-05-11 13:19:40', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '171', 'name' => 'DUMMY', 'product_id' => '123', 'sub_sku' => 'AS0123', 'product_variation_id' => '125', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5645.0000', 'dpp_inc_tax' => '5645.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7056.2500', 'sell_price_inc_tax' => '7056.2500', 'created_at' => '2020-05-11 13:20:15', 'updated_at' => '2020-05-11 13:20:15', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '172', 'name' => 'DUMMY', 'product_id' => '124', 'sub_sku' => 'AS0124', 'product_variation_id' => '126', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '7978.0000', 'dpp_inc_tax' => '7978.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '9972.5000', 'sell_price_inc_tax' => '9972.5000', 'created_at' => '2020-05-11 13:20:50', 'updated_at' => '2020-05-11 13:20:50', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '173', 'name' => 'DUMMY', 'product_id' => '125', 'sub_sku' => 'AS0125', 'product_variation_id' => '127', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '566.0000', 'dpp_inc_tax' => '566.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '707.5000', 'sell_price_inc_tax' => '707.5000', 'created_at' => '2020-05-11 13:21:42', 'updated_at' => '2020-05-11 13:21:42', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '174', 'name' => 'DUMMY', 'product_id' => '126', 'sub_sku' => 'AS0126', 'product_variation_id' => '128', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '899.0000', 'dpp_inc_tax' => '899.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1123.7500', 'sell_price_inc_tax' => '1123.7500', 'created_at' => '2020-05-11 13:22:43', 'updated_at' => '2020-05-11 13:22:43', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '175', 'name' => 'DUMMY', 'product_id' => '127', 'sub_sku' => 'AS0127', 'product_variation_id' => '129', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '900.0000', 'dpp_inc_tax' => '900.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1125.0000', 'sell_price_inc_tax' => '1125.0000', 'created_at' => '2020-05-11 13:23:29', 'updated_at' => '2020-05-11 13:23:29', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '176', 'name' => 'DUMMY', 'product_id' => '128', 'sub_sku' => 'AS0128', 'product_variation_id' => '130', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '897.0000', 'dpp_inc_tax' => '897.0000', 'profit_percent' => '9.0000', 'default_sell_price' => '977.7300', 'sell_price_inc_tax' => '977.7300', 'created_at' => '2020-05-11 13:24:57', 'updated_at' => '2020-05-11 13:24:57', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '177', 'name' => 'DUMMY', 'product_id' => '129', 'sub_sku' => 'AS0129', 'product_variation_id' => '131', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8978.0000', 'dpp_inc_tax' => '8978.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11222.5000', 'sell_price_inc_tax' => '11222.5000', 'created_at' => '2020-05-11 13:25:41', 'updated_at' => '2020-05-11 13:25:41', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '178', 'name' => 'DUMMY', 'product_id' => '130', 'sub_sku' => 'AS0130', 'product_variation_id' => '132', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5648.0000', 'dpp_inc_tax' => '5648.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7060.0000', 'sell_price_inc_tax' => '7060.0000', 'created_at' => '2020-05-11 13:26:17', 'updated_at' => '2020-05-11 13:26:17', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '179', 'name' => 'DUMMY', 'product_id' => '131', 'sub_sku' => 'AS0131', 'product_variation_id' => '133', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8978.0000', 'dpp_inc_tax' => '8978.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11222.5000', 'sell_price_inc_tax' => '11222.5000', 'created_at' => '2020-05-11 13:27:51', 'updated_at' => '2020-05-11 13:27:51', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '180', 'name' => 'DUMMY', 'product_id' => '132', 'sub_sku' => 'AS0132', 'product_variation_id' => '134', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5656.0000', 'dpp_inc_tax' => '5656.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7070.0000', 'sell_price_inc_tax' => '7070.0000', 'created_at' => '2020-05-11 13:30:09', 'updated_at' => '2020-05-11 13:30:09', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '181', 'name' => 'DUMMY', 'product_id' => '133', 'sub_sku' => 'AS0133', 'product_variation_id' => '135', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '900.0000', 'dpp_inc_tax' => '900.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1125.0000', 'sell_price_inc_tax' => '1125.0000', 'created_at' => '2020-05-11 13:31:14', 'updated_at' => '2020-05-11 13:31:14', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '182', 'name' => 'DUMMY', 'product_id' => '134', 'sub_sku' => 'AS0134', 'product_variation_id' => '136', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '897.0000', 'dpp_inc_tax' => '897.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1121.2500', 'sell_price_inc_tax' => '1121.2500', 'created_at' => '2020-05-11 13:32:01', 'updated_at' => '2020-05-11 13:32:01', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '183', 'name' => 'DUMMY', 'product_id' => '135', 'sub_sku' => 'AS0135', 'product_variation_id' => '137', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '10000.0000', 'dpp_inc_tax' => '10000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '12500.0000', 'sell_price_inc_tax' => '12500.0000', 'created_at' => '2020-05-11 13:34:38', 'updated_at' => '2020-05-11 13:34:38', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '184', 'name' => 'DUMMY', 'product_id' => '136', 'sub_sku' => 'AS0136', 'product_variation_id' => '138', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '56464.0000', 'dpp_inc_tax' => '56464.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '70580.0000', 'sell_price_inc_tax' => '70580.0000', 'created_at' => '2020-05-11 13:35:40', 'updated_at' => '2020-05-11 13:35:40', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '185', 'name' => 'DUMMY', 'product_id' => '137', 'sub_sku' => 'AS0137', 'product_variation_id' => '139', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '898.0000', 'dpp_inc_tax' => '898.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1122.5000', 'sell_price_inc_tax' => '1122.5000', 'created_at' => '2020-05-11 13:36:25', 'updated_at' => '2020-05-11 13:36:25', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '186', 'name' => 'DUMMY', 'product_id' => '138', 'sub_sku' => 'AS0138', 'product_variation_id' => '140', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8978.0000', 'dpp_inc_tax' => '8978.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11222.5000', 'sell_price_inc_tax' => '11222.5000', 'created_at' => '2020-05-11 13:37:28', 'updated_at' => '2020-05-11 13:37:28', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '187', 'name' => 'DUMMY', 'product_id' => '139', 'sub_sku' => 'AS0139', 'product_variation_id' => '141', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '8998.0000', 'dpp_inc_tax' => '8998.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11247.5000', 'sell_price_inc_tax' => '11247.5000', 'created_at' => '2020-05-11 13:38:24', 'updated_at' => '2020-05-11 13:38:24', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '188', 'name' => 'DUMMY', 'product_id' => '140', 'sub_sku' => 'AS0140', 'product_variation_id' => '142', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '2000.0000', 'dpp_inc_tax' => '2000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '2500.0000', 'sell_price_inc_tax' => '2500.0000', 'created_at' => '2020-05-11 13:39:16', 'updated_at' => '2020-05-11 13:39:16', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '189', 'name' => 'DUMMY', 'product_id' => '141', 'sub_sku' => 'AS0141', 'product_variation_id' => '143', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '89747.0000', 'dpp_inc_tax' => '89747.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '112183.7500', 'sell_price_inc_tax' => '112183.7500', 'created_at' => '2020-05-11 13:40:55', 'updated_at' => '2020-05-11 13:40:55', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '190', 'name' => 'DUMMY', 'product_id' => '142', 'sub_sku' => 'AS0142', 'product_variation_id' => '144', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '89748.0000', 'dpp_inc_tax' => '89748.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '112185.0000', 'sell_price_inc_tax' => '112185.0000', 'created_at' => '2020-05-11 13:42:14', 'updated_at' => '2020-05-11 13:42:14', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '191', 'name' => 'DUMMY', 'product_id' => '143', 'sub_sku' => 'AS0143', 'product_variation_id' => '145', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '6000.0000', 'dpp_inc_tax' => '6000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7500.0000', 'sell_price_inc_tax' => '7500.0000', 'created_at' => '2020-05-11 13:43:25', 'updated_at' => '2020-05-11 13:43:25', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '192', 'name' => 'DUMMY', 'product_id' => '144', 'sub_sku' => 'AS0144', 'product_variation_id' => '146', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '9000.0000', 'dpp_inc_tax' => '9000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '11250.0000', 'sell_price_inc_tax' => '11250.0000', 'created_at' => '2020-05-11 13:44:10', 'updated_at' => '2020-05-11 13:44:10', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '193', 'name' => 'DUMMY', 'product_id' => '145', 'sub_sku' => 'AS0145', 'product_variation_id' => '147', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '6000.0000', 'dpp_inc_tax' => '6000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7500.0000', 'sell_price_inc_tax' => '7500.0000', 'created_at' => '2020-05-11 13:44:53', 'updated_at' => '2020-05-11 13:44:53', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '194', 'name' => 'DUMMY', 'product_id' => '146', 'sub_sku' => 'AS0146', 'product_variation_id' => '148', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '890000.0000', 'dpp_inc_tax' => '890000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1112500.0000', 'sell_price_inc_tax' => '1112500.0000', 'created_at' => '2020-05-11 13:46:18', 'updated_at' => '2020-05-11 13:46:18', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '195', 'name' => 'DUMMY', 'product_id' => '147', 'sub_sku' => 'AS0147', 'product_variation_id' => '149', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '89000.0000', 'dpp_inc_tax' => '89000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '111250.0000', 'sell_price_inc_tax' => '111250.0000', 'created_at' => '2020-05-11 13:47:32', 'updated_at' => '2020-05-11 13:47:32', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '196', 'name' => 'DUMMY', 'product_id' => '148', 'sub_sku' => 'AS0148', 'product_variation_id' => '150', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5000.0000', 'dpp_inc_tax' => '5000.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '6250.0000', 'sell_price_inc_tax' => '6250.0000', 'created_at' => '2020-05-11 13:48:51', 'updated_at' => '2020-05-11 13:48:51', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '197', 'name' => 'DUMMY', 'product_id' => '149', 'sub_sku' => 'AS0149', 'product_variation_id' => '151', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5646.0000', 'dpp_inc_tax' => '5646.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7057.5000', 'sell_price_inc_tax' => '7057.5000', 'created_at' => '2020-05-11 13:49:20', 'updated_at' => '2020-05-11 13:49:20', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '198', 'name' => 'DUMMY', 'product_id' => '150', 'sub_sku' => 'AS0150', 'product_variation_id' => '152', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '5646.0000', 'dpp_inc_tax' => '5646.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '7057.5000', 'sell_price_inc_tax' => '7057.5000', 'created_at' => '2020-05-11 13:49:45', 'updated_at' => '2020-05-11 13:49:45', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '199', 'name' => 'DUMMY', 'product_id' => '151', 'sub_sku' => 'AS0151', 'product_variation_id' => '153', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '500.0000', 'dpp_inc_tax' => '500.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '625.0000', 'sell_price_inc_tax' => '625.0000', 'created_at' => '2020-05-11 13:50:12', 'updated_at' => '2020-05-11 13:50:12', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '200', 'name' => 'DUMMY', 'product_id' => '152', 'sub_sku' => 'AS0152', 'product_variation_id' => '154', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '7897.0000', 'dpp_inc_tax' => '7897.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '9871.2500', 'sell_price_inc_tax' => '9871.2500', 'created_at' => '2020-05-11 13:51:17', 'updated_at' => '2020-05-11 13:51:17', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '201', 'name' => 'DUMMY', 'product_id' => '153', 'sub_sku' => 'AS0153', 'product_variation_id' => '155', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '600.0000', 'dpp_inc_tax' => '600.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '750.0000', 'sell_price_inc_tax' => '750.0000', 'created_at' => '2020-05-11 13:52:03', 'updated_at' => '2020-05-11 13:52:03', 'deleted_at' => null, 'combo_variations' => '[]'],
            ['id' => '202', 'name' => 'DUMMY', 'product_id' => '154', 'sub_sku' => 'AS0154', 'product_variation_id' => '156', 'woocommerce_variation_id' => null, 'variation_value_id' => null, 'default_purchase_price' => '800.0000', 'dpp_inc_tax' => '800.0000', 'profit_percent' => '25.0000', 'default_sell_price' => '1000.0000', 'sell_price_inc_tax' => '1000.0000', 'created_at' => '2020-05-11 13:52:47', 'updated_at' => '2020-05-11 13:52:47', 'deleted_at' => null, 'combo_variations' => '[]'],
        ];

        DB::table('variations')->insert($variations);

        $res_product_modifier_sets = [
            ['modifier_set_id' => '78', 'product_id' => '72'],
            ['modifier_set_id' => '78', 'product_id' => '73'],
            ['modifier_set_id' => '79', 'product_id' => '73'],
            ['modifier_set_id' => '79', 'product_id' => '69'],
            ['modifier_set_id' => '79', 'product_id' => '72'],
            ['modifier_set_id' => '79', 'product_id' => '75'],
            ['modifier_set_id' => '79', 'product_id' => '68'],
        ];

        DB::table('res_product_modifier_sets')->insert($res_product_modifier_sets);

        DB::insert("INSERT INTO variation_templates (id, name, business_id, 
                    created_at, updated_at) VALUES
                    (1, 'Size (Tshirts)', 1, '2018-01-04 02:52:13', '2018-01-04 02:52:13'),
                    (2, 'Size (Shoes)', 1, '2018-01-04 02:53:21', '2018-01-04 02:53:21'),
                    (3, 'Waist Size (Jeans)', 1, '2018-01-04 02:54:34', '2018-01-04 02:54:34'),
                    (4, 'Color', 1, '2018-01-06 12:42:52', '2018-01-06 12:42:52')");

        DB::insert("INSERT INTO variation_value_templates (id, name, variation_template_id, 
                    created_at, updated_at) VALUES
                    (1, 'S', 1, '2018-01-04 02:52:13', '2018-01-04 02:52:13'),
                    (2, 'M', 1, '2018-01-04 02:52:13', '2018-01-04 02:52:13'),
                    (3, 'L', 1, '2018-01-04 02:52:13', '2018-01-04 02:52:13'),
                    (4, 'XL', 1, '2018-01-04 02:52:13', '2018-01-04 02:52:13'),
                    (5, '5', 2, '2018-01-04 02:53:21', '2018-01-04 02:53:21'),
                    (6, '6', 2, '2018-01-04 02:53:21', '2018-01-04 02:53:21'),
                    (7, '7', 2, '2018-01-04 02:53:21', '2018-01-04 02:53:21'),
                    (8, '8', 2, '2018-01-04 02:53:21', '2018-01-04 02:53:21'),
                    (9, '9', 2, '2018-01-04 02:53:21', '2018-01-04 02:53:21'),
                    (10, '28', 3, '2018-01-04 02:54:34', '2018-01-04 02:54:34'),
                    (11, '30', 3, '2018-01-04 02:54:34', '2018-01-04 02:54:34'),
                    (12, '32', 3, '2018-01-04 02:54:34', '2018-01-04 02:54:34'),
                    (13, '34', 3, '2018-01-04 02:54:35', '2018-01-04 02:54:35'),
                    (14, '36', 3, '2018-01-04 02:54:35', '2018-01-04 02:54:35'),
                    (16, 'Black', 4, '2018-01-06 12:43:17', '2018-01-06 12:43:17'),
                    (17, 'Blue', 4, '2018-01-06 12:43:17', '2018-01-06 12:43:17'),
                    (18, 'Brown', 4, '2018-01-06 12:43:17', '2018-01-06 12:43:17'),
                    (19, 'Grey', 4, '2018-01-06 12:43:17', '2018-01-06 12:43:17'),
                    (20, 'Gold', 4, '2018-01-06 12:43:17', '2018-01-06 12:43:17')");

        $purchase_lines = [
            ['id' => '1', 'transaction_id' => '1', 'product_id' => '2', 'variation_id' => '2', 'quantity' => '100', 'pp_without_discount' => '70.00', 'discount_percent' => '0.00', 'purchase_price' => '70.00', 'purchase_price_inc_tax' => '77.00', 'item_tax' => '7.00', 'tax_id' => '1', 'quantity_sold' => '50.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '2', 'transaction_id' => '1', 'product_id' => '2', 'variation_id' => '3', 'quantity' => '150', 'pp_without_discount' => '70.00', 'discount_percent' => '0.00', 'purchase_price' => '70.00', 'purchase_price_inc_tax' => '77.00', 'item_tax' => '7.00', 'tax_id' => '1', 'quantity_sold' => '60.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '3', 'transaction_id' => '1', 'product_id' => '2', 'variation_id' => '4', 'quantity' => '150', 'pp_without_discount' => '70.00', 'discount_percent' => '0.00', 'purchase_price' => '70.00', 'purchase_price_inc_tax' => '77.00', 'item_tax' => '7.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '4', 'transaction_id' => '1', 'product_id' => '2', 'variation_id' => '5', 'quantity' => '150', 'pp_without_discount' => '72.00', 'discount_percent' => '0.00', 'purchase_price' => '72.00', 'purchase_price_inc_tax' => '79.20', 'item_tax' => '7.20', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '5', 'transaction_id' => '1', 'product_id' => '2', 'variation_id' => '6', 'quantity' => '100', 'pp_without_discount' => '72.00', 'discount_percent' => '0.00', 'purchase_price' => '72.00', 'purchase_price_inc_tax' => '79.20', 'item_tax' => '7.20', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '6', 'transaction_id' => '2', 'product_id' => '14', 'variation_id' => '47', 'quantity' => '100', 'pp_without_discount' => '700.00', 'discount_percent' => '0.00', 'purchase_price' => '700.00', 'purchase_price_inc_tax' => '770.00', 'item_tax' => '70.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 06:58:10', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '7', 'transaction_id' => '3', 'product_id' => '28', 'variation_id' => '71', 'quantity' => '500', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '11.00', 'item_tax' => '1.00', 'tax_id' => '1', 'quantity_sold' => '30.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 07:02:22', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '8', 'transaction_id' => '4', 'product_id' => '21', 'variation_id' => '64', 'quantity' => '200', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '60.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 07:03:12', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '9', 'transaction_id' => '5', 'product_id' => '27', 'variation_id' => '70', 'quantity' => '500', 'pp_without_discount' => '20.00', 'discount_percent' => '0.00', 'purchase_price' => '20.00', 'purchase_price_inc_tax' => '22.00', 'item_tax' => '2.00', 'tax_id' => '1', 'quantity_sold' => '30.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-01-06 07:05:26', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '10', 'transaction_id' => '11', 'product_id' => '34', 'variation_id' => '77', 'quantity' => '50', 'pp_without_discount' => '6.00', 'discount_percent' => '0.00', 'purchase_price' => '6.00', 'purchase_price_inc_tax' => '6.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '10.00', 'quantity_adjusted' => '0.00', 'mfg_date' => '2018-04-10', 'exp_date' => $next_12_month, 'lot_number' => null, 'created_at' => '2018-04-10 10:18:16', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '11', 'transaction_id' => '12', 'product_id' => '32', 'variation_id' => '75', 'quantity' => '100', 'pp_without_discount' => '12.00', 'discount_percent' => '0.00', 'purchase_price' => '12.00', 'purchase_price_inc_tax' => '12.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '7.00', 'quantity_adjusted' => '0.00', 'mfg_date' => '2018-04-12', 'exp_date' => $next_6_month, 'lot_number' => null, 'created_at' => '2018-04-10 10:19:40', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '12', 'transaction_id' => '13', 'product_id' => '36', 'variation_id' => '79', 'quantity' => '150', 'pp_without_discount' => '9.00', 'discount_percent' => '0.00', 'purchase_price' => '9.00', 'purchase_price_inc_tax' => '9.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => '2018-03-30', 'exp_date' => $next_12_month, 'lot_number' => null, 'created_at' => '2018-04-10 10:20:41', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '13', 'transaction_id' => '14', 'product_id' => '33', 'variation_id' => '76', 'quantity' => '180', 'pp_without_discount' => '12.00', 'discount_percent' => '0.00', 'purchase_price' => '12.00', 'purchase_price_inc_tax' => '12.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '27.00', 'quantity_adjusted' => '0.00', 'mfg_date' => '2018-04-10', 'exp_date' => $yesterday, 'lot_number' => null, 'created_at' => '2018-04-10 10:21:38', 'updated_at' => '2018-06-13 11:39:08'],
            ['id' => '14', 'transaction_id' => '18', 'product_id' => '46', 'variation_id' => '89', 'quantity' => '30', 'pp_without_discount' => '321.00', 'discount_percent' => '0.00', 'purchase_price' => '321.00', 'purchase_price_inc_tax' => '321.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-04-10 11:39:54', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '15', 'transaction_id' => '19', 'product_id' => '41', 'variation_id' => '84', 'quantity' => '40', 'pp_without_discount' => '500.00', 'discount_percent' => '0.00', 'purchase_price' => '500.00', 'purchase_price_inc_tax' => '500.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '26.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-04-10 11:40:51', 'updated_at' => '2018-06-13 11:39:09'],
            ['id' => '16', 'transaction_id' => '20', 'product_id' => '45', 'variation_id' => '88', 'quantity' => '45', 'pp_without_discount' => '300.00', 'discount_percent' => '0.00', 'purchase_price' => '300.00', 'purchase_price_inc_tax' => '300.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-04-10 11:41:45', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '17', 'transaction_id' => '21', 'product_id' => '44', 'variation_id' => '87', 'quantity' => '100', 'pp_without_discount' => '195.00', 'discount_percent' => '0.00', 'purchase_price' => '195.00', 'purchase_price_inc_tax' => '195.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '20.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-04-10 11:42:34', 'updated_at' => '2018-06-13 11:39:09'],
            ['id' => '18', 'transaction_id' => '22', 'product_id' => '43', 'variation_id' => '86', 'quantity' => '30', 'pp_without_discount' => '400.00', 'discount_percent' => '0.00', 'purchase_price' => '400.00', 'purchase_price_inc_tax' => '400.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '5.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-04-10 11:43:12', 'updated_at' => '2018-06-13 11:39:09'],
            ['id' => '19', 'transaction_id' => '27', 'product_id' => '42', 'variation_id' => '85', 'quantity' => '50', 'pp_without_discount' => '520.00', 'discount_percent' => '0.00', 'purchase_price' => '520.00', 'purchase_price_inc_tax' => '520.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '20.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-04-10 11:53:36', 'updated_at' => '2018-06-13 11:39:09'],
            ['id' => '20', 'transaction_id' => '37', 'product_id' => '61', 'variation_id' => '104', 'quantity' => '20', 'pp_without_discount' => '34.00', 'discount_percent' => '0.00', 'purchase_price' => '34.00', 'purchase_price_inc_tax' => '34.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-11 16:47:07', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '21', 'transaction_id' => '37', 'product_id' => '64', 'variation_id' => '107', 'quantity' => '200', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-11 16:47:07', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '22', 'transaction_id' => '37', 'product_id' => '63', 'variation_id' => '106', 'quantity' => '200', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-11 16:47:07', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '23', 'transaction_id' => '37', 'product_id' => '65', 'variation_id' => '108', 'quantity' => '50', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-11 16:47:07', 'updated_at' => '2018-06-13 11:39:07'],
            ['id' => '24', 'transaction_id' => '38', 'product_id' => '17', 'variation_id' => '57', 'quantity' => '30', 'pp_without_discount' => '350.00', 'discount_percent' => '0.00', 'purchase_price' => '350.00', 'purchase_price_inc_tax' => '350.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '25', 'transaction_id' => '38', 'product_id' => '17', 'variation_id' => '58', 'quantity' => '50', 'pp_without_discount' => '350.00', 'discount_percent' => '0.00', 'purchase_price' => '350.00', 'purchase_price_inc_tax' => '350.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '26', 'transaction_id' => '38', 'product_id' => '64', 'variation_id' => '107', 'quantity' => '100', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '27', 'transaction_id' => '38', 'product_id' => '15', 'variation_id' => '49', 'quantity' => '30', 'pp_without_discount' => '950.00', 'discount_percent' => '0.00', 'purchase_price' => '950.00', 'purchase_price_inc_tax' => '1045.00', 'item_tax' => '95.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '28', 'transaction_id' => '38', 'product_id' => '15', 'variation_id' => '50', 'quantity' => '20', 'pp_without_discount' => '950.00', 'discount_percent' => '0.00', 'purchase_price' => '950.00', 'purchase_price_inc_tax' => '1045.00', 'item_tax' => '95.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '29', 'transaction_id' => '38', 'product_id' => '15', 'variation_id' => '51', 'quantity' => '30', 'pp_without_discount' => '950.00', 'discount_percent' => '0.00', 'purchase_price' => '950.00', 'purchase_price_inc_tax' => '1045.00', 'item_tax' => '95.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '30', 'transaction_id' => '38', 'product_id' => '15', 'variation_id' => '52', 'quantity' => '20', 'pp_without_discount' => '950.00', 'discount_percent' => '0.00', 'purchase_price' => '950.00', 'purchase_price_inc_tax' => '1045.00', 'item_tax' => '95.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '31', 'transaction_id' => '38', 'product_id' => '15', 'variation_id' => '53', 'quantity' => '40', 'pp_without_discount' => '1010.00', 'discount_percent' => '0.00', 'purchase_price' => '1010.00', 'purchase_price_inc_tax' => '1111.00', 'item_tax' => '101.00', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '32', 'transaction_id' => '38', 'product_id' => '18', 'variation_id' => '59', 'quantity' => '20', 'pp_without_discount' => '1350.00', 'discount_percent' => '0.00', 'purchase_price' => '1350.00', 'purchase_price_inc_tax' => '1350.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '33', 'transaction_id' => '38', 'product_id' => '18', 'variation_id' => '60', 'quantity' => '20', 'pp_without_discount' => '1450.00', 'discount_percent' => '0.00', 'purchase_price' => '1450.00', 'purchase_price_inc_tax' => '1450.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '34', 'transaction_id' => '38', 'product_id' => '63', 'variation_id' => '106', 'quantity' => '100', 'pp_without_discount' => '10.00', 'discount_percent' => '0.00', 'purchase_price' => '10.00', 'purchase_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '35', 'transaction_id' => '38', 'product_id' => '19', 'variation_id' => '61', 'quantity' => '30', 'pp_without_discount' => '8.00', 'discount_percent' => '0.00', 'purchase_price' => '8.00', 'purchase_price_inc_tax' => '8.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '36', 'transaction_id' => '38', 'product_id' => '22', 'variation_id' => '65', 'quantity' => '10', 'pp_without_discount' => '8.00', 'discount_percent' => '0.00', 'purchase_price' => '8.00', 'purchase_price_inc_tax' => '8.80', 'item_tax' => '0.80', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '37', 'transaction_id' => '38', 'product_id' => '24', 'variation_id' => '67', 'quantity' => '10', 'pp_without_discount' => '8.00', 'discount_percent' => '0.00', 'purchase_price' => '8.00', 'purchase_price_inc_tax' => '8.80', 'item_tax' => '0.80', 'tax_id' => '1', 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '38', 'transaction_id' => '38', 'product_id' => '25', 'variation_id' => '68', 'quantity' => '20', 'pp_without_discount' => '15.00', 'discount_percent' => '0.00', 'purchase_price' => '15.00', 'purchase_price_inc_tax' => '15.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '39', 'transaction_id' => '39', 'product_id' => '77', 'variation_id' => '120', 'quantity' => '100', 'pp_without_discount' => '3.00', 'discount_percent' => '0.00', 'purchase_price' => '3.00', 'purchase_price_inc_tax' => '3.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-07-13 07:34:45', 'updated_at' => '2018-07-13 07:34:45'],
            ['id' => '40', 'transaction_id' => '39', 'product_id' => '76', 'variation_id' => '119', 'quantity' => '200', 'pp_without_discount' => '8.00', 'discount_percent' => '0.00', 'purchase_price' => '8.00', 'purchase_price_inc_tax' => '8.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-07-13 07:34:45', 'updated_at' => '2018-07-13 07:34:45'],
            ['id' => '41', 'transaction_id' => '40', 'product_id' => '76', 'variation_id' => '119', 'quantity' => '100', 'pp_without_discount' => '8.00', 'discount_percent' => '0.00', 'purchase_price' => '8.00', 'purchase_price_inc_tax' => '8.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.00', 'quantity_adjusted' => '0.00', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2018-07-13 07:38:24', 'updated_at' => '2018-07-13 07:38:24'],
        ];
        DB::table('purchase_lines')->insert($purchase_lines);

        $mfg_purchase_lines = [
            ['id' => '42', 'transaction_id' => '45', 'product_id' => '88', 'variation_id' => '136', 'quantity' => '2000.0000', 'pp_without_discount' => '0.50', 'discount_percent' => '0.00', 'purchase_price' => '0.50', 'purchase_price_inc_tax' => '0.50', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '100.0000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:01:34', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '43', 'transaction_id' => '46', 'product_id' => '87', 'variation_id' => '135', 'quantity' => '2000.0000', 'pp_without_discount' => '0.20', 'discount_percent' => '0.00', 'purchase_price' => '0.20', 'purchase_price_inc_tax' => '0.20', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '500.0000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:01:49', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '44', 'transaction_id' => '47', 'product_id' => '86', 'variation_id' => '134', 'quantity' => '1000.0000', 'pp_without_discount' => '0.50', 'discount_percent' => '0.00', 'purchase_price' => '0.50', 'purchase_price_inc_tax' => '0.50', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '0.0000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:02:07', 'updated_at' => '2019-08-18 19:02:07'],
            ['id' => '45', 'transaction_id' => '48', 'product_id' => '84', 'variation_id' => '132', 'quantity' => '2000.0000', 'pp_without_discount' => '0.20', 'discount_percent' => '0.00', 'purchase_price' => '0.20', 'purchase_price_inc_tax' => '0.20', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '50.0000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:02:20', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '46', 'transaction_id' => '49', 'product_id' => '82', 'variation_id' => '130', 'quantity' => '1000.0000', 'pp_without_discount' => '20.00', 'discount_percent' => '0.00', 'purchase_price' => '20.00', 'purchase_price_inc_tax' => '20.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '5.0000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:02:33', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '47', 'transaction_id' => '50', 'product_id' => '85', 'variation_id' => '133', 'quantity' => '100.0000', 'pp_without_discount' => '0.20', 'discount_percent' => '0.00', 'purchase_price' => '0.20', 'purchase_price_inc_tax' => '0.20', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '100.0000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:02:52', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '48', 'transaction_id' => '51', 'product_id' => '83', 'variation_id' => '131', 'quantity' => '2000.0000', 'pp_without_discount' => '0.10', 'discount_percent' => '0.00', 'purchase_price' => '0.10', 'purchase_price_inc_tax' => '0.10', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '502.5000', 'mfg_date' => null, 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:03:10', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '49', 'transaction_id' => '52', 'product_id' => '81', 'variation_id' => '129', 'quantity' => '5.0000', 'pp_without_discount' => '2937.00', 'discount_percent' => '0.00', 'purchase_price' => '2937.00', 'purchase_price_inc_tax' => '2937.00', 'item_tax' => '0.00', 'tax_id' => null, 'quantity_sold' => '0.0000', 'quantity_adjusted' => '0.0000', 'quantity_returned' => '0.0000', 'mfg_quantity_used' => '0.0000', 'mfg_date' => '2019-08-18', 'exp_date' => null, 'lot_number' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
        ];
        DB::table('purchase_lines')->insert($mfg_purchase_lines);

        DB::insert("INSERT INTO transactions (id, business_id, location_id, type, status, payment_status, adjustment_type, contact_id, customer_group_id, invoice_no, ref_no, transaction_date, total_before_tax, tax_id, tax_amount, discount_type, discount_amount, shipping_details, shipping_charges, additional_notes, staff_note, final_total, expense_category_id, expense_for, commission_agent, document, is_direct_sale, exchange_rate, total_amount_recovered, transfer_parent_id, opening_stock_product_id, created_by, created_at, updated_at) VALUES
(1, 1, 1, 'purchase', 'received', 'paid', NULL, 2, NULL, NULL, '35001BCVX', '$last_15th_day', '50600.00', 1, '5060.00', NULL, '0', NULL, '0.00', NULL, NULL, '55660.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 06:57:11', '2018-01-06 06:57:11'),
(2, 1, 1, 'purchase', 'received', 'paid', NULL, 5, NULL, NULL, '35001BJGN', '$last_15th_day', '77000.00', 1, '7700.00', NULL, '0', NULL, '0.00', NULL, NULL, '84700.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 06:58:10', '2018-01-06 06:58:10'),
(3, 1, 1, 'purchase', 'received', 'partial', NULL, 6, NULL, NULL, '35001BCVJ', '$last_month', '5500.00', 1, '550.00', NULL, '0', NULL, '0.00', NULL, NULL, '6050.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:02:22', '2018-01-06 07:02:22'),
(4, 1, 1, 'purchase', 'received', 'paid', NULL, 6, NULL, NULL, '35001BCVK', '$last_month', '2000.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '2000.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:03:12', '2018-01-06 07:03:12'),
(5, 1, 1, 'purchase', 'received', 'due', NULL, 6, NULL, NULL, '35001BCVD', '$last_week', '11000.00', 1, '1100.00', NULL, '0', NULL, '0.00', NULL, NULL, '12100.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:05:26', '2018-01-06 07:05:26'),
(6, 1, 1, 'sell', 'final', 'paid', NULL, 4, NULL, 'AS0001', '', '2018-04-10 13:23:21', '770.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '770.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:06:11', '2018-01-06 07:06:11'),
(7, 1, 1, 'sell', 'final', 'paid', NULL, 1, NULL, 'AS0002', '', '$yesterday', '825.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '825.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:06:31', '2018-01-06 07:06:31'),
(8, 1, 1, 'sell', 'final', 'paid', NULL, 4, NULL, 'AS0003', '', '$yesterday', '7700.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '7700.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:07:22', '2018-01-06 07:07:22'),
(9, 1, 1, 'sell', 'final', 'paid', NULL, 1, NULL, 'AS0004', '', '$today', '750.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '750.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:07:45', '2018-01-06 07:07:45'),
(10, 1, 1, 'sell', 'final', 'paid', NULL, 1, NULL, 'AS0005', '', '$today', '412.50', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '412.50', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 1, '2018-01-06 07:08:03', '2018-01-06 07:08:03'),
(11, 2, 2, 'purchase', 'received', 'paid', NULL, 8, NULL, NULL, '56247065', '$last_week', '300.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '300.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:18:16', '2018-04-10 10:18:47'),
(12, 2, 2, 'purchase', 'received', 'paid', NULL, 9, NULL, NULL, '547887025', '$last_week', '1200.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '1200.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:19:40', '2018-04-10 10:19:49'),
(13, 2, 2, 'purchase', 'received', 'paid', NULL, 9, NULL, NULL, '45470025', '$last_month', '1350.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '1350.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:20:41', '2018-04-10 10:20:55'),
(14, 2, 2, 'purchase', 'received', 'partial', NULL, 9, NULL, NULL, '65589898', '$last_month', '2160.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '2160.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:21:38', '2018-04-10 10:22:10'),
(15, 2, 2, 'sell', 'final', 'paid', NULL, 7, NULL, '0001', '', '$yesterday', '75.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '75.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:23:35', '2018-04-10 10:23:35'),
(16, 2, 2, 'sell', 'final', 'paid', NULL, 7, NULL, '0002', '', '$yesterday', '105.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '105.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:23:58', '2018-04-10 10:23:58'),
(17, 2, 2, 'sell', 'final', 'paid', NULL, 7, NULL, '0003', '', '$today', '405.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '405.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 4, '2018-04-10 10:24:21', '2018-04-10 10:26:26'),
(18, 3, 3, 'purchase', 'received', 'paid', NULL, 11, NULL, NULL, '548471117', '$last_month', '9630.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '9630.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:39:54', '2018-04-10 11:40:02'),
(19, 3, 3, 'purchase', 'received', 'paid', NULL, 12, NULL, NULL, '548222220', '$last_week', '20000.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '20000.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:40:51', '2018-04-10 11:40:58'),
(20, 3, 3, 'purchase', 'received', 'paid', NULL, 12, NULL, NULL, '878445524', '$last_15th_day', '13500.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '13500.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:41:45', '2018-04-10 11:43:19'),
(21, 3, 3, 'purchase', 'received', 'paid', NULL, 12, NULL, NULL, '582500058', '$last_15th_day', '19500.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '19500.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:42:34', '2018-04-10 11:43:24'),
(22, 3, 3, 'purchase', 'received', 'partial', NULL, 11, NULL, NULL, '848485552', '$last_week', '12000.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '12000.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:43:12', '2018-04-10 11:43:35'),
(23, 3, 3, 'sell', 'final', 'paid', NULL, 10, NULL, 'AE0001', '', '$today', '2500.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '2500.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:44:47', '2018-04-10 11:47:10'),
(24, 3, 3, 'sell', 'final', 'paid', NULL, 10, NULL, 'AE0002', '', '$yesterday', '4875.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '4875.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:45:06', '2018-04-10 11:46:43'),
(25, 3, 3, 'sell', 'final', 'paid', NULL, 10, NULL, 'AE0003', '', '$yesterday', '625.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '625.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:46:02', '2018-04-10 11:46:02'),
(26, 3, 3, 'sell', 'final', 'paid', NULL, 10, NULL, 'AE0004', '', '$today', '15625.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '15625.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:48:48', '2018-04-10 11:48:48'),
(27, 3, 3, 'purchase', 'received', 'due', NULL, 12, NULL, NULL, '1545477882', '$last_15th_day', '26000.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '26000.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:53:36', '2018-04-10 11:53:36'),
(28, 3, 3, 'sell', 'final', 'paid', NULL, 10, NULL, 'AE0005', '', '$yesterday', '13000.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '13000.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 5, '2018-04-10 11:54:16', '2018-04-10 11:54:16'),
(29, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0001', '', '$today', '656.25', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '656.25', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:04:40', '2018-04-10 13:04:40'),
(30, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0002', '', '$yesterday', '250.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '250.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:04:54', '2018-04-10 13:04:54'),
(31, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0003', '', '$today', '75.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '75.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:05:19', '2018-04-10 13:05:19'),
(32, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0004', '', '$today', '37.50', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '37.50', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:07:50', '2018-04-10 13:07:50'),
(33, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0005', '', '$yesterday', '375.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '375.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:08:01', '2018-04-10 13:08:01'),
(34, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0006', '', '$yesterday', '250.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '250.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:08:15', '2018-04-10 13:08:15'),
(35, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0007', '', '$today', '100.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '100.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:08:27', '2018-04-10 13:08:27'),
(36, 4, 4, 'sell', 'final', 'paid', NULL, 13, NULL, '0008', '', '$yesterday', '125.00', NULL, '0.00', 'percentage', '0', NULL, '0.00', NULL, NULL, '125.00', NULL, NULL, NULL, NULL, 0, '1.000', NULL, NULL, NULL, 6, '2018-04-10 13:08:49', '2018-04-10 13:08:49'),
(37, 1, 1, 'purchase', 'received', 'due', NULL, 6, NULL, NULL, 'PO2018/0001','$yesterday', '5180.00', NULL, '0.00', NULL, '0', NULL, '0.00', NULL, NULL, '5180.00', NULL, NULL, NULL, NULL, '0', '1.000', NULL, NULL, NULL, 1 , '2018-06-11 22:17:07', '2018-06-11 22:17:07'),
(38, 1, 1, 'purchase', 'received', 'due', NULL, 6, NULL, NULL, 'PO2018/0002', '$today', '235656.00', NULL,'0.00', NULL, '0', NULL, '0.00', NULL, NULL, '235656.00', NULL, NULL, NULL, NULL, '0', '1.000', NULL, NULL,NULL, 1, '2018-06-12 23:18:04', '2018-06-12 23:18:04')");

        $new_transactions = [
            ['id' => '39', 'business_id' => '5', 'location_id' => '5', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'purchase', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'due', 'adjustment_type' => null, 'contact_id' => '15', 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => 'PO2018/0001', 'transaction_date' => $yesterday, 'total_before_tax' => '1900.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => '0', 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '1900.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '7', 'created_at' => '2018-07-13 07:34:45', 'updated_at' => '2018-07-13 07:34:45'],
            ['id' => '40', 'business_id' => '5', 'location_id' => '5', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'purchase', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'due', 'adjustment_type' => null, 'contact_id' => '15', 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => 'PO2018/0002', 'transaction_date' => $yesterday, 'total_before_tax' => '800.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => '0', 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '800.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '7', 'created_at' => '2018-07-13 07:38:24', 'updated_at' => '2018-07-13 07:38:24'],
            ['id' => '41', 'business_id' => '5', 'location_id' => '5', 'res_table_id' => '1', 'res_waiter_id' => '8', 'res_order_status' => 'served', 'type' => 'sell', 'status' => 'final', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => '14', 'customer_group_id' => null, 'invoice_no' => '0001', 'ref_no' => '', 'transaction_date' => $today, 'total_before_tax' => '42.50', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => 'percentage', 'discount_amount' => '0', 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '42.50', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '7', 'created_at' => '2018-07-13 07:44:40', 'updated_at' => '2018-07-13 07:48:07'],
            ['id' => '42', 'business_id' => '5', 'location_id' => '5', 'res_table_id' => '2', 'res_waiter_id' => '8', 'res_order_status' => null, 'type' => 'sell', 'status' => 'final', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => '14', 'customer_group_id' => null, 'invoice_no' => '0002', 'ref_no' => '', 'transaction_date' => $today, 'total_before_tax' => '77.50', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => 'percentage', 'discount_amount' => '0', 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '77.50', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '7', 'created_at' => '2018-07-13 07:45:28', 'updated_at' => '2018-07-13 07:45:29'],
            ['id' => '43', 'business_id' => '5', 'location_id' => '5', 'res_table_id' => '3', 'res_waiter_id' => '8', 'res_order_status' => null, 'type' => 'sell', 'status' => 'final', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => '14', 'customer_group_id' => null, 'invoice_no' => '0003', 'ref_no' => '', 'transaction_date' => $today, 'total_before_tax' => '33.75', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => 'percentage', 'discount_amount' => '0', 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '33.75', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '7', 'created_at' => '2018-07-13 07:46:04', 'updated_at' => '2018-07-13 07:46:04'],
            ['id' => '44', 'business_id' => '5', 'location_id' => '5', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => 'cooked', 'type' => 'sell', 'status' => 'final', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => '14', 'customer_group_id' => null, 'invoice_no' => '0004', 'ref_no' => '', 'transaction_date' => $today, 'total_before_tax' => '71.25', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => 'percentage', 'discount_amount' => '0', 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '71.25', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '7', 'created_at' => '2018-07-13 07:46:36', 'updated_at' => '2018-07-13 07:46:55'],
        ];
        DB::table('transactions')->insert($new_transactions);
        $mfg_transactions = [
            ['id' => '45', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:01:33', 'total_before_tax' => '1000.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '1000.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '88', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:01:33', 'updated_at' => '2019-08-18 19:01:33'],
            ['id' => '46', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:01:49', 'total_before_tax' => '400.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '400.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '87', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:01:49', 'updated_at' => '2019-08-18 19:01:49'],
            ['id' => '47', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:02:07', 'total_before_tax' => '500.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '500.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '86', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:02:07', 'updated_at' => '2019-08-18 19:02:07'],
            ['id' => '48', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:02:20', 'total_before_tax' => '400.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '400.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '84', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:02:20', 'updated_at' => '2019-08-18 19:02:20'],
            ['id' => '49', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:02:33', 'total_before_tax' => '20000.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '20000.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '82', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:02:33', 'updated_at' => '2019-08-18 19:02:33'],
            ['id' => '50', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:02:52', 'total_before_tax' => '20.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '20.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '85', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:02:52', 'updated_at' => '2019-08-18 19:02:52'],
            ['id' => '51', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'opening_stock', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'paid', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-01-01 19:03:10', 'total_before_tax' => '200.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '200.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => '83', 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:03:10', 'updated_at' => '2019-08-18 19:03:10'],
            ['id' => '52', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'production_purchase', 'status' => 'received', 'is_quotation' => '0', 'payment_status' => 'due', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => '2019/0001', 'subscription_no' => null, 'transaction_date' => '2019-08-18 19:08:00', 'total_before_tax' => '0.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '14685.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '12', 'mfg_parent_production_purchase_id' => null, 'mfg_wasted_units' => '0.0000', 'mfg_production_cost' => '10.0000', 'mfg_is_final' => '1', 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '53', 'business_id' => '6', 'location_id' => '6', 'res_table_id' => null, 'res_waiter_id' => null, 'res_order_status' => null, 'type' => 'production_sell', 'status' => 'final', 'is_quotation' => '0', 'payment_status' => 'due', 'adjustment_type' => null, 'contact_id' => null, 'customer_group_id' => null, 'invoice_no' => null, 'ref_no' => null, 'subscription_no' => null, 'transaction_date' => '2019-08-18 19:08:00', 'total_before_tax' => '0.00', 'tax_id' => null, 'tax_amount' => '0.00', 'discount_type' => null, 'discount_amount' => null, 'shipping_details' => null, 'shipping_charges' => '0.00', 'additional_notes' => null, 'staff_note' => null, 'final_total' => '14685.00', 'expense_category_id' => null, 'expense_for' => null, 'commission_agent' => null, 'document' => null, 'is_direct_sale' => '0', 'exchange_rate' => '1.000', 'total_amount_recovered' => null, 'transfer_parent_id' => null, 'opening_stock_product_id' => null, 'created_by' => '12', 'mfg_parent_production_purchase_id' => '52', 'mfg_wasted_units' => null, 'mfg_production_cost' => '0.0000', 'mfg_is_final' => '0', 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
        ];
        DB::table('transactions')->insert($mfg_transactions);

        DB::insert("INSERT INTO transaction_payments (id, transaction_id, amount, method, card_transaction_number, card_number, card_type, card_holder_name, card_month, card_year, card_security, cheque_number, bank_account_number, paid_on, created_by, payment_for, parent_id, note, created_at, updated_at) VALUES
                    (1, 6, '770.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-09 17:30:35', 1, NULL, NULL, NULL, '2018-01-06 01:36:11', '2018-01-06 01:36:11'),
(2, 7, '825.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-09 17:30:35', 1, NULL, NULL, NULL, '2018-01-06 01:36:31', '2018-01-06 01:36:31'),
(3, 8, '7700.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-09 17:30:35', 1, NULL, NULL, NULL, '2018-01-06 01:37:23', '2018-01-06 01:37:23'),
(4, 9, '750.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-09 17:30:35', 1, NULL, NULL, NULL, '2018-01-06 01:37:45', '2018-01-06 01:37:45'),
(5, 10, '412.50', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-09 17:30:35', 1, NULL, NULL, NULL, '2018-01-06 01:38:03', '2018-01-06 01:38:03'),
(6, 4, '2000.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-11 17:32:56', 1, NULL, NULL, 'Cash Payment', '2018-01-11 06:32:56', '2018-01-11 06:32:56'),
(7, 3, '3000.00', 'bank_transfer', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, '************', '2018-01-11 17:34:10', 1, NULL, NULL, '3000 Paid Via Bank Transfer', '2018-01-11 06:34:10', '2018-01-11 06:34:10'),
(8, 2, '84700.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-11 17:34:36', 1, NULL, NULL, NULL, '2018-01-11 06:34:36', '2018-01-11 06:34:36'),
(9, 1, '50000.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-11 17:35:04', 1, NULL, NULL, NULL, '2018-01-11 06:35:04', '2018-01-11 06:35:04'),
(10, 1, '5660.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-01-11 17:35:17', 1, NULL, NULL, NULL, '2018-01-11 06:35:17', '2018-01-11 06:35:17'),
(11, 11, '300.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:48:47', 4, 8, NULL, NULL, '2018-04-10 10:18:47', '2018-04-10 10:18:47'),
(12, 12, '1200.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:49:49', 4, 9, NULL, NULL, '2018-04-10 10:19:49', '2018-04-10 10:19:49'),
(13, 13, '1350.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:50:55', 4, 9, NULL, NULL, '2018-04-10 10:20:55', '2018-04-10 10:20:55'),
(14, 14, '1500.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:52:10', 4, 9, NULL, NULL, '2018-04-10 10:22:10', '2018-04-10 10:22:10'),
(15, 15, '75.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:53:35', 4, 7, NULL, NULL, '2018-04-10 10:23:35', '2018-04-10 10:23:35'),
(16, 16, '105.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:53:58', 4, 7, NULL, NULL, '2018-04-10 10:23:58', '2018-04-10 10:23:58'),
(17, 17, '405.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 15:54:21', 4, 7, NULL, NULL, '2018-04-10 10:24:21', '2018-04-10 10:26:26'),
(18, 18, '9630.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:10:02', 5, 11, NULL, NULL, '2018-04-10 11:40:02', '2018-04-10 11:40:02'),
(19, 19, '20000.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:10:58', 5, 12, NULL, NULL, '2018-04-10 11:40:58', '2018-04-10 11:40:58'),
(20, 20, '13500.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:13:19', 5, 12, NULL, NULL, '2018-04-10 11:43:19', '2018-04-10 11:43:19'),
(21, 21, '19500.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:13:24', 5, 12, NULL, NULL, '2018-04-10 11:43:24', '2018-04-10 11:43:24'),
(22, 22, '5000.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:13:35', 5, 11, NULL, NULL, '2018-04-10 11:43:35', '2018-04-10 11:43:35'),
(23, 23, '2500.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:14:47', 5, 10, NULL, NULL, '2018-04-10 11:44:47', '2018-04-10 11:47:10'),
(24, 24, '4875.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:15:06', 5, 10, NULL, NULL, '2018-04-10 11:45:06', '2018-04-10 11:46:43'),
(25, 25, '625.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:16:02', 5, 10, NULL, NULL, '2018-04-10 11:46:02', '2018-04-10 11:46:02'),
(26, 26, '15625.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:18:48', 5, 10, NULL, NULL, '2018-04-10 11:48:48', '2018-04-10 11:48:48'),
(27, 28, '13000.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 17:24:16', 5, 10, NULL, NULL, '2018-04-10 11:54:16', '2018-04-10 11:54:16'),
(28, 29, '656.25', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:34:40', 6, 13, NULL, NULL, '2018-04-10 13:04:40', '2018-04-10 13:04:40'),
(29, 30, '250.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:34:54', 6, 13, NULL, NULL, '2018-04-10 13:04:54', '2018-04-10 13:04:54'),
(30, 31, '75.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:35:19', 6, 13, NULL, NULL, '2018-04-10 13:05:19', '2018-04-10 13:05:19'),
(31, 32, '37.50', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:37:50', 6, 13, NULL, NULL, '2018-04-10 13:07:50', '2018-04-10 13:07:50'),
(32, 33, '375.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:38:01', 6, 13, NULL, NULL, '2018-04-10 13:08:01', '2018-04-10 13:08:01'),
(33, 34, '250.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:38:15', 6, 13, NULL, NULL, '2018-04-10 13:08:15', '2018-04-10 13:08:15'),
(34, 35, '100.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:38:27', 6, 13, NULL, NULL, '2018-04-10 13:08:27', '2018-04-10 13:08:27'),
(35, 36, '125.00', 'cash', NULL, NULL, 'visa', NULL, NULL, NULL, NULL, NULL, NULL, '2018-04-10 18:38:49', 6, 13, NULL, NULL, '2018-04-10 13:08:49', '2018-04-10 13:08:49')");

        $transaction_sell_lines = [
            ['id' => '1', 'transaction_id' => '6', 'product_id' => '2', 'variation_id' => '3', 'quantity' => '10', 'unit_price' => '70.00', 'unit_price_inc_tax' => '77.00', 'item_tax' => '7.00', 'tax_id' => '1', 'sell_line_note' => null, 'parent_sell_line_id' => null, 'created_at' => '2018-01-06 07:06:11', 'updated_at' => '2018-01-06 07:06:11', 'unit_price_before_discount' => '70.00'],
            ['id' => '2', 'transaction_id' => '7', 'product_id' => '27', 'variation_id' => '70', 'quantity' => '30', 'unit_price' => '25.00', 'unit_price_inc_tax' => '27.50', 'item_tax' => '2.50', 'tax_id' => '1', 'sell_line_note' => null, 'parent_sell_line_id' => null, 'created_at' => '2018-01-06 07:06:31', 'updated_at' => '2018-01-06 07:06:31', 'unit_price_before_discount' => '25.00'],
            ['id' => '3', 'transaction_id' => '8', 'product_id' => '2', 'variation_id' => '3', 'quantity' => '50', 'unit_price' => '70.00', 'unit_price_inc_tax' => '77.00', 'item_tax' => '7.00', 'tax_id' => '1', 'sell_line_note' => null, 'parent_sell_line_id' => null, 'created_at' => '2018-01-06 07:07:23', 'updated_at' => '2018-01-06 07:07:23', 'unit_price_before_discount' => '70.00'],
            ['id' => '4', 'transaction_id' => '8', 'product_id' => '2', 'variation_id' => '2', 'quantity' => '50', 'unit_price' => '70.00', 'unit_price_inc_tax' => '77.00', 'item_tax' => '7.00', 'tax_id' => '1', 'sell_line_note' => null, 'parent_sell_line_id' => null, 'created_at' => '2018-01-06 07:07:23', 'updated_at' => '2018-01-06 07:07:23', 'unit_price_before_discount' => '70.00'],
            ['id' => '5', 'transaction_id' => '9', 'product_id' => '21', 'variation_id' => '64', 'quantity' => '60', 'unit_price' => '12.50', 'unit_price_inc_tax' => '12.50', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => null, 'parent_sell_line_id' => null, 'created_at' => '2018-01-06 07:07:45', 'updated_at' => '2018-01-06 07:07:45', 'unit_price_before_discount' => '12.50'],
            ['id' => '6', 'transaction_id' => '10', 'product_id' => '28', 'variation_id' => '71', 'quantity' => '30', 'unit_price' => '12.50', 'unit_price_inc_tax' => '13.75', 'item_tax' => '1.25', 'tax_id' => '1', 'sell_line_note' => null, 'parent_sell_line_id' => null, 'created_at' => '2018-01-06 07:08:03', 'updated_at' => '2018-01-06 07:08:03', 'unit_price_before_discount' => '12.50'],
            ['id' => '7', 'transaction_id' => '15', 'product_id' => '34', 'variation_id' => '77', 'quantity' => '10', 'unit_price' => '7.50', 'unit_price_inc_tax' => '7.50', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 10:23:35', 'updated_at' => '2018-04-10 10:23:35', 'unit_price_before_discount' => '7.50'],
            ['id' => '8', 'transaction_id' => '16', 'product_id' => '32', 'variation_id' => '75', 'quantity' => '7', 'unit_price' => '15.00', 'unit_price_inc_tax' => '15.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 10:23:58', 'updated_at' => '2018-04-10 10:23:58', 'unit_price_before_discount' => '15.00'],
            ['id' => '9', 'transaction_id' => '17', 'product_id' => '33', 'variation_id' => '76', 'quantity' => '27', 'unit_price' => '15.00', 'unit_price_inc_tax' => '15.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 10:24:21', 'updated_at' => '2018-04-10 10:26:26', 'unit_price_before_discount' => '15.00'],
            ['id' => '10', 'transaction_id' => '23', 'product_id' => '43', 'variation_id' => '86', 'quantity' => '5', 'unit_price' => '500.00', 'unit_price_inc_tax' => '500.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => 'IHN45822225007', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 11:44:47', 'updated_at' => '2018-04-10 11:47:10', 'unit_price_before_discount' => '500.00'],
            ['id' => '11', 'transaction_id' => '24', 'product_id' => '44', 'variation_id' => '87', 'quantity' => '20', 'unit_price' => '243.75', 'unit_price_inc_tax' => '243.75', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => 'SMJ7558455524', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 11:45:06', 'updated_at' => '2018-04-10 11:46:43', 'unit_price_before_discount' => '243.75'],
            ['id' => '12', 'transaction_id' => '25', 'product_id' => '41', 'variation_id' => '84', 'quantity' => '1', 'unit_price' => '625.00', 'unit_price_inc_tax' => '625.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => 'EXSD1225778855', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 11:46:02', 'updated_at' => '2018-04-10 11:46:02', 'unit_price_before_discount' => '625.00'],
            ['id' => '13', 'transaction_id' => '26', 'product_id' => '41', 'variation_id' => '84', 'quantity' => '25', 'unit_price' => '625.00', 'unit_price_inc_tax' => '625.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => 'HP020555548444', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 11:48:48', 'updated_at' => '2018-04-10 11:48:48', 'unit_price_before_discount' => '625.00'],
            ['id' => '14', 'transaction_id' => '28', 'product_id' => '42', 'variation_id' => '85', 'quantity' => '20', 'unit_price' => '650.00', 'unit_price_inc_tax' => '650.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => 'HP68400258F', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 11:54:16', 'updated_at' => '2018-04-10 11:54:16', 'unit_price_before_discount' => '650.00'],
            ['id' => '15', 'transaction_id' => '29', 'product_id' => '53', 'variation_id' => '96', 'quantity' => '5', 'unit_price' => '131.25', 'unit_price_inc_tax' => '131.25', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:04:40', 'updated_at' => '2018-04-10 13:04:40', 'unit_price_before_discount' => '131.25'],
            ['id' => '16', 'transaction_id' => '30', 'product_id' => '51', 'variation_id' => '94', 'quantity' => '2', 'unit_price' => '125.00', 'unit_price_inc_tax' => '125.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:04:54', 'updated_at' => '2018-04-10 13:04:54', 'unit_price_before_discount' => '125.00'],
            ['id' => '17', 'transaction_id' => '31', 'product_id' => '57', 'variation_id' => '100', 'quantity' => '3', 'unit_price' => '25.00', 'unit_price_inc_tax' => '25.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:05:19', 'updated_at' => '2018-04-10 13:05:19', 'unit_price_before_discount' => '25.00'],
            ['id' => '18', 'transaction_id' => '32', 'product_id' => '60', 'variation_id' => '103', 'quantity' => '2', 'unit_price' => '18.75', 'unit_price_inc_tax' => '18.75', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:07:50', 'updated_at' => '2018-04-10 13:07:50', 'unit_price_before_discount' => '18.75'],
            ['id' => '19', 'transaction_id' => '33', 'product_id' => '56', 'variation_id' => '99', 'quantity' => '2', 'unit_price' => '187.50', 'unit_price_inc_tax' => '187.50', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:08:01', 'updated_at' => '2018-04-10 13:08:01', 'unit_price_before_discount' => '187.50'],
            ['id' => '20', 'transaction_id' => '34', 'product_id' => '54', 'variation_id' => '97', 'quantity' => '20', 'unit_price' => '12.50', 'unit_price_inc_tax' => '12.50', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:08:15', 'updated_at' => '2018-04-10 13:08:15', 'unit_price_before_discount' => '12.50'],
            ['id' => '21', 'transaction_id' => '35', 'product_id' => '55', 'variation_id' => '98', 'quantity' => '10', 'unit_price' => '10.00', 'unit_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:08:27', 'updated_at' => '2018-04-10 13:08:27', 'unit_price_before_discount' => '10.00'],
            ['id' => '22', 'transaction_id' => '36', 'product_id' => '57', 'variation_id' => '100', 'quantity' => '5', 'unit_price' => '25.00', 'unit_price_inc_tax' => '25.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-04-10 13:08:49', 'updated_at' => '2018-04-10 13:08:49', 'unit_price_before_discount' => '25.00'],
            ['id' => '23', 'transaction_id' => '41', 'product_id' => '71', 'variation_id' => '114', 'quantity' => '2', 'unit_price' => '21.25', 'unit_price_inc_tax' => '21.25', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:44:40', 'updated_at' => '2018-07-13 07:44:40', 'unit_price_before_discount' => '21.25'],
            ['id' => '24', 'transaction_id' => '42', 'product_id' => '70', 'variation_id' => '113', 'quantity' => '2', 'unit_price' => '15.00', 'unit_price_inc_tax' => '15.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:45:28', 'updated_at' => '2018-07-13 07:45:28', 'unit_price_before_discount' => '15.00'],
            ['id' => '25', 'transaction_id' => '42', 'product_id' => '67', 'variation_id' => '110', 'quantity' => '2', 'unit_price' => '10.00', 'unit_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:45:28', 'updated_at' => '2018-07-13 07:45:28', 'unit_price_before_discount' => '10.00'],
            ['id' => '26', 'transaction_id' => '42', 'product_id' => '68', 'variation_id' => '111', 'quantity' => '2', 'unit_price' => '13.75', 'unit_price_inc_tax' => '13.75', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:45:28', 'updated_at' => '2018-07-13 07:45:28', 'unit_price_before_discount' => '13.75'],
            ['id' => '27', 'transaction_id' => '43', 'product_id' => '74', 'variation_id' => '117', 'quantity' => '1', 'unit_price' => '8.75', 'unit_price_inc_tax' => '8.75', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:46:04', 'updated_at' => '2018-07-13 07:46:04', 'unit_price_before_discount' => '8.75'],
            ['id' => '28', 'transaction_id' => '43', 'product_id' => '73', 'variation_id' => '116', 'quantity' => '1', 'unit_price' => '25.00', 'unit_price_inc_tax' => '25.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:46:04', 'updated_at' => '2018-07-13 07:46:04', 'unit_price_before_discount' => '25.00'],
            ['id' => '29', 'transaction_id' => '44', 'product_id' => '74', 'variation_id' => '117', 'quantity' => '3', 'unit_price' => '8.75', 'unit_price_inc_tax' => '8.75', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:46:36', 'updated_at' => '2018-07-13 07:46:36', 'unit_price_before_discount' => '8.75'],
            ['id' => '30', 'transaction_id' => '44', 'product_id' => '69', 'variation_id' => '112', 'quantity' => '3', 'unit_price' => '15.00', 'unit_price_inc_tax' => '15.00', 'item_tax' => '0.00', 'tax_id' => null, 'sell_line_note' => '', 'parent_sell_line_id' => null, 'created_at' => '2018-07-13 07:46:36', 'updated_at' => '2018-07-13 07:46:36', 'unit_price_before_discount' => '15.00'],
        ];

        DB::table('transaction_sell_lines')->insert($transaction_sell_lines);

        $mfg_sell_lines = [
            ['id' => '31', 'transaction_id' => '53', 'product_id' => '82', 'variation_id' => '130', 'quantity' => '5.0000', 'mfg_waste_percent' => '0.00', 'quantity_returned' => '0.0000', 'unit_price_before_discount' => '20.00', 'unit_price' => '20.00', 'line_discount_type' => null, 'line_discount_amount' => '0.00', 'unit_price_inc_tax' => '20.00', 'item_tax' => '0.00', 'tax_id' => null, 'discount_id' => null, 'lot_no_line_id' => null, 'sell_line_note' => '', 'res_service_staff_id' => null, 'res_line_order_status' => null, 'woocommerce_line_items_id' => null, 'parent_sell_line_id' => null, 'children_type' => '', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '32', 'transaction_id' => '53', 'product_id' => '83', 'variation_id' => '131', 'quantity' => '502.5000', 'mfg_waste_percent' => '0.50', 'quantity_returned' => '0.0000', 'unit_price_before_discount' => '20.00', 'unit_price' => '20.00', 'line_discount_type' => null, 'line_discount_amount' => '0.00', 'unit_price_inc_tax' => '20.00', 'item_tax' => '0.00', 'tax_id' => null, 'discount_id' => null, 'lot_no_line_id' => null, 'sell_line_note' => '', 'res_service_staff_id' => null, 'res_line_order_status' => null, 'woocommerce_line_items_id' => null, 'parent_sell_line_id' => null, 'children_type' => '', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '33', 'transaction_id' => '53', 'product_id' => '84', 'variation_id' => '132', 'quantity' => '50.0000', 'mfg_waste_percent' => '0.00', 'quantity_returned' => '0.0000', 'unit_price_before_discount' => '20.00', 'unit_price' => '20.00', 'line_discount_type' => null, 'line_discount_amount' => '0.00', 'unit_price_inc_tax' => '20.00', 'item_tax' => '0.00', 'tax_id' => null, 'discount_id' => null, 'lot_no_line_id' => null, 'sell_line_note' => '', 'res_service_staff_id' => null, 'res_line_order_status' => null, 'woocommerce_line_items_id' => null, 'parent_sell_line_id' => null, 'children_type' => '', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '34', 'transaction_id' => '53', 'product_id' => '85', 'variation_id' => '133', 'quantity' => '100.0000', 'mfg_waste_percent' => '0.00', 'quantity_returned' => '0.0000', 'unit_price_before_discount' => '2.00', 'unit_price' => '2.00', 'line_discount_type' => null, 'line_discount_amount' => '0.00', 'unit_price_inc_tax' => '2.00', 'item_tax' => '0.00', 'tax_id' => null, 'discount_id' => null, 'lot_no_line_id' => null, 'sell_line_note' => '', 'res_service_staff_id' => null, 'res_line_order_status' => null, 'woocommerce_line_items_id' => null, 'parent_sell_line_id' => null, 'children_type' => '', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '35', 'transaction_id' => '53', 'product_id' => '87', 'variation_id' => '135', 'quantity' => '500.0000', 'mfg_waste_percent' => '0.00', 'quantity_returned' => '0.0000', 'unit_price_before_discount' => '2.00', 'unit_price' => '2.00', 'line_discount_type' => null, 'line_discount_amount' => '0.00', 'unit_price_inc_tax' => '2.00', 'item_tax' => '0.00', 'tax_id' => null, 'discount_id' => null, 'lot_no_line_id' => null, 'sell_line_note' => '', 'res_service_staff_id' => null, 'res_line_order_status' => null, 'woocommerce_line_items_id' => null, 'parent_sell_line_id' => null, 'children_type' => '', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '36', 'transaction_id' => '53', 'product_id' => '88', 'variation_id' => '136', 'quantity' => '100.0000', 'mfg_waste_percent' => '0.00', 'quantity_returned' => '0.0000', 'unit_price_before_discount' => '10.00', 'unit_price' => '10.00', 'line_discount_type' => null, 'line_discount_amount' => '0.00', 'unit_price_inc_tax' => '10.00', 'item_tax' => '0.00', 'tax_id' => null, 'discount_id' => null, 'lot_no_line_id' => null, 'sell_line_note' => '', 'res_service_staff_id' => null, 'res_line_order_status' => null, 'woocommerce_line_items_id' => null, 'parent_sell_line_id' => null, 'children_type' => '', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
        ];

        DB::table('transaction_sell_lines')->insert($mfg_sell_lines);

        $variation_location_details = [
            ['id' => '1', 'product_id' => '2', 'product_variation_id' => '2', 'variation_id' => '2', 'location_id' => '1', 'qty_available' => '50.00', 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-01-06 07:07:23'],
            ['id' => '2', 'product_id' => '2', 'product_variation_id' => '2', 'variation_id' => '3', 'location_id' => '1', 'qty_available' => '90.00', 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-01-06 07:07:23'],
            ['id' => '3', 'product_id' => '2', 'product_variation_id' => '2', 'variation_id' => '4', 'location_id' => '1', 'qty_available' => '150.00', 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-01-06 06:57:11'],
            ['id' => '4', 'product_id' => '2', 'product_variation_id' => '2', 'variation_id' => '5', 'location_id' => '1', 'qty_available' => '150.00', 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-01-06 06:57:11'],
            ['id' => '5', 'product_id' => '2', 'product_variation_id' => '2', 'variation_id' => '6', 'location_id' => '1', 'qty_available' => '100.00', 'created_at' => '2018-01-06 06:57:11', 'updated_at' => '2018-01-06 06:57:11'],
            ['id' => '6', 'product_id' => '14', 'product_variation_id' => '15', 'variation_id' => '47', 'location_id' => '1', 'qty_available' => '100.00', 'created_at' => '2018-01-06 06:58:10', 'updated_at' => '2018-01-06 06:58:10'],
            ['id' => '7', 'product_id' => '28', 'product_variation_id' => '30', 'variation_id' => '71', 'location_id' => '1', 'qty_available' => '470.00', 'created_at' => '2018-01-06 07:02:22', 'updated_at' => '2018-01-06 07:08:03'],
            ['id' => '8', 'product_id' => '21', 'product_variation_id' => '23', 'variation_id' => '64', 'location_id' => '1', 'qty_available' => '140.00', 'created_at' => '2018-01-06 07:03:12', 'updated_at' => '2018-01-06 07:07:45'],
            ['id' => '9', 'product_id' => '27', 'product_variation_id' => '29', 'variation_id' => '70', 'location_id' => '1', 'qty_available' => '470.00', 'created_at' => '2018-01-06 07:05:26', 'updated_at' => '2018-01-06 07:06:32'],
            ['id' => '10', 'product_id' => '34', 'product_variation_id' => '36', 'variation_id' => '77', 'location_id' => '2', 'qty_available' => '40.00', 'created_at' => '2018-04-10 10:18:16', 'updated_at' => '2018-04-10 10:23:35'],
            ['id' => '11', 'product_id' => '32', 'product_variation_id' => '34', 'variation_id' => '75', 'location_id' => '2', 'qty_available' => '93.00', 'created_at' => '2018-04-10 10:19:40', 'updated_at' => '2018-04-10 10:23:58'],
            ['id' => '12', 'product_id' => '36', 'product_variation_id' => '38', 'variation_id' => '79', 'location_id' => '2', 'qty_available' => '150.00', 'created_at' => '2018-04-10 10:20:41', 'updated_at' => '2018-04-10 10:20:41'],
            ['id' => '13', 'product_id' => '33', 'product_variation_id' => '35', 'variation_id' => '76', 'location_id' => '2', 'qty_available' => '153.00', 'created_at' => '2018-04-10 10:21:38', 'updated_at' => '2018-04-10 10:24:21'],
            ['id' => '14', 'product_id' => '46', 'product_variation_id' => '48', 'variation_id' => '89', 'location_id' => '3', 'qty_available' => '30.00', 'created_at' => '2018-04-10 11:39:54', 'updated_at' => '2018-04-10 11:39:54'],
            ['id' => '15', 'product_id' => '41', 'product_variation_id' => '43', 'variation_id' => '84', 'location_id' => '3', 'qty_available' => '14.00', 'created_at' => '2018-04-10 11:40:51', 'updated_at' => '2018-04-10 11:48:48'],
            ['id' => '16', 'product_id' => '45', 'product_variation_id' => '47', 'variation_id' => '88', 'location_id' => '3', 'qty_available' => '45.00', 'created_at' => '2018-04-10 11:41:45', 'updated_at' => '2018-04-10 11:41:45'],
            ['id' => '17', 'product_id' => '44', 'product_variation_id' => '46', 'variation_id' => '87', 'location_id' => '3', 'qty_available' => '80.00', 'created_at' => '2018-04-10 11:42:34', 'updated_at' => '2018-04-10 11:45:06'],
            ['id' => '18', 'product_id' => '43', 'product_variation_id' => '45', 'variation_id' => '86', 'location_id' => '3', 'qty_available' => '25.00', 'created_at' => '2018-04-10 11:43:12', 'updated_at' => '2018-04-10 11:44:47'],
            ['id' => '19', 'product_id' => '42', 'product_variation_id' => '44', 'variation_id' => '85', 'location_id' => '3', 'qty_available' => '30.00', 'created_at' => '2018-04-10 11:53:36', 'updated_at' => '2018-04-10 11:54:16'],
            ['id' => '20', 'product_id' => '61', 'product_variation_id' => '63', 'variation_id' => '104', 'location_id' => '1', 'qty_available' => '20.00', 'created_at' => '2018-06-11 22:17:07', 'updated_at' => '2018-06-11 22:17:07'],
            ['id' => '21', 'product_id' => '64', 'product_variation_id' => '66', 'variation_id' => '107', 'location_id' => '1', 'qty_available' => '300.00', 'created_at' => '2018-06-11 22:17:07', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '22', 'product_id' => '63', 'product_variation_id' => '65', 'variation_id' => '106', 'location_id' => '1', 'qty_available' => '300.00', 'created_at' => '2018-06-11 22:17:07', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '23', 'product_id' => '65', 'product_variation_id' => '67', 'variation_id' => '108', 'location_id' => '1', 'qty_available' => '50.00', 'created_at' => '2018-06-11 22:17:07', 'updated_at' => '2018-06-11 22:17:07'],
            ['id' => '24', 'product_id' => '17', 'product_variation_id' => '19', 'variation_id' => '57', 'location_id' => '1', 'qty_available' => '30.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '25', 'product_id' => '17', 'product_variation_id' => '19', 'variation_id' => '58', 'location_id' => '1', 'qty_available' => '50.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '26', 'product_id' => '15', 'product_variation_id' => '16', 'variation_id' => '49', 'location_id' => '1', 'qty_available' => '30.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '27', 'product_id' => '15', 'product_variation_id' => '16', 'variation_id' => '50', 'location_id' => '1', 'qty_available' => '20.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '28', 'product_id' => '15', 'product_variation_id' => '16', 'variation_id' => '51', 'location_id' => '1', 'qty_available' => '30.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '29', 'product_id' => '15', 'product_variation_id' => '17', 'variation_id' => '52', 'location_id' => '1', 'qty_available' => '20.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '30', 'product_id' => '15', 'product_variation_id' => '17', 'variation_id' => '53', 'location_id' => '1', 'qty_available' => '40.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '31', 'product_id' => '18', 'product_variation_id' => '20', 'variation_id' => '59', 'location_id' => '1', 'qty_available' => '20.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '32', 'product_id' => '18', 'product_variation_id' => '20', 'variation_id' => '60', 'location_id' => '1', 'qty_available' => '20.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '33', 'product_id' => '19', 'product_variation_id' => '21', 'variation_id' => '61', 'location_id' => '1', 'qty_available' => '30.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '34', 'product_id' => '22', 'product_variation_id' => '24', 'variation_id' => '65', 'location_id' => '1', 'qty_available' => '10.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '35', 'product_id' => '24', 'product_variation_id' => '26', 'variation_id' => '67', 'location_id' => '1', 'qty_available' => '10.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '36', 'product_id' => '25', 'product_variation_id' => '27', 'variation_id' => '68', 'location_id' => '1', 'qty_available' => '20.00', 'created_at' => '2018-06-12 23:18:04', 'updated_at' => '2018-06-12 23:18:04'],
            ['id' => '37', 'product_id' => '77', 'product_variation_id' => '79', 'variation_id' => '120', 'location_id' => '5', 'qty_available' => '100.00', 'created_at' => '2018-07-13 07:34:45', 'updated_at' => '2018-07-13 07:34:45'],
            ['id' => '38', 'product_id' => '76', 'product_variation_id' => '78', 'variation_id' => '119', 'location_id' => '5', 'qty_available' => '300.00', 'created_at' => '2018-07-13 07:34:45', 'updated_at' => '2018-07-13 07:38:24'],
            ['id' => '39', 'product_id' => '88', 'product_variation_id' => '90', 'variation_id' => '136', 'location_id' => '6', 'qty_available' => '1900.0000', 'created_at' => '2019-08-18 19:01:33', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '40', 'product_id' => '87', 'product_variation_id' => '89', 'variation_id' => '135', 'location_id' => '6', 'qty_available' => '1500.0000', 'created_at' => '2019-08-18 19:01:49', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '41', 'product_id' => '86', 'product_variation_id' => '88', 'variation_id' => '134', 'location_id' => '6', 'qty_available' => '1000.0000', 'created_at' => '2019-08-18 19:02:07', 'updated_at' => '2019-08-18 19:02:07'],
            ['id' => '42', 'product_id' => '84', 'product_variation_id' => '86', 'variation_id' => '132', 'location_id' => '6', 'qty_available' => '1950.0000', 'created_at' => '2019-08-18 19:02:20', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '43', 'product_id' => '82', 'product_variation_id' => '84', 'variation_id' => '130', 'location_id' => '6', 'qty_available' => '995.0000', 'created_at' => '2019-08-18 19:02:33', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '44', 'product_id' => '85', 'product_variation_id' => '87', 'variation_id' => '133', 'location_id' => '6', 'qty_available' => '0.0000', 'created_at' => '2019-08-18 19:02:52', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '45', 'product_id' => '83', 'product_variation_id' => '85', 'variation_id' => '131', 'location_id' => '6', 'qty_available' => '1497.5000', 'created_at' => '2019-08-18 19:03:10', 'updated_at' => '2019-08-18 19:09:20'],
            ['id' => '46', 'product_id' => '81', 'product_variation_id' => '83', 'variation_id' => '129', 'location_id' => '6', 'qty_available' => '5.0000', 'created_at' => '2019-08-18 19:09:19', 'updated_at' => '2019-08-18 19:09:19'],
            ['id' => '47', 'product_id' => '94', 'product_variation_id' => '96', 'variation_id' => '142', 'location_id' => '1', 'qty_available' => '8.0000', 'created_at' => '2020-05-07 21:18:22', 'updated_at' => '2020-05-07 21:18:22'],
            ['id' => '48', 'product_id' => '95', 'product_variation_id' => '97', 'variation_id' => '143', 'location_id' => '1', 'qty_available' => '7.0000', 'created_at' => '2020-05-07 21:19:10', 'updated_at' => '2020-05-09 10:28:58'],
        ];
        DB::table('variation_location_details')->insert($variation_location_details);

        $system = [
            ['key' => 'default_business_active_status', 'value' => '1'],
            ['key' => 'superadmin_version', 'value' => config('superadmin.module_version')],
            ['key' => 'app_currency_id', 'value' => '2'],
            ['key' => 'invoice_business_name', 'value' => 'Ultimate POS'],
            ['key' => 'invoice_business_landmark', 'value' => 'Linking Street'],
            ['key' => 'invoice_business_zip', 'value' => '85001'],
            ['key' => 'invoice_business_state', 'value' => 'Arizona'],
            ['key' => 'invoice_business_city', 'value' => 'Phoenix'],
            ['key' => 'invoice_business_country', 'value' => 'USA'],
            ['key' => 'email', 'value' => '<EMAIL>'],
            ['key' => 'enable_offline_payment', 'value' => '1'],
        ];
        foreach ($system as $row) {
            DB::table('system')->where('key', $row['key'])->update(['value' => $row['value']]);
        }

        $packages = [
            ['id' => '1', 'name' => 'Starter - Free', 'description' => 'Give it a test drive...', 'location_count' => '1', 'user_count' => '2', 'product_count' => '30', 'bookings' => '0', 'kitchen' => '0', 'order_screen' => '0', 'tables' => '0', 'invoice_count' => '30', 'interval' => 'months', 'interval_count' => '1', 'trial_days' => '10', 'price' => '0.0000', 'created_by' => '1', 'sort_order' => '0', 'is_active' => '1', 'deleted_at' => null, 'created_at' => $today, 'updated_at' => '2018-08-01 20:10:49', 'custom_permissions' => '{"essentials_module":"1","woocommerce_module":"1"}'],
            ['id' => '2', 'name' => 'Regular', 'description' => 'For Small Shops', 'location_count' => '0', 'user_count' => '0', 'product_count' => '0', 'bookings' => '0', 'kitchen' => '0', 'order_screen' => '0', 'tables' => '0', 'invoice_count' => '0', 'interval' => 'months', 'interval_count' => '1', 'trial_days' => '10', 'price' => '199.9900', 'custom_permissions' => '{"repair_module":"1"}', 'created_by' => '1', 'sort_order' => '1', 'is_active' => '1', 'deleted_at' => null, 'created_at' => $today, 'updated_at' => $today],

            ['id' => '3', 'name' => 'Unlimited', 'description' => 'For Large Business', 'location_count' => '0', 'user_count' => '0', 'product_count' => '0', 'bookings' => '0', 'kitchen' => '0', 'order_screen' => '0', 'tables' => '0', 'invoice_count' => '0', 'interval' => 'months', 'interval_count' => '1', 'trial_days' => '10', 'price' => '599.9900', 'created_by' => '1', 'sort_order' => '1', 'is_active' => '1', 'deleted_at' => null, 'created_at' => $today, 'updated_at' => '2018-08-01 20:13:50', 'custom_permissions' => ''],

            ['id' => '4', 'name' => 'Business', 'description' => 'For Small & Growing Shops...', 'location_count' => '10', 'user_count' => '10', 'product_count' => '15000', 'bookings' => '0', 'kitchen' => '0', 'order_screen' => '0', 'tables' => '0', 'invoice_count' => '1000', 'interval' => 'months', 'interval_count' => '1', 'trial_days' => '10', 'price' => '259.9900', 'created_by' => '1', 'sort_order' => '5', 'is_active' => '0', 'deleted_at' => null, 'created_at' => $today, 'updated_at' => '2018-08-01 20:16:14', 'custom_permissions' => ''],
        ];
        DB::table('packages')->insert($packages);

        $subscription_start = \Carbon::today()->subDay(2)->toDateString();
        $subscription_trial = \Carbon::today()->addDays(8)->toDateString();
        $subscription_end = \Carbon::today()->addDays(28)->toDateString();

        $subscriptions = [
            ['id' => '1', 'business_id' => '1', 'package_id' => '3', 'start_date' => $subscription_start, 'trial_end_date' => $subscription_trial, 'end_date' => $subscription_end, 'package_price' => '599.99', 'package_details' => '{"location_count":0,"user_count":0,"product_count":0,"invoice_count":0,"name":"Unlimited","woocommerce_module":1, "essentials_module":1}', 'created_id' => '1', 'paid_via' => 'stripe', 'payment_transaction_id' => 'ch_1CuLdQAhokBpT93LVZNg2At6', 'status' => 'approved', 'deleted_at' => null, 'created_at' => '2018-08-01 07:49:09', 'updated_at' => '2018-08-01 07:49:09'],
            ['id' => '2', 'business_id' => '2', 'package_id' => '3', 'start_date' => $subscription_start, 'trial_end_date' => $subscription_trial, 'end_date' => $subscription_end, 'package_price' => '599.99', 'package_details' => '{"location_count":0,"user_count":0,"product_count":0,"invoice_count":0,"name":"Unlimited"}', 'created_id' => '4', 'paid_via' => 'stripe', 'payment_transaction_id' => 'ch_1CuLggAhokBpT93LbaE29pMW', 'status' => 'approved', 'deleted_at' => null, 'created_at' => '2018-08-01 09:52:31', 'updated_at' => '2018-08-01 09:52:31'],
            ['id' => '3', 'business_id' => '4', 'package_id' => '2', 'start_date' => $subscription_start, 'trial_end_date' => $subscription_trial, 'end_date' => $subscription_end, 'package_price' => '199.9900', 'package_details' => '{"location_count":0,"user_count":0,"product_count":0,"invoice_count":0,"name":"Regular","repair_module":"1"}', 'created_id' => '6', 'paid_via' => 'stripe', 'payment_transaction_id' => 'ch_1CuLkoAhokBpT93LW0UAFC7N', 'status' => 'approved', 'deleted_at' => null, 'created_at' => '2018-08-01 09:56:48', 'updated_at' => '2018-08-01 09:56:48'],
            ['id' => '4', 'business_id' => '3', 'package_id' => '3', 'start_date' => $subscription_start, 'trial_end_date' => $subscription_trial, 'end_date' => $subscription_end, 'package_price' => '599.99', 'package_details' => '{"location_count":0,"user_count":0,"product_count":0,"invoice_count":0,"name":"Unlimited"}', 'created_id' => '5', 'paid_via' => 'stripe', 'payment_transaction_id' => 'ch_1CuLljAhokBpT93LGozt93Wn', 'status' => 'approved', 'deleted_at' => null, 'created_at' => '2018-08-01 09:57:44', 'updated_at' => '2018-08-01 09:57:44'],
            ['id' => '5', 'business_id' => '5', 'package_id' => '3', 'start_date' => $subscription_start, 'trial_end_date' => $subscription_trial, 'end_date' => $subscription_end, 'package_price' => '599.99', 'package_details' => '{"location_count":0,"user_count":0,"product_count":0,"invoice_count":0,"name":"Unlimited"}', 'created_id' => '7', 'paid_via' => 'stripe', 'payment_transaction_id' => 'ch_1CuLmXAhokBpT93LOUCvEIKD', 'status' => 'approved', 'deleted_at' => null, 'created_at' => '2018-08-01 09:58:34', 'updated_at' => '2018-08-01 09:58:34'],
            ['id' => '6', 'business_id' => '6', 'package_id' => '5', 'start_date' => $subscription_start, 'trial_end_date' => $subscription_trial, 'end_date' => $subscription_end, 'package_price' => '200.00', 'package_details' => '{"location_count":0,"user_count":0,"product_count":0,"invoice_count":0,"name":"Manufacturing Module package","manufacturing_module":"1"}', 'created_id' => '1', 'paid_via' => 'offline', 'payment_transaction_id' => '121333434', 'status' => 'approved', 'deleted_at' => null, 'created_at' => '2019-08-18 18:41:56', 'updated_at' => '2019-08-18 18:41:56'],
        ];

        DB::table('subscriptions')->insert($subscriptions);

        $notification_template_data = NotificationTemplate::defaultNotificationTemplates();
        $notification_template_array = [];
        for ($i = 1; $i < 6; $i++) {
            foreach ($notification_template_data as $notification_template) {
                $notification_template['business_id'] = $i;
                $notification_template_array[] = $notification_template;
            }
        }

        DB::table('notification_templates')->insert($notification_template_array);

        $mfg_recipes = [
            ['id' => '1', 'product_id' => '81', 'variation_id' => '129', 'instructions' => '<p>Steps for making pizza can be written here ...</p>', 'waste_percent' => '0.00', 'ingredients_cost' => '2660.0000', 'extra_cost' => '10.0000', 'total_quantity' => '1.0000', 'final_price' => '2926.0000', 'sub_unit_id' => '9', 'created_at' => '2019-08-18 19:05:09', 'updated_at' => '2019-08-18 19:08:12'],
            ['id' => '2', 'product_id' => '93', 'variation_id' => '141', 'instructions' => null, 'waste_percent' => '0.00', 'ingredients_cost' => '31.5000', 'extra_cost' => '10.0000', 'total_quantity' => '1.0000', 'final_price' => '34.6500', 'sub_unit_id' => '9', 'created_at' => '2019-08-18 19:22:40', 'updated_at' => '2019-08-18 19:22:40'],
        ];
        DB::table('mfg_recipes')->insert($mfg_recipes);

        $mfg_recipe_ingredients = [
            ['id' => '1', 'mfg_recipe_id' => '1', 'variation_id' => '130', 'quantity' => '1.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:05:09', 'updated_at' => '2019-08-18 19:08:12'],
            ['id' => '2', 'mfg_recipe_id' => '1', 'variation_id' => '136', 'quantity' => '20.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:05:09', 'updated_at' => '2019-08-18 19:08:12'],
            ['id' => '3', 'mfg_recipe_id' => '1', 'variation_id' => '135', 'quantity' => '100.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:05:09', 'updated_at' => '2019-08-18 19:08:12'],
            ['id' => '4', 'mfg_recipe_id' => '1', 'variation_id' => '132', 'quantity' => '10.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:05:09', 'updated_at' => '2019-08-18 19:08:12'],
            ['id' => '5', 'mfg_recipe_id' => '1', 'variation_id' => '133', 'quantity' => '20.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:08:13', 'updated_at' => '2019-08-18 19:08:13'],
            ['id' => '6', 'mfg_recipe_id' => '1', 'variation_id' => '131', 'quantity' => '100.0000', 'waste_percent' => '0.50', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:08:13', 'updated_at' => '2019-08-18 19:08:13'],
            ['id' => '7', 'mfg_recipe_id' => '2', 'variation_id' => '139', 'quantity' => '6.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:22:41', 'updated_at' => '2019-08-18 19:22:41'],
            ['id' => '8', 'mfg_recipe_id' => '2', 'variation_id' => '138', 'quantity' => '0.5000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:22:41', 'updated_at' => '2019-08-18 19:22:41'],
            ['id' => '9', 'mfg_recipe_id' => '2', 'variation_id' => '140', 'quantity' => '1.0000', 'waste_percent' => '0.00', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:22:41', 'updated_at' => '2019-08-18 19:22:41'],
            ['id' => '10', 'mfg_recipe_id' => '2', 'variation_id' => '137', 'quantity' => '2.5000', 'waste_percent' => '0.50', 'sub_unit_id' => null, 'created_at' => '2019-08-18 19:22:41', 'updated_at' => '2019-08-18 19:22:41'],
        ];

        DB::table('mfg_recipe_ingredients')->insert($mfg_recipe_ingredients);

        $product_locations = [
            ['product_id' => '1', 'location_id' => '1'],
            ['product_id' => '2', 'location_id' => '1'],
            ['product_id' => '3', 'location_id' => '1'],
            ['product_id' => '4', 'location_id' => '1'],
            ['product_id' => '8', 'location_id' => '1'],
            ['product_id' => '9', 'location_id' => '1'],
            ['product_id' => '10', 'location_id' => '1'],
            ['product_id' => '11', 'location_id' => '1'],
            ['product_id' => '12', 'location_id' => '1'],
            ['product_id' => '13', 'location_id' => '1'],
            ['product_id' => '14', 'location_id' => '1'],
            ['product_id' => '15', 'location_id' => '1'],
            ['product_id' => '16', 'location_id' => '1'],
            ['product_id' => '17', 'location_id' => '1'],
            ['product_id' => '18', 'location_id' => '1'],
            ['product_id' => '19', 'location_id' => '1'],
            ['product_id' => '20', 'location_id' => '1'],
            ['product_id' => '21', 'location_id' => '1'],
            ['product_id' => '22', 'location_id' => '1'],
            ['product_id' => '23', 'location_id' => '1'],
            ['product_id' => '24', 'location_id' => '1'],
            ['product_id' => '25', 'location_id' => '1'],
            ['product_id' => '26', 'location_id' => '1'],
            ['product_id' => '27', 'location_id' => '1'],
            ['product_id' => '28', 'location_id' => '1'],
            ['product_id' => '29', 'location_id' => '1'],
            ['product_id' => '30', 'location_id' => '1'],
            ['product_id' => '61', 'location_id' => '1'],
            ['product_id' => '62', 'location_id' => '1'],
            ['product_id' => '63', 'location_id' => '1'],
            ['product_id' => '64', 'location_id' => '1'],
            ['product_id' => '65', 'location_id' => '1'],
            ['product_id' => '41', 'location_id' => '3'],
            ['product_id' => '42', 'location_id' => '3'],
            ['product_id' => '43', 'location_id' => '3'],
            ['product_id' => '44', 'location_id' => '3'],
            ['product_id' => '45', 'location_id' => '3'],
            ['product_id' => '46', 'location_id' => '3'],
            ['product_id' => '47', 'location_id' => '3'],
            ['product_id' => '48', 'location_id' => '3'],
            ['product_id' => '49', 'location_id' => '3'],
            ['product_id' => '50', 'location_id' => '3'],
            ['product_id' => '31', 'location_id' => '2'],
            ['product_id' => '32', 'location_id' => '2'],
            ['product_id' => '33', 'location_id' => '2'],
            ['product_id' => '34', 'location_id' => '2'],
            ['product_id' => '35', 'location_id' => '2'],
            ['product_id' => '36', 'location_id' => '2'],
            ['product_id' => '37', 'location_id' => '2'],
            ['product_id' => '38', 'location_id' => '2'],
            ['product_id' => '39', 'location_id' => '2'],
            ['product_id' => '40', 'location_id' => '2'],
            ['product_id' => '51', 'location_id' => '4'],
            ['product_id' => '52', 'location_id' => '4'],
            ['product_id' => '53', 'location_id' => '4'],
            ['product_id' => '54', 'location_id' => '4'],
            ['product_id' => '55', 'location_id' => '4'],
            ['product_id' => '56', 'location_id' => '4'],
            ['product_id' => '57', 'location_id' => '4'],
            ['product_id' => '58', 'location_id' => '4'],
            ['product_id' => '59', 'location_id' => '4'],
            ['product_id' => '60', 'location_id' => '4'],
            ['product_id' => '81', 'location_id' => '6'],
            ['product_id' => '82', 'location_id' => '6'],
            ['product_id' => '83', 'location_id' => '6'],
            ['product_id' => '84', 'location_id' => '6'],
            ['product_id' => '85', 'location_id' => '6'],
            ['product_id' => '86', 'location_id' => '6'],
            ['product_id' => '87', 'location_id' => '6'],
            ['product_id' => '88', 'location_id' => '6'],
            ['product_id' => '89', 'location_id' => '6'],
            ['product_id' => '90', 'location_id' => '6'],
            ['product_id' => '91', 'location_id' => '6'],
            ['product_id' => '92', 'location_id' => '6'],
            ['product_id' => '93', 'location_id' => '6'],
            ['product_id' => '74', 'location_id' => '5'],
            ['product_id' => '77', 'location_id' => '5'],
            ['product_id' => '72', 'location_id' => '5'],
            ['product_id' => '69', 'location_id' => '5'],
            ['product_id' => '75', 'location_id' => '5'],
            ['product_id' => '70', 'location_id' => '5'],
            ['product_id' => '66', 'location_id' => '5'],
            ['product_id' => '71', 'location_id' => '5'],
            ['product_id' => '67', 'location_id' => '5'],
            ['product_id' => '68', 'location_id' => '5'],
            ['product_id' => '76', 'location_id' => '5'],
            ['product_id' => '73', 'location_id' => '5'],
            ['product_id' => '154', 'location_id' => '4'],
            ['product_id' => '95', 'location_id' => '4'],
            ['product_id' => '99', 'location_id' => '4'],
            ['product_id' => '100', 'location_id' => '4'],
            ['product_id' => '101', 'location_id' => '4'],
            ['product_id' => '102', 'location_id' => '4'],
            ['product_id' => '103', 'location_id' => '4'],
            ['product_id' => '104', 'location_id' => '4'],
            ['product_id' => '105', 'location_id' => '4'],
            ['product_id' => '106', 'location_id' => '4'],
            ['product_id' => '107', 'location_id' => '4'],
            ['product_id' => '108', 'location_id' => '4'],
            ['product_id' => '109', 'location_id' => '4'],
            ['product_id' => '110', 'location_id' => '4'],
            ['product_id' => '111', 'location_id' => '4'],
            ['product_id' => '112', 'location_id' => '4'],
            ['product_id' => '113', 'location_id' => '4'],
            ['product_id' => '114', 'location_id' => '4'],
            ['product_id' => '115', 'location_id' => '4'],
            ['product_id' => '116', 'location_id' => '4'],
            ['product_id' => '117', 'location_id' => '4'],
            ['product_id' => '118', 'location_id' => '4'],
            ['product_id' => '119', 'location_id' => '4'],
            ['product_id' => '120', 'location_id' => '4'],
            ['product_id' => '121', 'location_id' => '4'],
            ['product_id' => '122', 'location_id' => '4'],
            ['product_id' => '123', 'location_id' => '4'],
            ['product_id' => '124', 'location_id' => '4'],
            ['product_id' => '125', 'location_id' => '4'],
            ['product_id' => '126', 'location_id' => '4'],
            ['product_id' => '127', 'location_id' => '4'],
            ['product_id' => '128', 'location_id' => '4'],
            ['product_id' => '129', 'location_id' => '4'],
            ['product_id' => '130', 'location_id' => '4'],
            ['product_id' => '131', 'location_id' => '4'],
            ['product_id' => '132', 'location_id' => '4'],
            ['product_id' => '133', 'location_id' => '4'],
            ['product_id' => '134', 'location_id' => '4'],
            ['product_id' => '135', 'location_id' => '4'],
            ['product_id' => '136', 'location_id' => '4'],
            ['product_id' => '137', 'location_id' => '4'],
            ['product_id' => '138', 'location_id' => '4'],
            ['product_id' => '139', 'location_id' => '4'],
            ['product_id' => '140', 'location_id' => '4'],
            ['product_id' => '141', 'location_id' => '4'],
            ['product_id' => '142', 'location_id' => '4'],
            ['product_id' => '143', 'location_id' => '4'],
            ['product_id' => '144', 'location_id' => '4'],
            ['product_id' => '145', 'location_id' => '4'],
            ['product_id' => '146', 'location_id' => '4'],
            ['product_id' => '147', 'location_id' => '4'],
            ['product_id' => '148', 'location_id' => '4'],
            ['product_id' => '149', 'location_id' => '4'],
            ['product_id' => '150', 'location_id' => '4'],
            ['product_id' => '151', 'location_id' => '4'],
            ['product_id' => '152', 'location_id' => '4'],
            ['product_id' => '153', 'location_id' => '4'],
        ];

        DB::table('product_locations')->insert($product_locations);

        //Roles and permissions for business 1
        $admin_role1 = Role::create(['name' => 'Admin#1',
            'business_id' => 1,
            'guard_name' => 'web',
            'is_default' => 1,
        ]);
        $cashier_role1 = Role::create(['name' => 'Cashier#1',
            'business_id' => 1,
            'guard_name' => 'web',
        ]);

        $cashier_role1->syncPermissions(['sell.view', 'sell.create', 'sell.update', 'sell.delete', 'view_cash_register', 'close_cash_register', 'print_invoice']);

        $admin1 = User::findOrFail(1);
        $admin_essentials = User::findOrFail(11);
        $superadmin1 = User::findOrFail(9);
        $woocommerce1 = User::findOrFail(10);
        $cashier1 = User::findOrFail(2);
        $demo_user1 = User::findOrFail(3);

        $admin1->assignRole('Admin#1');
        $admin_essentials->assignRole('Admin#1');
        $superadmin1->assignRole('Admin#1');
        $cashier1->assignRole('Cashier#1');
        $demo_user1->assignRole('Admin#1');
        $woocommerce1->assignRole('Admin#1');
        Permission::create(['name' => 'location.1']);

        //give location.1 permissions
        $cashier1->givePermissionTo('location.1');

        //Roles and permissions for business 2
        $admin_role2 = Role::create(['name' => 'Admin#2',
            'business_id' => 2,
            'guard_name' => 'web',
            'is_default' => 1,
        ]);
        $cashier_role2 = Role::create(['name' => 'Cashier#2',
            'business_id' => 2,
            'guard_name' => 'web',
        ]);

        $cashier_role2->syncPermissions(['sell.view', 'sell.create', 'sell.update', 'sell.delete', 'view_cash_register', 'close_cash_register', 'print_invoice']);

        $admin2 = User::findOrFail(4);

        $admin2->assignRole('Admin#2');
        Permission::create(['name' => 'location.2']);

        //Roles and permissions for business 3
        $admin_role3 = Role::create(['name' => 'Admin#3',
            'business_id' => 3,
            'guard_name' => 'web',
        ]);
        $cashier_role3 = Role::create(['name' => 'Cashier#3',
            'business_id' => 3,
            'guard_name' => 'web',
        ]);

        $cashier_role3->syncPermissions(['sell.view', 'sell.create', 'sell.update', 'sell.delete', 'view_cash_register', 'close_cash_register', 'print_invoice']);

        $admin3 = User::findOrFail(5);

        $admin3->assignRole('Admin#3');
        Permission::create(['name' => 'location.3']);

        //Roles and permissions for business 4
        $admin_role4 = Role::create(['name' => 'Admin#4',
            'business_id' => 4,
            'guard_name' => 'web',
            'is_default' => 1,
        ]);
        $cashier_role4 = Role::create(['name' => 'Cashier#4',
            'business_id' => 4,
            'guard_name' => 'web',
        ]);

        $cashier_role4->syncPermissions(['sell.view', 'sell.create', 'sell.update', 'sell.delete', 'view_cash_register', 'close_cash_register', 'print_invoice']);

        $admin4 = User::findOrFail(6);

        $admin4->assignRole('Admin#4');
        Permission::create(['name' => 'location.4']);

        //Roles and permissions for business 5
        $admin_role5 = Role::create(['name' => 'Admin#5',
            'business_id' => 5,
            'guard_name' => 'web',
            'is_default' => 1,
        ]);
        $cashier_role5 = Role::create(['name' => 'Cashier#5',
            'business_id' => 5,
            'guard_name' => 'web',
        ]);

        $cashier_role5->syncPermissions(['sell.view', 'sell.create', 'sell.update', 'sell.delete', 'view_cash_register', 'close_cash_register', 'print_invoice']);

        $admin5 = User::findOrFail(7);

        $admin5->assignRole('Admin#5');
        Permission::create(['name' => 'location.5']);

        $waiter_role5 = Role::create(['name' => 'Waiter#5',
            'business_id' => 5,
            'guard_name' => 'web',
            'is_service_staff' => 1,
        ]);
        $waiter_role5->syncPermissions(['dashboard.data']);
        $waiter5 = User::findOrFail(8);
        $waiter5->assignRole('Waiter#5');
        $waiter5->givePermissionTo('location.5');

        $admin_role6 = Role::create(['name' => 'Admin#6',
            'business_id' => 6,
            'guard_name' => 'web',
            'is_default' => 1,
        ]);
        $admin6 = User::findOrFail(12);
        $admin6->assignRole('Admin#6');

        //Essential Module : Dummy Data
        $essentials_leave_types = [
            ['id' => '1', 'leave_type' => 'Sick Leave', 'max_leave_count' => null, 'leave_count_interval' => null, 'business_id' => '1', 'created_at' => '2019-08-07 00:00:36', 'updated_at' => '2019-08-07 00:00:36'],
            ['id' => '2', 'leave_type' => 'Vacation Leaves', 'max_leave_count' => null, 'leave_count_interval' => null, 'business_id' => '1', 'created_at' => '2019-08-07 00:00:49', 'updated_at' => '2019-08-07 00:00:49'],
            ['id' => '3', 'leave_type' => 'Maternity leave', 'max_leave_count' => null, 'leave_count_interval' => null, 'business_id' => '1', 'created_at' => '2019-08-07 00:01:11', 'updated_at' => '2019-08-07 00:01:11'],
            ['id' => '4', 'leave_type' => 'Others', 'max_leave_count' => '1', 'leave_count_interval' => 'month', 'business_id' => '1', 'created_at' => '2019-08-07 00:01:34', 'updated_at' => '2019-08-07 00:01:34'],
        ];
        DB::table('essentials_leave_types')->insert($essentials_leave_types);

        //Repair Module Dummy Data [product/device(categories) added above]
        $repair_device_models = [
            ['id' => '1', 'business_id' => '4', 'name' => 'Samsung Galaxy M21', 'repair_checklist' => '"MIC|WiFi|Bluetooth|Sound|Camera|Ram"', 'brand_id' => '24', 'device_id' => '48', 'created_by' => '1', 'created_at' => '2020-05-07 21:07:24', 'updated_at' => '2020-05-11 12:35:00'],
            ['id' => '2', 'business_id' => '4', 'name' => 'Samsung Galaxy S20+', 'repair_checklist' => '"MIC|WiFi|Bluetooth|Sound|Camera|Ram"', 'brand_id' => '24', 'device_id' => '48', 'created_by' => '1', 'created_at' => '2020-05-07 21:08:11', 'updated_at' => '2020-05-11 12:34:34'],
            ['id' => '3', 'business_id' => '4', 'name' => 'DB43J 43 Inch Full HD Monitor', 'repair_checklist' => '"RAM|Wifi|keyBoard|Sound Quallity | Battery capacity | Mouse | USB Ports | Graphics"', 'brand_id' => '24', 'device_id' => '47', 'created_by' => '1', 'created_at' => '2020-05-07 21:09:15', 'updated_at' => '2020-05-11 12:33:47'],
            ['id' => '4', 'business_id' => '4', 'name' => 'LS24E510CS 23.5 inch LED Monitor', 'repair_checklist' => '"Keyboard|Ram|Sound Quallity | Battery capacity | Mouse | USB Ports | Graphics"', 'brand_id' => '24', 'device_id' => '47', 'created_by' => '1', 'created_at' => '2020-05-07 21:10:11', 'updated_at' => '2020-05-11 12:33:28'],
            ['id' => '5', 'business_id' => '4', 'name' => 'Samsung ChromeBook XE50', 'repair_checklist' => '"Camera | Windows | KeyBoard | Mouse | Battery capacity | Ram | USB Ports | Graphics"', 'brand_id' => '24', 'device_id' => '46', 'created_by' => '1', 'created_at' => '2020-05-07 21:11:44', 'updated_at' => '2020-05-11 12:30:48'],
            ['id' => '6', 'business_id' => '4', 'name' => 'Samsung Spin 7 NP730QAA', 'repair_checklist' => '"Battery capacity | Ram | Mouse | USB Ports | Graphics"', 'brand_id' => '24', 'device_id' => '46', 'created_by' => '1', 'created_at' => '2020-05-07 21:12:48', 'updated_at' => '2020-05-11 12:30:18'],
            ['id' => '7', 'business_id' => '4', 'name' => 'Apple MacBook Pro MVVK2LL', 'repair_checklist' => '"Camera | Windows | KeyBoard | Mouse | Battery capacity | Ram | USB Ports | Graphics"', 'brand_id' => '25', 'device_id' => '46', 'created_by' => '1', 'created_at' => '2020-05-07 21:13:54', 'updated_at' => '2020-05-11 12:37:44'],
            ['id' => '8', 'business_id' => '4', 'name' => 'iMac (Retina 5K, 27-inch, 2019)', 'repair_checklist' => '"Keyboard|Ram|Sound Quallity | Battery capacity | Mouse | USB Ports | Graphics"', 'brand_id' => '25', 'device_id' => '47', 'created_by' => '1', 'created_at' => '2020-05-07 21:14:39', 'updated_at' => '2020-05-11 12:38:06'],
            ['id' => '9', 'business_id' => '4', 'name' => 'Apple iPhone X', 'repair_checklist' => '"MIC|WiFi|Bluetooth|Sound|Camera|Ram"', 'brand_id' => '25', 'device_id' => '48', 'created_by' => '1', 'created_at' => '2020-05-07 21:15:38', 'updated_at' => '2020-05-11 12:38:48'],
        ];

        DB::table('repair_device_models')->insert($repair_device_models);

        $repair_statuses = [
            ['id' => '1', 'name' => 'Repaired', 'color' => '#3bd914', 'sort_order' => '4', 'business_id' => '4', 'created_at' => '2020-05-07 21:02:40', 'updated_at' => '2020-05-07 21:02:40'],
            ['id' => '2', 'name' => 'In Progress', 'color' => '#e3b81e', 'sort_order' => '2', 'business_id' => '4', 'created_at' => '2020-05-07 21:03:14', 'updated_at' => '2020-05-07 21:03:14'],
            ['id' => '3', 'name' => 'Not Started', 'color' => '#e3631b', 'sort_order' => '1', 'business_id' => '4', 'created_at' => '2020-05-07 21:03:43', 'updated_at' => '2020-05-07 21:03:43'],
            ['id' => '4', 'name' => 'Can\'t be repaired', 'color' => '#f5164b', 'sort_order' => '3', 'business_id' => '4', 'created_at' => '2020-05-07 21:04:10', 'updated_at' => '2020-05-08 10:04:58'],
        ];

        DB::table('repair_statuses')->insert($repair_statuses);

        $essentials_shifts = [
            ['id' => '1', 'name' => 'Morning Shift', 'type' => 'fixed_shift', 'business_id' => '1', 'start_time' => '09:00:00', 'end_time' => '18:00:00', 'holidays' => '["sunday","saturday"]', 'created_at' => '2020-05-11 05:26:56', 'updated_at' => '2020-05-11 21:27:03'],
            ['id' => '2', 'name' => 'Evening Shift', 'type' => 'flexible_shift', 'business_id' => '1', 'start_time' => null, 'end_time' => null, 'holidays' => '["sunday","saturday"]', 'created_at' => '2020-05-11 05:28:17', 'updated_at' => '2020-05-11 05:28:17'],
        ];

        DB::table('essentials_shifts')->insert($essentials_shifts);

        $essentials_user_shifts = [
            ['id' => '2', 'user_id' => '3', 'essentials_shift_id' => '1', 'start_date' => $start_of_week, 'end_date' => $end_of_week, 'created_at' => '2020-05-11 21:27:50', 'updated_at' => '2020-05-11 21:27:50'],
            ['id' => '3', 'user_id' => '9', 'essentials_shift_id' => '1', 'start_date' => $start_of_week, 'end_date' => $end_of_week, 'created_at' => '2020-05-11 21:27:50', 'updated_at' => '2020-05-11 21:27:50'],
            ['id' => '5', 'user_id' => '10', 'essentials_shift_id' => '2', 'start_date' => $start_of_week, 'end_date' => $end_of_week, 'created_at' => '2020-05-11 21:28:15', 'updated_at' => '2020-05-11 21:28:15'],
            ['id' => '6', 'user_id' => '11', 'essentials_shift_id' => '2', 'start_date' => $start_of_week, 'end_date' => $end_of_week, 'created_at' => '2020-05-11 21:28:15', 'updated_at' => '2020-05-11 21:28:15'],
            ['id' => '7', 'user_id' => '1', 'essentials_shift_id' => '2', 'start_date' => $start_of_week, 'end_date' => $end_of_week, 'created_at' => '2020-05-11 21:29:16', 'updated_at' => '2020-05-11 21:29:16'],
            ['id' => '8', 'user_id' => '2', 'essentials_shift_id' => '1', 'start_date' => $start_of_week, 'end_date' => $end_of_week, 'created_at' => '2020-05-11 21:29:45', 'updated_at' => '2020-05-11 21:29:45'],
        ];
        DB::table('essentials_user_shifts')->insert($essentials_user_shifts);

        DB::statement('SET FOREIGN_KEY_CHECKS = 1');

        $installUtil = new InstallUtil();
        $installUtil->createExistingProductsVariationsToTemplate();

        DB::commit();
    }
}
