<?php

return [
    'account' => 'Konto',
    'manage_your_account' => 'Verwalte Dein Konto',
    'all_accounts' => 'Alle Konten',
    'account_number' => 'Kontonummer',
    'add_account' => 'Konto hinzufügen',
    'account_created_success' => 'Konto erfolgreich erstellt',
    'account_updated_success' => 'Konto erfolgreich aktualisiert',
    'edit_account' => 'Konto bearbeiten',
    'account_closed_success' => 'Konto erfolgreich geschlossen',
    'closed' => 'Geschlossen',
    'list_accounts' => 'Konten auflisten',
    'opening_balance' => 'Anfangsbestand',
    'account_book' => 'Geschäftsbuch',
    'credit' => 'Kredit',
    'debit' => 'Lastschrift',
    'account_name' => 'Kontobezeichnung',
    'transaction_type' => 'Art der Transaktion',
    'fund_transfer' => 'Überweisung',
    'selected_account' => 'Ausgewähltes Konto',
    'transfer_to' => 'Übertragen nach',
    'fund_transfered_success' => 'Fonds erfolgreich transferiert',
    'deposit' => 'Anzahlung',
    'deposited_successfully' => 'Betrag erfolgreich hinterlegt',
    'from' => 'Von',
    'to' => 'Zu',
    'balance_sheet' => 'Bilanz',
    'liability' => 'Haftung',
    'assets' => 'Vermögenswerte',
    'supplier_due' => 'Lieferant fällig',
    'customer_due' => 'Kunde fällig',
    'account_balances' => 'Kontostände',
    'total_assets' => 'Gesamtvermögen',
    'total_liability' => 'Gesamthaftung',
    'trial_balance' => 'Test Balance',
    'payments_not_linked_with_account' => 'Summe <b>:payments </b>, die keinem Konto zugeordnet sind.',
    'saving_current' => 'Speichern / Aktuell',
    'capital' => 'Hauptstadt',
    'not_applicable' => 'Unzutreffend',
    'account_type' => 'Konto Typ',
    'accounts' => 'Konten',
    'capital_accounts' => 'Kapitalkonten',
    'deposit_from' => 'Einzahlung von',
    'no_capital_account_created' => 'Es ist kein aktives Kapitalkonto vorhanden.',
    'payment_account_report' => 'Zahlungskontobericht',
    'invoice_ref_no' => 'Rechnungsnr. / Nr.',
    'payment_ref_no' => 'Zahlungsreferenz-Nr.',
    'payment_for' => 'Zahlung für',
    'link_account' => 'Konto verbinden',
    'account_linked_success' => 'Konto erfolgreich verknüpft',
    'view_details' => 'Details anzeigen',
];
