<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Report Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used in reports
    |
    */

    'reports' => 'Reports',
    'purchase_sell_report' => 'Purchase & Sale',
    'purchase_sell' => 'Purchase & Sale Report',
    'purchase_sell_msg' => 'Purchase & sale details for the selected date range',
    'total_purchase' => 'Total Purchase',
    'purchase_inc_tax' => 'Purchase Including tax',
    'total_sell' => 'Total Sale',
    'sell_inc_tax' => 'Sale Including tax',
    'purchase_due' => 'Purchase Due',
    'sell_due' => 'Sale Due',
    'overall' => 'Overall (Sale-Purchase)',
    'sell_minus_purchase' => 'Sale - Purchase',
    'difference_due' => 'Due amount',
    'customer' => 'Customers',
    'supplier' => 'Suppliers',
    'total_due' => 'Due',
    'contacts' => 'Supplier & Customer Report',
    'contact' => 'Contact',
    'stock_report' => 'Stock Report',
    'products' => 'Products',
    'current_stock' => 'Current stock',
    'total_unit_sold' => 'Total unit sold',
    'tax_report' => 'Tax Report',
    'tax_report_msg' => 'Tax details for  the selected date range',
    'input_tax' => 'Input Tax',
    'output_tax' => 'Output Tax',
    'tax_overall' => 'Overall (Input - Output)',
    'trending_products' => 'Trending Products',
    'apply_filters' => 'Apply Filters',
    'filters' => 'Filters',
    'expense_report' => 'Expense Report',
    'total_expense' => 'Total Expense',
    'others' => 'Others',
    'all' => 'All',
    'top_trending_products' => 'Top Trending Products',
    'all_locations' => 'All locations',
    'stock_adjustment_report' => 'Stock Adjustment Report',
    'total_stock_adjustment' => 'Total Stock Adjustment',
    'total_recovered' => 'Total Amount Recovered',
    'total_normal' => 'Total Normal',
    'total_abnormal' => 'Total Abnormal',
    'register_report' => 'Register Report',
    'open_time' => 'Open Time',
    'close_time' => 'Close Time',
    'user' => 'User',
    'all_users' => 'All Users',
    'sales_representative' => 'Sales Representative Report',
    'sales_representative_expenses' => 'All expenses related to the sales representative',
    'summary' => 'Summary',
    'date_range' => 'Date Range',
    'stock_expiry_report' => 'Stock Expiry Report',
    'stock_left' => 'Stock Left',
    'expired' => 'Expired',
    'view_stocks' => 'View Stocks',
    'expiring_in_1_week' => 'Expiring in a week',
    'expiring_in_15_days' => 'Expiring in 15 days',
    'expiring_in_1_month' => 'Expiring in a month',
    'expiring_in_3_months' => 'Expiring in 3 months',
    'expiring_in_6_months' => 'Expiring in 6 months',
    'expiring_in_1_year' => 'Expiring in a year',
    'profit_loss' => 'Profit / Loss Report',
    'opening_stock' => 'Opening Stock',
    'closing_stock' => 'Closing stock',
    'total_expense' => 'Total Expense',
    'net_profit' => 'Net Profit',
    'total_stock_adjustment' => 'Total Stock Adjustment',
    'total_stock_recovered' => 'Total Stock Recovered',
];
