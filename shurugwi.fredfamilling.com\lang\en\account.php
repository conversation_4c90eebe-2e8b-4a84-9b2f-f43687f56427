<?php

return [
    'account' => 'Account',
    'manage_your_account' => 'Manage your account',
    'all_accounts' => 'All accounts',
    'account_number' => 'Account Number',
    'add_account' => 'Add Account',
    'account_created_success' => 'Account created successfully',
    'account_updated_success' => 'Account updated successfully',
    'edit_account' => 'Edit Account',
    'account_closed_success' => 'Account closed successfully',
    'closed' => 'Closed',
    'list_accounts' => 'List Accounts',
    'opening_balance' => 'Opening Balance',
    'account_book' => 'Account Book',
    'credit' => 'Credit',
    'debit' => 'Debit',
    'account_name' => 'Account Name',
    'transaction_type' => 'Transaction Type',
    'fund_transfer' => 'Fund Transfer',
    'selected_account' => 'Selected Account',
    'transfer_to' => 'Transfer To',
    'fund_transfered_success' => 'Fund transfered successfully',
    'deposit' => 'Deposit',
    'deposited_successfully' => 'Amount deposited successfully',
    'from' => 'From',
    'to' => 'To',
    'balance_sheet' => 'Balance Sheet',
    'liability' => 'Liability',
    'assets' => 'Assets',
    'supplier_due' => 'Supplier Due',
    'customer_due' => 'Customer Due',
    'account_balances' => 'Account Balances',
    'total_assets' => 'Total Assets',
    'total_liability' => 'Total Liability',
    'trial_balance' => 'Trial Balance',
    'payments_not_linked_with_account' => 'Total <b>:payments</b> payments not linked with any account.',
    'saving_current' => 'Saving/Current',
    'capital' => 'Capital',
    'not_applicable' => 'Not Applicable',
    'account_type' => 'Account Type',
    'accounts' => 'Accounts',
    'capital_accounts' => 'Capital Accounts',
    'deposit_from' => 'Deposit From',
    'no_capital_account_created' => 'No active Capital Account exists.',
    'payment_account_report' => 'Payment Account Report',
    'invoice_ref_no' => 'Invoice No./Ref. No.',
    'payment_ref_no' => 'Payment Ref No.',
    'payment_for' => 'Payment For',
    'link_account' => 'Link Account',
    'account_linked_success' => 'Account linked successfully',
    'view_details' => 'View Details',
];
