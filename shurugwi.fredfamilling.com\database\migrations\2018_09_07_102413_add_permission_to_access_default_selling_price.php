<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //Create a new permission related to the created selling price group
        Permission::create(['name' => 'access_default_selling_price']);

        $roles = Role::get();
        foreach ($roles as $role) {
            $role->givePermissionTo('access_default_selling_price');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
