<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Role Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for Brand CRUD operations.
    |
    */

    'add_role' => 'Add Role',
    'edit_role' => 'Edit Role',
    'user' => 'User',
    'supplier' => 'Supplier',
    'customer' => 'Customer',
    'purchase' => 'Purchase & Stock Adjustment',
    'report' => 'Report',

    'user.view' => 'View user',
    'user.create' => 'Add user',
    'user.update' => 'Edit user',
    'user.delete' => 'Delete user',

    'supplier.view' => 'View supplier',
    'supplier.create' => 'Add supplier',
    'supplier.update' => 'Edit supplier',
    'supplier.delete' => 'Delete supplier',

    'customer.view' => 'View customer',
    'customer.create' => 'Add customer',
    'customer.update' => 'Edit customer',
    'customer.delete' => 'Delete customer',

    'product.view' => 'View product',
    'product.create' => 'Add product',
    'product.update' => 'Edit product',
    'product.delete' => 'Delete product',

    'purchase.view' => 'View purchase & Stock Adjustment',
    'purchase.create' => 'Add purchase & Stock Adjustment',
    'purchase.update' => 'Edit purchase & Stock Adjustment',
    'purchase.delete' => 'Delete purchase & Stock Adjustment',

    'sell.view' => 'View POS sell',
    'sell.create' => 'Add POS sell',
    'sell.update' => 'Edit POS sell',
    'sell.delete' => 'Delete POS sell',

    'purchase_n_sell_report.view' => 'View purchase & sell report',
    'contacts_report.view' => 'View Supplier & Customer report',
    'stock_report.view' => 'View stock report, stock adjustment report & stock expiry report',
    'tax_report.view' => 'View Tax report',
    'trending_product_report.view' => 'View trending product report',
    'register_report.view' => 'View register report',
    'sales_representative.view' => 'View sales representative report',
    'expense_report.view' => 'View expense report',

    'business_settings.access' => 'Access business settings',
    'barcode_settings.access' => 'Access barcode settings',
    'invoice_settings.access' => 'Access invoice settings',

    'brand.view' => 'View brand',
    'brand.create' => 'Add brand',
    'brand.update' => 'Edit brand',
    'brand.delete' => 'Delete brand',

    'tax_rate.view' => 'View tax rate',
    'tax_rate.create' => 'Add tax rate',
    'tax_rate.update' => 'Edit tax rate',
    'tax_rate.delete' => 'Delete tax rate',

    'unit.view' => 'View unit',
    'unit.create' => 'Add unit',
    'unit.update' => 'Edit unit',
    'unit.delete' => 'Delete unit',

    'category.view' => 'View category',
    'category.create' => 'Add category',
    'category.update' => 'Edit category',
    'category.delete' => 'Delete category',
    'select_all' => 'Select all',
    'settings' => 'Settings',
    'brand' => 'Brand',
    'tax_rate' => 'Tax rate',
    'unit' => 'Unit',
    'access_locations' => 'Access locations',
    'all_locations' => 'All Locations',
    'dashboard' => 'Home',
    'dashboard.data' => 'View Home data',
    'profit_loss_report.view' => 'View profit/loss report',
    'direct_sell.access' => 'Access sell',
];
