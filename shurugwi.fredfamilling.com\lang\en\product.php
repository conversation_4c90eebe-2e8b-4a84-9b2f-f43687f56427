<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Product Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for Product related operations.
    |
    */

    'exc_of_tax' => 'Exc. tax',
    'inc_of_tax' => 'Inc. tax',
    'profit_percent' => 'x Margin(%)',
    'add_new_product' => 'Add new product',
    'add_product_for_labels' => 'Add products to generate Labels',
    'product_type' => 'Product Type',
    'category' => 'Category',
    'sub_category' => 'Sub category',
    'unit' => 'Unit',
    'brand' => 'Brand',
    'tax' => 'Tax',
    'sku' => 'SKU',
    'alert_quantity' => 'Alert quantity',
    'product_name' => 'Product Name',
    'auto_generate' => 'Auto generate',
    'manage_stock' => 'Manage Stock?',
    'enable_stock_help' => 'Enable stock management at product level',
    'barcode_type' => 'Barcode Type',
    'applicable_tax' => 'Applicable Tax',
    'selling_price_tax_type' => 'Selling Price Tax Type',
    'inclusive' => 'Inclusive',
    'exclusive' => 'Exclusive',
    'edit_product' => 'Edit Product',
    'default_purchase_price' => 'Default Purchase Price',
    'default_selling_price' => 'Default Selling Price',
    'value' => 'Value',
    'variation_name' => 'Variation Name',
    'variation_values' => 'Variation Values',
    'use_template' => 'Use Template',
    'variation_values' => 'Variation Values',
    'add_variation' => 'Add Variation',
    'product_added_success' => 'Product added successfully',
    'product_updated_success' => 'Product updated successfully',
    'enable_product_expiry' => 'Enable Product Expiry',
    'expiry_period' => 'Expiry Period',
    'expires_in' => 'Expires in',
    'not_applicable' => 'Not Applicable',
    'months' => 'Months',
    'days' => 'Days',
    'mfg_date' => 'MFG Date',
    'exp_date' => 'EXP Date',
    'view_product' => 'View Product',
    'add_product' => 'Add Product',
    'variations' => 'Variations',
    'import_products' => 'Import Products',
    'file_to_import' => 'File To Import',
    'file_imported_successfully' => 'File imported successfully',
];
