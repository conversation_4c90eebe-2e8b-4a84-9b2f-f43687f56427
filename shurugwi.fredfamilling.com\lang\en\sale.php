<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Brand Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for Brand CRUD operations.
    |
    */

    'sale' => 'Sell',
    'sells' => 'Sales',
    'list_sale' => 'List Sales',
    'add_sale' => 'Add Sale',
    'pos_sale' => 'POS',
    'draft_added' => 'Draft added successfully',
    'invoice_added' => 'Invoice added successfully',
    'item' => 'Items',
    'total' => 'Total',
    'order_tax' => 'Order Tax',
    'discount' => 'Discount',
    'total_payable' => 'Total Payable',
    'cancel' => 'Cancel',
    'draft' => 'Draft',
    'finalize' => 'Pay & Checkout',
    'express_finalize' => 'Express <br/>Checkout',
    'product' => 'Product',
    'products' => 'Products',
    'unit_price' => 'Unit Price',
    'qty' => 'Quantity',
    'subtotal' => 'Subtotal',
    'pos_sale_added' => 'Sale added successfully',
    'price_inc_tax' => 'Price inc. tax',
    'tax' => 'Tax',
    'edit_discount' => 'Edit Discount',
    'edit_order_tax' => 'Edit Order Tax',
    'discount_type' => 'Discount Type',
    'discount_amount' => 'Discount Amount',
    'no_recent_transactions' => 'No Recent Transactions',
    'final' => 'Final',
    'invoice_no' => 'Invoice No.',
    'customer_name' => 'Customer name',
    'payment_status' => 'Payment Status',
    'status' => 'Status',
    'total_amount' => 'Total amount',
    'total_paid' => 'Total paid',
    'total_remaining' => 'Total remaining',
    'payment_info' => 'Payment info',
    'drafts' => 'Drafts',
    'all_drafts' => 'All drafts',
    'sell_details' => 'Sell Details',
    'payments' => 'Payments',
    'amount' => 'Amount',
    'payment_mode' => 'Payment mode',
    'payment_note' => 'Payment note',
    'sell_note' => 'Sell note',
    'staff_note' => 'Staff note',
    'draft_updated' => 'Draft updated successfully',
    'pos_sale_updated' => 'Sale updated successfully',
    'location' => 'Location',
    'add_payment_row' => 'Add Payment Row',
    'finalize_payment' => 'Finalize Payment',
    'sale_date' => 'Sale Date',
    'list_pos' => 'List POS',
    'edit_sale' => 'Edit Sale',
    'shipping' => 'Shipping',
    'shipping_details' => 'Shipping Details',
    'shipping_charges' => 'Shipping Charges',
];
